pfUI_locale["deDE"] = {}

pfUI_locale["deDE"]["class"] = {
  ["<PERSON><PERSON><PERSON>"] = "WARLOCK",
  ["<PERSON>rie<PERSON>"] = "WARRIOR",
  ["<PERSON>ä<PERSON>"] = "HUNTER",
  ["Magier"] = "MAGE",
  ["Priester"] = "PRIEST",
  ["Druide"] = "DRUID",
  ["Paladin"] = "PALADIN",
  ["<PERSON>hamane"] = "SHAMAN",
  ["<PERSON>hur<PERSON>"] = "ROGUE",
}

pfUI_locale["deDE"]["healduration"] = {
  ["Rejuvenation"] = "Erhöht die Dauer Eures Zaubers 'Verjüngung' um 3 Sek.",
  ["Renew"] = "Erhöht die Wirkungsdauer von 'Erneuerung' um 3 Sek.",
}

pfUI_locale["deDE"]["bagtypes"] = {
  ["Köcher"] = "QUIVER",
  ["Seelentasche"] = "SOULBAG",
  ["Behälter"] = "DEFAULT",
}

pfUI_locale["deDE"]["itemtypes"] = {
  ["INVTYPE_WAND"] = "Zauberstab",
  ["INVTYPE_THROWN"] = "Wurfwaffe",
  ["INVTYPE_GUN"] = "Schusswaffe",
  ["INVTYPE_CROSSBOW"] = "Armbrust",
  ["INVTYPE_PROJECTILE"] = "Projektil",
}

pfUI_locale["deDE"]["hunterpaging"] = {
  ["MELEE"] = "Zurechtstutzen",
  ["RANGED"] = "Arkaner Schuss",
}

pfUI_locale["deDE"]["customcast"] = {
  ["AIMEDSHOT"] = "Gezielter Schuss",
  ["MULTISHOT"] = "Mehrfach-Schuss",
}

pfUI_locale["deDE"]["critters"] = {
  'Otter',
  'Käfer',
  'Turmfledermaus',
  'Gallkröte',
  'Schwarze Ratte',
  'Brauner Präriehund',
  'Eingesperrter Hase',
  'Eingesperrtes Schaf',
  'Eingesperrtes Eichhörnchen',
  'Eingesperrte Kröte',
  'Katze',
  'Huhn',
  'Cleo',
  'Kernratte',
  'Kuh',
  'Verwandelte Kuh',
  'Geheiltes Reh',
  'Geheilte Gazelle',
  'Untergrundratte',
  'Reh',
  'Hund',
  'Effsee',
  'Umzauberte Untergrundratte',
  'Reißzahn',
  'Rehkitz',
  'Feuerkäfer',
  'Flauschie',
  'Kleiner Frosch',
  'Gazelle',
  'Hase',
  'Pferd',
  'Titanische Kröte',
  'Infiziertes Reh',
  'Infiziertes Eichhörnchen',
  'Dschungelkröte',
  'Krakles Thermometer',
  'Lady',
  'Larve',
  'Lavakrebs',
  'Made',
  'Wasserschlange',
  'Maus',
  'Mr. Bigglesworth',
  'Nibbles',
  'Noarm',
  'Graumähne',
  'Papagei',
  'Verwandeltes Schwein',
  'Piratenschatz Auslöser Meute',
  'Verseuchtes Insekt',
  'Verseuchte Made',
  'Verseuchte Ratte',
  'Pestlandtermite',
  'Verwandeltes Huhn',
  'Verwandelte Ratte',
  'Präriehund',
  'Kaninchen',
  'Widder',
  'Ratte',
  'Reitwidder',
  'Schabe',
  'Salome',
  'Fischschwarm',
  'Skorpid',
  'Verwandeltes Schaf',
  'Schaf',
  'Irrwisch der Shen\'dralar',
  'Kränkliches Reh',
  'Kränkliche Gazelle',
  'Natter',
  'Spinne',
  'Spike',
  'Eichhörnchen',
  'Schwein',
  'Besudelte Kakerlake',
  'Besudelte Ratte',
  'Kröte',
  'Transporterfehlfunktion',
  'Verwandelte Schildkröte',
  'Schleicherpfote',
  'Stimme von Elune',
  'Waypoint',
  'Irrwisch',
}

pfUI_locale["deDE"]["dyndebuffs"] = {
  ["Rupture"] = "Blutung",
  ["Kidney Shot"] = "Nierenhieb",
  ["Rend"] = "Verwunden",
  ["Shadow Word: Pain"] = "Schattenwort: Schmerz",
  ["Demoralizing Shout"] = "Demoralisierungsruf",
  ["Frostbolt"] = "Frostblitz",
  ["Gouge"] = "Solarplexus",
}

pfUI_locale["deDE"]["judgements"] = {
  ["Richturteil der Gerechtigkeit"] = true,
  ["Richturteil des Lichts"] = true,
  ["Richturteil der Weisheit"] = true,
  ["Richturteil des Kreuzfahrers"] = true,
}

pfUI_locale["deDE"]["interrupts"] = {
  ["Schildhieb"] = true,
  ["Zuschlagen"] = true,
  ["Tritt"] = true,
  ["Erdschock"] = true,
  ["Kriegsdonner"] = true,
  ["Erschütternder Schlag"] = true,
  ["Sturmangriffsbetäubung"] = true,
  ["Betäubung abfangen"] = true,
  ["Hammer der Gerechtigkeit"] = true,
  ["Fieser Trick"] = true,
  ["Solarplexus"] = true,
  ["Nierenhieb"] = true,
  ["Stille"] = true,
  ["Gegenzauber"] = true,
  ["Gegenzauber - zum Schweigen gebracht"] = true,
  ["Hieb"] = true,
  ["Furcht"] = true,
  ["Schreckensgeheul"] = true,
  ["Psychischer Schrei"] = true,
  ["Drohruf"] = true,
  ["Sternenfeuerbetäubung"] = true,
  ["Rachebetäubung"] = true,
  ["Verbesserter erschütternder Schuss"] = true,
  ["Einschlag"] = true,
  ["Feuerschwall"] = true,
  ["Blackout"] = true,
  ["Betäuben"] = true,
  ["Streitkolbenbetäubung"] = true,
  ["Erderschütterer"] = true,
  ["Buße"] = true,
  ["Streuschuss"] = true,
  ["Blenden"] = true,
  ["Winterschlaf"] = true,
  ["Stich des Flügeldrachen"] = true,
  ["Raue Kupferbombe"] = true,
  ["Große Kupferbombe"] = true,
  ["Kleine Bronzebombe"] = true,
  ["Große Bronzebombe"] = true,
  ["Große Eisenbombe"] = true,
  ["Mithrilschrapnellbombe"] = true,
  ["Hochexplosive Bombe"] = true,
  ["Dunkeleisenbombe"] = true,
  ["Eisengranate"] = true,
  ["M73 Schrapnellgranate"] = true,
  ["Thoriumgranate"] = true,
  ["Goblin-Mörser"] = true,
}

pfUI_locale["deDE"]["resurrections"] = {
  ["Auferstehung"] = true,
  ["Wiedergeburt"] = true,
  ["Erlösung"] = true,
  ["Geist der Ahnen"] = true,
}

pfUI_locale["deDE"]["spells"] = {
  ['Dämmerungstrickfalle']={t=1500,icon='Temp'},
  ['Aasskarabäus beschwören']={t=2000,icon='Spell_Shadow_CarrionSwarm'},
  ['Abbau-Schwarm']={t=3000,icon='Spell_Holy_Dizzy'},
  ['Ablagerungen sammeln']={t=4000,icon='Temp'},
  ['Abschreckknurren']={t=1000,icon='Ability_Racial_Cannibalize'},
  ['Abtauchen Bild']={t=1500,icon='Spell_Fire_Volcano'},
  ['Abzeichen entfernen']={t=1000,icon='Temp'},
  ['Adlereule beschwören']={t=1000,icon='Ability_Seal'},
  ['Alarm-o-bot beschwören']={t=250,icon='INV_Gizmo_08'},
  ['Albinonatter beschwören']={t=1000,icon='Ability_Seal'},
  ['Albinoschnappkiefer beschwören']={t=1000,icon='Ability_Seal'},
  ['Alptraumgegenstand werfen']={t=2000,icon='Spell_Fire_SelfDestruct'},
  ['Altar der Beschwörung herbeizaubern']={t=10000,icon='Temp'},
  ['Altar der Gezeiten-Teleporter']={t=2000,icon='Temp'},
  ['Ancona-Huhn beschwören']={t=1000,icon='Ability_Seal'},
  ['Anhänger platzieren']={t=5000,icon='Temp'},
  ['Annalen von Darrowshire\' verzaubern']={t=3500,icon='Temp'},
  ['Antimagieschild']={t=2000,icon='Spell_Shadow_AntiMagicShell'},
  ['Anvilmar-Teleporter']={t=2000,icon='Temp'},
  ['Aquadynamische Fischlinse']={t=5000,icon='INV_Misc_Spyglass_01'},
  ['Aquadynamischer Fischanlocker']={t=5000,icon='INV_Misc_Orb_03'},
  ['Aquementas beschwören']={t=2000,icon='Temp'},
  ['Ar\'lia beschwören']={t=2500,icon='Spell_Nature_GroundingTotem'},
  ['Arbeiterverkleidung']={t=3000,icon='Ability_Rogue_Disguise'},
  ['Archaedas wecken\'-Bild (DND)']={t=1500,icon='Spell_Nature_Earthquake'},
  ['Arglist des Raptors']={t=3000,icon='INV_Misc_MonsterClaw_02'},
  ['Arkanblitz']={t=1000,icon='Spell_Arcane_StarFire'},
  ['Arkane Bombe']={t=1500,icon='Spell_Holy_Silence'},
  ['Arkane Explosion']={t=1500,icon='Spell_Nature_WispSplode'},
  ['Arkaner Geist II']={t=1000,icon='Spell_Holy_MagicalSentry'},
  ['Arkaner Geist III']={t=1000,icon='Spell_Holy_MagicalSentry'},
  ['Arkaner Geist IV']={t=1000,icon='Spell_Holy_MagicalSentry'},
  ['Arkaner Geist V']={t=1000,icon='Spell_Holy_MagicalSentry'},
  ['Arkanitboje platzieren']={t=2000,icon='Temp'},
  ['Arkanitdietrich']={t=5000,icon='Temp'},
  ['Arkanschwäche']={t=5000,icon='INV_Misc_QirajiCrystal_01'},
  ['Arktischer Wolf']={t=3000,icon='Ability_Mount_WhiteDireWolf'},
  ['Armbrust abschießen']={t=1000,icon='Ability_Marksmanship'},
  ['Armors Pfeil']={t=1000,icon='Temp'},
  ['Arugals Gabe']={t=2500,icon='Spell_Shadow_ChillTouch'},
  ['Arygos Rache']={t=2000,icon='Temp'},
  ['Ashcrombes Teleporter']={t=2000,icon='Spell_Fire_SelfDestruct'},
  ['Ashcrombes Öffner']={t=4000,icon='Spell_Nature_MoonKey'},
  ['Aspekt von Neptulon']={t=1000,icon='Temp'},
  ['Astraler Rückruf']={t=10000,icon='Spell_Nature_AstralRecal'},
  ['Atal\'ai - Altar - Licht - Bild (DND)']={t=1000,icon='Temp'},
  ['Atem']={t=5000,icon='Spell_Fire_Fire'},
  ['Atombomben-Attrappe']={t=2000,icon='Temp'},
  ['Attrappe NSC Beschwören']={t=20000,icon='Spell_Shadow_UnsummonBuilding'},
  ['Attrappenschuss']={t=2000,icon='Ability_Marksmanship'},
  ['Auferstandenen Lakaien beschwören']={t=2000,icon='Spell_Shadow_RaiseDead'},
  ['Auferstehung']={t=10000,icon='Spell_Holy_Resurrection'},
  ['Aufgeladener Arkanblitz']={t=7000,icon='Spell_Arcane_StarFire'},
  ['Aufmotzen']={t=7000,icon='INV_Gizmo_02'},
  ['Auge von Immol\'thar']={t=2000,icon='Spell_Shadow_AntiMagicShell'},
  ['Auge von Kilrogg']={t=5000,icon='Spell_Shadow_EvilEye'},
  ['Auge von Yesmur (PT)']={t=2000,icon='Temp'},
  ['Augen des Wildtiers']={t=2000,icon='Ability_EyeOfTheOwl'},
  ['Augenstrahl']={t=2000,icon='Spell_Nature_CallStorm'},
  ['Axt werfen']={t=1000,icon='INV_Axe_08'},
  ['Aynashas Pfeil']={t=500,icon='Temp'},
  ['Azurblauen Welpling beschwören']={t=1000,icon='Ability_Seal'},
  ['Babyhai beschwören']={t=1000,icon='Ability_Seal'},
  ['Bandnatter beschwören']={t=1000,icon='Ability_Seal'},
  ['Bannen']={t=1000,icon='Spell_Holy_DispelMagic'},
  ['Bannfluch rufen']={t=1000,icon='Temp'},
  ['Bansheefluch']={t=2000,icon='Spell_Nature_Drowsy'},
  ['Bartür öffnen']={t=5000,icon='Temp'},
  ['Baumfrosch beschwören']={t=1000,icon='Ability_Seal'},
  ['Bedrohlicher Blick']={t=2000,icon='Spell_Shadow_Charm'},
  ['Bedrohliches Knurren']={t=1000,icon='Ability_Racial_Cannibalize'},
  ['Beerens Echte']={t=1000,icon='INV_ValentinesChocolate02'},
  ['Beherrschung']={t=1000,icon='Spell_Shadow_ShadowWordDominate'},
  ['Benebelnder Schmerz']={t=1500,icon='Spell_Nature_CorrosiveBreath'},
  ['Benedicts Truhe öffnen']={t=5000,icon='Temp'},
  ['Bergbau']={t=3200,icon='Trade_Mining'},
  ['Berührung des Todes']={t=3000,icon='Spell_Shadow_UnsummonBuilding'},
  ['Berührung von Rabenklaue']={t=1500,icon='Spell_Shadow_Requiem'},
  ['Beschworener Urok']={t=1000,icon='Temp'},
  ['Beschwören']={t=1000,icon='Spell_Arcane_Blink'},
  ['Betrunkene Boxencrew']={t=2000,icon='Temp'},
  ['Betäubungsbombe']={t=2000,icon='Spell_Fire_SelfDestruct'},
  ['Betäubungsbombenangriff']={t=2000,icon='Spell_Fire_SelfDestruct'},
  ['Beutetruhe öffnen']={t=5000,icon='Temp'},
  ['Biegsamer Schienbeinknochen']={t=1500,icon='Temp'},
  ['Blackhand-Schreckenswirker beschwören']={t=5000,icon='Spell_Nature_Purge'},
  ['Blackhand-Veteranen beschwören']={t=5000,icon='Spell_Nature_Purge'},
  ['Blaue Qirajipanzerdrohne beschwören']={t=3000,icon='INV_Misc_QirajiCrystal_04'},
  ['Blauen Flitzer beschwören']={t=1000,icon='Ability_Seal'},
  ['Blauer Roboschreiter']={t=3000,icon='Ability_Mount_MechaStrider'},
  ['Blauer Widder']={t=3000,icon='Ability_Mount_MountainRam'},
  ['Blaues Skelettpferd']={t=3000,icon='Ability_Mount_Undeadhorse'},
  ['Blitzheilung']={t=1500,icon='Spell_Holy_FlashHeal'},
  ['Blitzschlag']={t=3000,icon='Spell_Nature_Lightning'},
  ['Blitzschlagatem']={t=2000,icon='Spell_Nature_Lightning'},
  ['Blitzschlagtotem']={t=500,icon='Spell_Nature_Lightning'},
  ['Blitzschlagwolke']={t=3000,icon='Spell_Nature_CallStorm'},
  ['Blizzard']={t=2000,icon='Spell_Frost_IceStorm'},
  ['Blue Dragon Transform DND']={t=1000,icon='Temp'},
  ['Blutblütenminiplagen beschwören']={t=2000,icon='Spell_Shadow_DarkSummoning'},
  ['Blutfluch']={t=2000,icon='Spell_Shadow_RitualOfSacrifice'},
  ['Blutgeheul']={t=1000,icon='Spell_Shadow_LifeDrain'},
  ['Blutpapagei beschwören']={t=1000,icon='Ability_Seal'},
  ['Bly\'s Band\'s Escape']={t=10000,icon='INV_Misc_Rune_01'},
  ['Bogenschuss']={t=1000,icon='Ability_Marksmanship'},
  ['Bombardieren']={t=3000,icon='Ability_GolemStormBolt'},
  ['Bombardieren II']={t=3000,icon='Ability_GolemStormBolt'},
  ['Bombay beschwören']={t=1000,icon='Ability_Seal'},
  ['Bombe']={t=2000,icon='Spell_Fire_SelfDestruct'},
  ['Bombe beschwören']={t=1000,icon='Ability_Seal'},
  ['Brandpulver']={t=5000,icon='Temp'},
  ['Braune Natter beschwören']={t=1000,icon='Ability_Seal'},
  ['Brauner Kodo']={t=3000,icon='Ability_Mount_Kodo_03'},
  ['Brauner Widder']={t=3000,icon='Ability_Mount_MountainRam'},
  ['Brauner Wolf']={t=3000,icon='Ability_Mount_BlackDireWolf'},
  ['Braunes Pferd']={t=3000,icon='Ability_Mount_RidingHorse'},
  ['Braunes Skelettpferd']={t=3000,icon='Ability_Mount_Undeadhorse'},
  ['Brennende Winde']={t=1000,icon='Spell_Nature_Cyclone'},
  ['Brennenden Verbannten verbannen']={t=1000,icon='Spell_Shadow_LifeDrain'},
  ['Brennstoff ins Freudenfeuer werfen']={t=500,icon='Temp'},
  ['Briefkasten von Stratholme öffnen']={t=5000,icon='Temp'},
  ['Bronzewelpling beschwören']={t=1000,icon='Ability_Seal'},
  ['Brunnen des Lichts']={t=1500,icon='Spell_Holy_SummonLightwell'},
  ['Brust erfüllen - Absorbieren']={t=5000,icon='Spell_Holy_GreaterHeal'},
  ['Brust erfüllen - Geringe Willenskraft']={t=5000,icon='Spell_Holy_GreaterHeal'},
  ['Brust erfüllen - Geringes Absorbieren']={t=5000,icon='Spell_Holy_GreaterHeal'},
  ['Brust erfüllen - Schwache Willenskraft']={t=5000,icon='Spell_Holy_GreaterHeal'},
  ['Brut Nozdormus Fraktion +1000']={t=1000,icon='Temp'},
  ['Brut identifizieren']={t=10000,icon='Spell_Lightning_LightningBolt01'},
  ['Brut von Bael\'Gar beschwören']={t=4000,icon='Spell_Fire_LavaSpawn'},
  ['Brut-Herausforderung an Urok']={t=2000,icon='Temp'},
  ['Buttermilchküsschen']={t=1000,icon='INV_ValentinesChocolate01'},
  ['Bärenfalle platzieren']={t=2000,icon='Temp'},
  ['Bäumling beschwören']={t=3000,icon='Spell_Nature_ProtectionformNature'},
  ['Böser Blick']={t=1500,icon='Spell_Shadow_Charm'},
  ['Böser Gott\'-Gegenzauber']={t=300000,icon='Temp'},
  ['CHUs QUESTZAUBER']={t=4000,icon='Spell_Shadow_LifeDrain'},
  ['Chromatisches Reittier']={t=3000,icon='INV_Misc_Head_Dragon_Black'},
  ['Cookies Kochkunst']={t=2000,icon='Spell_Holy_Heal'},
  ['Cornish Rex beschwören']={t=1000,icon='Ability_Seal'},
  ['CreatureSpecial']={t=2000,icon='Temp'},
  ['Cyclonian beschwören']={t=10000,icon='Spell_Nature_EarthBind'},
  ['Dagun beschwören']={t=5000,icon='Temp'},
  ['Dauerhafte Tyrion-Illusion']={t=3000,icon='Ability_Rogue_Disguise'},
  ['Dauerhafte \'Bischöfin Tyriona\'-Illusion']={t=3000,icon='Ability_Rogue_Disguise'},
  ['Defiasverkleidung']={t=3000,icon='Ability_Rogue_Disguise'},
  ['Defibrillation']={t=4000,icon='Spell_Nature_Purge'},
  ['Delete Me']={t=4000,icon='INV_Scroll_02'},
  ['Den Köder anwenden']={t=4000,icon='Temp'},
  ['Den Seelenschinder wecken']={t=5000,icon='Temp'},
  ['Der Große']={t=3000,icon='Spell_Fire_SelfDestruct'},
  ['Detonation']={t=5000,icon='Spell_Fire_SelfDestruct'},
  ['Diablo beschwören']={t=1000,icon='Ability_Seal'},
  ['Dichtes Dynamit']={t=1000,icon='Spell_Fire_SelfDestruct'},
  ['Die Hunde loslassen']={t=2000,icon='Temp'},
  ['Die Uralten herbeirufen']={t=7000,icon='Temp'},
  ['Diener von Morganth']={t=2500,icon='Spell_Totem_WardOfDraining'},
  ['Dimensionsportal']={t=2000,icon='Temp'},
  ['Donnernden Verbannten verbannen']={t=1000,icon='Spell_Shadow_LifeDrain'},
  ['Donnerschlag beschwören']={t=10000,icon='Temp'},
  ['Dornenfluch']={t=2000,icon='Spell_Shadow_AntiShadow'},
  ['Dr. Wackel beschwören']={t=1000,icon='Ability_Seal'},
  ['Draco-Incarcinatrix 900']={t=2000,icon='Temp'},
  ['Dreschadongerippe platzieren']={t=2500,icon='INV_Misc_Bowl_01'},
  ['Druidenschlummer']={t=2500,icon='Spell_Nature_Sleep'},
  ['Dröhnendes Gebrüll']={t=1500,icon='Spell_Fire_Fire'},
  ['Dunkeleisenbier werfen']={t=500,icon='Temp'},
  ['Dunkeleisenbombe']={t=1000,icon='Spell_Fire_SelfDestruct'},
  ['Dunkeleisenlandmine']={t=1000,icon='Spell_Shadow_Metamorphosis'},
  ['Dunkeleisenzwerg-Verkleidung']={t=3000,icon='Ability_Rogue_Disguise'},
  ['Dunkelhain-Teleporter']={t=2000,icon='Temp'},
  ['Dunkelwelpling beschwören']={t=1000,icon='Ability_Seal'},
  ['Dunkle Besserung']={t=3500,icon='Spell_Shadow_ChillTouch'},
  ['Dunkle Versuchung']={t=1000,icon='INV_ValentinesChocolate04'},
  ['Dunklen Kasten öffnen']={t=5000,icon='Temp'},
  ['Dunkler Rumsey-Rum']={t=1000,icon='INV_Drink_04'},
  ['Dunkler Schlamm']={t=5000,icon='Spell_Shadow_CreepingPlague'},
  ['Dunkles Geflüster']={t=3000,icon='Spell_Shadow_Haunting'},
  ['Dunkles Wiederherstellen']={t=2000,icon='Ability_Hunter_MendPet'},
  ['Dynamit']={t=1000,icon='Spell_Fire_SelfDestruct'},
  ['Dynamit werfen']={t=2000,icon='Spell_Fire_SelfDestruct'},
  ['Dämmerwald-Teleporter']={t=2000,icon='Temp'},
  ['Dämon der Kugel beschwören']={t=5000,icon='Temp'},
  ['Dämonenhacke']={t=5000,icon='Temp'},
  ['Dämonenportal']={t=500,icon='Spell_Arcane_TeleportOrgrimmar'},
  ['Dämonensklave']={t=3000,icon='Spell_Shadow_EnslaveDemon'},
  ['EZ-Thro-Dynamit']={t=1000,icon='Spell_Fire_SelfDestruct'},
  ['Ebergeist beschwören']={t=1500,icon='Spell_Magic_PolymorphPig'},
  ['Echeyakee beschwören']={t=500,icon='Spell_Shadow_LifeDrain'},
  ['Echsenschlag']={t=2000,icon='Spell_Nature_Lightning'},
  ['Echtsilberdietrich']={t=5000,icon='Temp'},
  ['Edana Hasskralle beschwören']={t=4000,icon='Temp'},
  ['Edelstein der Schlange']={t=5000,icon='Temp'},
  ['Editor Test Spell']={t=250,icon='Temp'},
  ['Ehrenpunkte +138']={t=1000,icon='Temp'},
  ['Ehrenpunkte +228']={t=1000,icon='Temp'},
  ['Ehrenpunkte +2388']={t=1000,icon='INV_BannerPVP_02'},
  ['Ehrenpunkte +378']={t=1000,icon='Temp'},
  ['Ehrenpunkte +398']={t=1000,icon='Temp'},
  ['Ehrenpunkte +50']={t=1000,icon='Temp'},
  ['Ehrenpunkte +82']={t=1000,icon='Temp'},
  ['Ei zerstören']={t=3000,icon='INV_Misc_MonsterClaw_02'},
  ['Einfacher Gruppen-Teleporter']={t=2000,icon='Spell_Magic_LesserInvisibilty'},
  ['Einfacher Teleporter']={t=1000,icon='Spell_Magic_LesserInvisibilty'},
  ['Einfacher Teleporter - Andere']={t=1000,icon='Spell_Magic_LesserInvisibilty'},
  ['Einfaches Lagerfeuer']={t=10000,icon='Spell_Fire_Fire'},
  ['Eingestellter Dämpfer']={t=2000,icon='Spell_Holy_SearingLight'},
  ['Eingraben']={t=1000,icon='Ability_Vanish'},
  ['Einhüllende Schatten']={t=1500,icon='Spell_Shadow_LifeDrain02'},
  ['Einhüllende Winde']={t=2000,icon='Spell_Nature_Cyclone'},
  ['Einschüchterndes Knurren']={t=1000,icon='Ability_Racial_Cannibalize'},
  ['Einsperren']={t=2000,icon='Spell_Shadow_Teleport'},
  ['Einstellung anpassen']={t=2000,icon='INV_Gizmo_01'},
  ['Eisblauer Roboschreiter']={t=3000,icon='Ability_Mount_MechaStrider'},
  ['Eisblitz']={t=2000,icon='Spell_Frost_FrostBolt02'},
  ['Eisengranate']={t=1000,icon='Spell_Fire_SelfDestruct'},
  ['Eisenherz nimmt wieder Huhngestalt an']={t=1000,icon='Ability_Racial_BearForm'},
  ['Eisgrabmal']={t=1500,icon='Spell_Frost_Glacier'},
  ['Eiskalter Atem']={t=1000,icon='Spell_Frost_Wisp'},
  ['Eisketten']={t=1300,icon='Spell_Frost_ChainsOfIce'},
  ['Eiskälte']={t=4000,icon='Spell_Frost_Glacier'},
  ['Eiszapfen']={t=1500,icon='Spell_Frost_FrostBolt02'},
  ['Ekelhaften Schlammling beschwören']={t=1000,icon='Ability_Seal'},
  ['Elektrifiziertes Netz']={t=500,icon='Ability_Ensnare'},
  ['Elementarfeuer']={t=500,icon='Spell_Fire_Fireball02'},
  ['Elementarfuror']={t=1000,icon='Spell_Fire_FireArmor'},
  ['Elementarrüstung']={t=1000,icon='Spell_Frost_Frost'},
  ['Elfen-Irrwisch beschwören']={t=1000,icon='Ability_Seal'},
  ['Elfenbeinfarbener Raptor']={t=3000,icon='Ability_Mount_Raptor'},
  ['Elunes Gebet']={t=1000,icon='Spell_Holy_Resurrection'},
  ['Elunes Kerze']={t=500,icon='Temp'},
  ['Elwynn-Teleporter']={t=2000,icon='Temp'},
  ['Energieentzug']={t=1500,icon='Spell_Shadow_Cripple'},
  ['Entfesselungskünstler']={t=500,icon='Ability_Rogue_Trip'},
  ['Entkräften']={t=2000,icon='Spell_Shadow_CurseOfMannoroth'},
  ['Entzaubern']={t=3000,icon='Spell_Holy_RemoveCurse'},
  ['Erdelementar']={t=3000,icon='Ability_Temp'},
  ['Erdengriff-Totem']={t=500,icon='Spell_Nature_NatureTouchDecay'},
  ['Erkrankte Spucke']={t=3000,icon='Spell_Shadow_CreepingPlague'},
  ['Erleuchtung']={t=2000,icon='Spell_Shadow_Fumble'},
  ['Erlösung']={t=10000,icon='Spell_Holy_Resurrection'},
  ['Erneuerung']={t=2000,icon='Spell_Holy_Renew'},
  ['Erste Hilfe']={t=3000,icon='Spell_Holy_GreaterHeal'},
  ['Erz schmelzen']={t=1500,icon='Spell_Fire_SelfDestruct'},
  ['Essen läutern und platzieren']={t=5000,icon='INV_Misc_Bowl_01'},
  ['Eulengestalt']={t=5000,icon='Spell_Nature_RavenForm'},
  ['Everlook Transporter']={t=10000,icon='Spell_Fire_SelfDestruct'},
  ['Ewige Flamme löschen']={t=1000,icon='Temp'},
  ['Explodieren']={t=2000,icon='Temp'},
  ['Explodierendes Schaf']={t=2000,icon='Ability_Repair'},
  ['Explosivgeschoss']={t=1000,icon='Spell_Fire_Fireball02'},
  ['Explosivschuss']={t=1000,icon='Spell_Fire_Fireball02'},
  ['Fackel zur Dämonenbeschwörung']={t=3000,icon='Temp'},
  ['Fackelkombination']={t=5000,icon='Temp'},
  ['Fackelwurf']={t=1000,icon='Spell_Fire_Flare'},
  ['Falkeneule beschwören']={t=1000,icon='Ability_Seal'},
  ['Falkenschnabelschnappkiefer beschwören']={t=1000,icon='Ability_Seal'},
  ['Falle entschärfen']={t=2000,icon='Spell_Shadow_GrimWard'},
  ['Fanatische Klinge']={t=1000,icon='Spell_Fire_Immolation'},
  ['Faust von Shahram']={t=1000,icon='Ability_Whirlwind'},
  ['Feenfeuer']={t=2000,icon='Spell_Nature_FaerieFire'},
  ['Feenling beschwören']={t=1000,icon='Ability_Seal'},
  ['Feger']={t=1500,icon='Spell_Nature_Thorns'},
  ['Fehlgeschlagener Schattenblitz']={t=2000,icon='Spell_Shadow_ShadowBolt'},
  ['Fels werfen']={t=3000,icon='Ability_GolemStormBolt'},
  ['Fels werfen II']={t=3000,icon='Ability_GolemStormBolt'},
  ['Felsen']={t=2000,icon='Ability_Throw'},
  ['Felstrom-Auferstehung']={t=3000,icon='Spell_Totem_WardOfDraining'},
  ['Ferngesteuerten Golem beschwören']={t=3000,icon='Ability_Repair'},
  ['Fernsicht']={t=2000,icon='Spell_Nature_FarSight'},
  ['Fernsicht (PT)']={t=2000,icon='Temp'},
  ['Fernzündung']={t=1000,icon='INV_Misc_StoneTablet_04'},
  ['Feststecken']={t=10000,icon='Spell_Shadow_Teleport'},
  ['Feuerball']={t=3500,icon='Spell_Fire_FlameBolt'},
  ['Feuerballsalve']={t=3000,icon='Spell_Fire_FlameBolt'},
  ['Feuerblitz']={t=2000,icon='Spell_Fire_FireBolt'},
  ['Feuerblitz II']={t=3000,icon='Spell_Fire_FireBolt02'},
  ['Feuerblitz III']={t=3000,icon='Spell_Fire_FireBolt02'},
  ['Feuerblitz IV']={t=3000,icon='Spell_Fire_FireBolt02'},
  ['Feuerbrand']={t=2000,icon='Spell_Fire_Immolation'},
  ['Feuerelementar']={t=3000,icon='Ability_Temp'},
  ['Feuerexplosion']={t=1500,icon='Spell_Fire_FireBolt'},
  ['Feuergeröstetes Brötchen']={t=1000,icon='INV_Misc_Food_11'},
  ['Feuerregen']={t=3000,icon='Spell_Shadow_RainOfFire'},
  ['Feuerschild']={t=1000,icon='Spell_Fire_Immolation'},
  ['Feuerschild II']={t=1000,icon='Spell_Fire_Immolation'},
  ['Feuerschild III']={t=1000,icon='Spell_Fire_Immolation'},
  ['Feuerschild IV']={t=1000,icon='Spell_Fire_Immolation'},
  ['Feuerschwäche']={t=5000,icon='INV_Misc_QirajiCrystal_02'},
  ['Feuersturm']={t=2000,icon='Spell_Fire_SelfDestruct'},
  ['Feuerwerk']={t=500,icon='Temp'},
  ['Feurige Lohe']={t=3000,icon='Spell_Fire_SelfDestruct'},
  ['Fieberhafte Erschöpfung']={t=3000,icon='Spell_Nature_NullifyDisease'},
  ['Fieberseuche']={t=4500,icon='Spell_Nature_NullifyDisease'},
  ['Flachkopfschnappkiefer beschwören']={t=1000,icon='Ability_Seal'},
  ['Flagge aufstellen']={t=2300,icon='Temp'},
  ['Flammen der Vergeltung']={t=3000,icon='Temp'},
  ['Flammen des Chaos']={t=1000,icon='Spell_Fire_WindsofWoe'},
  ['Flammen verstärken']={t=1000,icon='Spell_Fire_Fireball'},
  ['Flammen von Shahram']={t=1000,icon='Spell_Fire_SelfDestruct'},
  ['Flammenatem']={t=1700,icon='Spell_Fire_Fire'},
  ['Flammenexplosion']={t=3000,icon='Spell_Fire_SelfDestruct'},
  ['Flammenkanone']={t=1500,icon='Spell_Fire_FlameBolt'},
  ['Flammenknall']={t=2500,icon='Spell_Fire_Fire'},
  ['Flammenpeitsche']={t=1000,icon='Spell_Fire_Fireball'},
  ['Flammenpuffer']={t=2200,icon='Spell_Fire_Fireball'},
  ['Flammenschauer']={t=1700,icon='Spell_Fire_Fire'},
  ['Flammenspeien']={t=3000,icon='Spell_Fire_FlameBolt'},
  ['Flammenstachel']={t=3000,icon='Spell_Fire_SelfDestruct'},
  ['Flammenstoß']={t=3000,icon='Spell_Fire_SelfDestruct'},
  ['Fleisch entzünden']={t=2000,icon='Spell_Fire_Fire'},
  ['Fleischfressender Wurm']={t=5000,icon='INV_Misc_Orb_03'},
  ['Flinky beschwören']={t=1000,icon='Ability_Seal'},
  ['Fluch der Feuerbrand']={t=2000,icon='Ability_Creature_Cursed_03'},
  ['Fluch der Heilung']={t=1000,icon='Spell_Shadow_AntiShadow'},
  ['Fluch der Schwäche']={t=1000,icon='Spell_Shadow_CurseOfMannoroth'},
  ['Fluch der Seuchenratte']={t=1500,icon='Spell_Shadow_UnholyFrenzy'},
  ['Fluch der Stämme']={t=2000,icon='Spell_Shadow_CurseOfMannoroth'},
  ['Fluch der Totenwaldfelle']={t=2000,icon='Spell_Shadow_GatherShadows'},
  ['Fluch des Dunkelmeisters']={t=2000,icon='Spell_Shadow_AntiShadow'},
  ['Fluch des gefallenen Magram']={t=2000,icon='Spell_Shadow_UnholyFrenzy'},
  ['Fluch von Hakkar']={t=2500,icon='Spell_Shadow_GatherShadows'},
  ['Fluch von Shahram']={t=1000,icon='Spell_Magic_LesserInvisibilty'},
  ['Flächenbrand']={t=3000,icon='Spell_Fire_SelfDestruct'},
  ['Flüchtige Infektion']={t=2000,icon='Spell_Holy_HarmUndeadAura'},
  ['Flügelstoß']={t=1000,icon='INV_Misc_MonsterScales_14'},
  ['Flüssiges Metall']={t=2000,icon='Spell_Fire_Fireball'},
  ['Fokussieren']={t=5000,icon='Temp'},
  ['Friedhof-Teleporter']={t=2000,icon='Temp'},
  ['Frostatem']={t=250,icon='Spell_Frost_FrostNova'},
  ['Frostbeulen']={t=2000,icon='Spell_Frost_ChillingBlast'},
  ['Frostblitz']={t=3000,icon='Spell_Frost_FrostBolt02'},
  ['Frostblitz-Salve']={t=2000,icon='Spell_Frost_FrostBolt02'},
  ['Frostschwäche']={t=5000,icon='INV_Misc_QirajiCrystal_04'},
  ['Frostsäbler']={t=3000,icon='Ability_Mount_WhiteTiger'},
  ['Frostwidder']={t=3000,icon='Ability_Mount_MountainRam'},
  ['Frostwiderstand']={t=1000,icon='Spell_Frost_WizardMark'},
  ['Frostwolfheuler']={t=3000,icon='Ability_Mount_WhiteDireWolf'},
  ['Frostöl']={t=3000,icon='Temp'},
  ['Funke']={t=2000,icon='Spell_Nature_Lightning'},
  ['Furbolggestalt']={t=2000,icon='INV_Misc_MonsterClaw_04'},
  ['Furcht']={t=1500,icon='Spell_Shadow_Possession'},
  ['Furcht (NYI)']={t=3000,icon='Spell_Shadow_Possession'},
  ['Furis Teufelsross herbeizaubern ATTRAPPE DND']={t=5000,icon='Temp'},
  ['Fährtenleser des Syndikats (MURP) DND']={t=1000,icon='Spell_Arcane_Blink'},
  ['Gabe von Xavian']={t=5000,icon='Spell_Holy_FlashHeal'},
  ['Gabelblitzschlag']={t=2000,icon='Spell_Nature_ChainLightning'},
  ['Gargoylestoß']={t=1500,icon='Spell_Shadow_ShadowBolt'},
  ['Gasbombe']={t=1000,icon='INV_Misc_Ammo_Bullet_01'},
  ['Gebet der Heilung']={t=3000,icon='Spell_Holy_PrayerOfHealing02'},
  ['Geburt']={t=2000,icon='Temp'},
  ['Gedanken beherrschen']={t=2000,icon='Spell_Shadow_ShadowWordDominate'},
  ['Gedanken verseuchen']={t=4000,icon='Spell_Shadow_CallofBone'},
  ['Gedankenbeben']={t=2000,icon='Spell_Nature_Earthquake'},
  ['Gedankenbenebelndes Gift']={t=3000,icon='Spell_Nature_NullifyDisease'},
  ['Gedankenbenebelndes Gift II']={t=3000,icon='Spell_Nature_NullifyDisease'},
  ['Gedankenbenebelndes Gift III']={t=3000,icon='Spell_Nature_NullifyDisease'},
  ['Gedankenkontrolle']={t=3000,icon='Spell_Shadow_ShadowWordDominate'},
  ['Gedankenschlag']={t=1500,icon='Spell_Shadow_UnholyFrenzy'},
  ['Gedankenverfall']={t=2000,icon='Spell_Shadow_MindRot'},
  ['Gefleckten Hasen beschwören']={t=1000,icon='Ability_Seal'},
  ['Gefleckter Frostsäbler']={t=3000,icon='Ability_Mount_WhiteTiger'},
  ['Gefleckter Panther']={t=3000,icon='Ability_Mount_BlackPanther'},
  ['Gefüllten Verwahrungskasten herstellen']={t=2000,icon='Temp'},
  ['Gegenstand erstellen - Bild (DND)']={t=5000,icon='Spell_Shadow_SoulGem'},
  ['Gegenwart des Todes']={t=1000,icon='Spell_Shadow_ShadeTrueSight'},
  ['Geheimen Safe öffnen']={t=5000,icon='Temp'},
  ['Geist Spawn-out']={t=500,icon='Temp'},
  ['Geist auffüllen']={t=3000,icon='Spell_Nature_MoonGlow'},
  ['Geist auffüllen II']={t=3000,icon='Spell_Nature_MoonGlow'},
  ['Geist der Ahnen']={t=10000,icon='Spell_Nature_Regenerate'},
  ['Geist der Vergangenheit beschwören']={t=4000,icon='Spell_Nature_Purge'},
  ['Geist manifestieren']={t=5000,icon='Spell_Totem_WardOfDraining'},
  ['Geistdiebstahl']={t=2000,icon='Spell_Shadow_Possession'},
  ['Geister austreiben']={t=4000,icon='Temp'},
  ['Geistermagneten platzieren']={t=1500,icon='Temp'},
  ['Geistermagneten zerstören']={t=10000,icon='Temp'},
  ['Geisterwolf']={t=3000,icon='Spell_Nature_SpiritWolf'},
  ['Gelbbraune Säblerkatze']={t=3000,icon='Ability_Mount_JungleTiger'},
  ['Gelbe Qirajipanzerdrohne beschwören']={t=3000,icon='INV_Misc_QirajiCrystal_01'},
  ['Geldkassette öffnen']={t=5000,icon='Temp'},
  ['Geläutertes Wasser versprühen']={t=6000,icon='Temp'},
  ['Gemeinsame Bande']={t=1500,icon='Spell_Shadow_UnsummonBuilding'},
  ['Geringe Unsichtbarkeit']={t=3000,icon='Spell_Magic_LesserInvisibilty'},
  ['Geringe Welle der Heilung']={t=1500,icon='Spell_Nature_HealingWaveLesser'},
  ['Geringes Heilen']={t=2500,icon='Spell_Holy_LesserHeal'},
  ['Geringes Manaöl']={t=3000,icon='Temp'},
  ['Geringes Zauberöl']={t=3000,icon='Temp'},
  ['Geschoss abfeuern']={t=3000,icon='INV_Ammo_Bullet_03'},
  ['Geschärfte Sinne']={t=1000,icon='Temp'},
  ['Gesegnetes Zauberöl']={t=3000,icon='Temp'},
  ['Gestreifter Frostsäbler']={t=3000,icon='Ability_Mount_WhiteTiger'},
  ['Gestreifter Nachtsäbler']={t=3000,icon='Ability_Mount_BlackPanther'},
  ['Gesundheitsstein herstellen']={t=3000,icon='INV_Stone_04'},
  ['Gesundheitsstein herstellen (erheblich)']={t=3000,icon='INV_Stone_04'},
  ['Gesundheitsstein herstellen (gering)']={t=3000,icon='INV_Stone_04'},
  ['Gesundheitsstein herstellen (groß)']={t=3000,icon='INV_Stone_04'},
  ['Gesundheitsstein herstellen (schwach)']={t=3000,icon='INV_Stone_04'},
  ['Getoastetes Smorc']={t=1000,icon='INV_SummerFest_Smorc'},
  ['Geweihte Waffe']={t=3000,icon='Temp'},
  ['Gewölbewärter wecken']={t=5000,icon='Spell_Nature_Earthquake'},
  ['Gezielter Schuss']={t=3000,icon='INV_Spear_07'},
  ['Gift bannen']={t=2000,icon='Spell_Holy_Purify'},
  ['Giftbann']={t=2000,icon='Temp'},
  ['Giftblitz']={t=2500,icon='Spell_Nature_CorrosiveBreath'},
  ['Giftspucke']={t=2500,icon='Spell_Nature_CorrosiveBreath'},
  ['Giftstachel']={t=2000,icon='INV_Misc_MonsterTail_03'},
  ['Giftwolke']={t=1000,icon='Spell_Nature_Regenerate'},
  ['Gletschergebrüll']={t=1000,icon='Spell_Frost_FrostNova'},
  ['Glimmernetz beschwören']={t=1000,icon='Ability_Seal'},
  ['Glut beschwören']={t=2000,icon='Spell_Fire_Fire'},
  ['Glutseher - Start']={t=5000,icon='Spell_Nature_EarthBindTotem'},
  ['Glyphen des Zauberschutzes herbeirufen']={t=3000,icon='Temp'},
  ['Glänzendes Schmuckstück']={t=5000,icon='INV_Misc_Orb_03'},
  ['Gnomen-Kameraverbindung']={t=3000,icon='Temp'},
  ['Gnomischer Transporter']={t=10000,icon='Temp'},
  ['Goblin-Bombe beschwören']={t=250,icon='Ability_Repair'},
  ['Goblin-Kameraverbindung']={t=3000,icon='Temp'},
  ['Goblin-Landmine']={t=1000,icon='Spell_Shadow_Metamorphosis'},
  ['Goblin-Mörser']={t=1000,icon='Spell_Fire_SelfDestruct'},
  ['Golddietrich']={t=5000,icon='Temp'},
  ['Goldene Säblerkatze']={t=3000,icon='Ability_Mount_JungleTiger'},
  ['Goldhain-Portal']={t=5000,icon='Temp'},
  ['Goldhain-Teleporter']={t=2000,icon='Temp'},
  ['Gong']={t=500,icon='Temp'},
  ['Gong Zul\'Farrak Gong']={t=500,icon='Temp'},
  ['Gor\'tesh-Kopf einpflanzen']={t=5000,icon='Temp'},
  ['Grark fangen']={t=3000,icon='Temp'},
  ['Graublauer Kodo']={t=3000,icon='Ability_Mount_Kodo_02'},
  ['Grauer Kodo']={t=3000,icon='Ability_Mount_Kodo_01'},
  ['Grauer Widder']={t=3000,icon='Ability_Mount_MountainRam'},
  ['Grauer Wolf']={t=3000,icon='Ability_Mount_WhiteDireWolf'},
  ['Green Dragon Transform DND']={t=1000,icon='Temp'},
  ['Greifende Ranken']={t=1000,icon='Spell_Nature_Earthquake'},
  ['Grimmhauer']={t=1000,icon='Spell_Nature_Thorns'},
  ['Grobes Dynamit']={t=1000,icon='Spell_Fire_SelfDestruct'},
  ['Groms Tribut']={t=2000,icon='Temp'},
  ['Große Bronzebombe']={t=1000,icon='Spell_Fire_SelfDestruct'},
  ['Große Eisenbombe']={t=1000,icon='Spell_Fire_SelfDestruct'},
  ['Große Heilung']={t=3000,icon='Spell_Holy_GreaterHeal'},
  ['Große Kupferbombe']={t=1000,icon='Spell_Fire_SelfDestruct'},
  ['Große Ohreule beschwören']={t=1000,icon='Ability_Seal'},
  ['Große Unsichtbarkeit']={t=3000,icon='Spell_Nature_Invisibilty'},
  ['Große Zephyriumladung']={t=5000,icon='Temp'},
  ['Großen Skarabäuskasten öffnen']={t=5000,icon='Temp'},
  ['Großer Waldwolf']={t=3000,icon='Ability_Mount_BlackDireWolf'},
  ['Großer brauner Kodo']={t=3000,icon='Ability_Mount_Kodo_03'},
  ['Großer grauer Kodo']={t=3000,icon='Ability_Mount_Kodo_01'},
  ['Großer weißer Kodo']={t=3000,icon='Ability_Mount_Kodo_01'},
  ['Großes Heilen']={t=4000,icon='Spell_Holy_Heal'},
  ['Großes Zeug abbrechen']={t=2000,icon='Spell_Shadow_CurseOfAchimonde'},
  ['Grubenratte wiederbeleben']={t=3000,icon='Spell_Holy_Resurrection'},
  ['Gruftskarabäen']={t=1500,icon='Spell_Shadow_CarrionSwarm'},
  ['Grüne Qirajipanzerdrohne beschwören']={t=3000,icon='INV_Misc_QirajiCrystal_03'},
  ['Grüne Wassernatter beschwören']={t=1000,icon='Ability_Seal'},
  ['Grünen Ara beschwören']={t=1000,icon='Ability_Seal'},
  ['Grüner Gordokgrog']={t=1000,icon='INV_Drink_03'},
  ['Grüner Kodo']={t=3000,icon='Ability_Mount_Kodo_02'},
  ['Grüner Roboschreiter']={t=3000,icon='Ability_Mount_MechaStrider'},
  ['Grünes Skelettschlachtross']={t=3000,icon='Ability_Mount_Undeadhorse'},
  ['Gunthers Angesicht beschwören']={t=2000,icon='Temp'},
  ['Gurky beschwören']={t=1000,icon='Ability_Seal'},
  ['Guses Signal platzieren']={t=5000,icon='Temp'},
  ['Göttliche Pein']={t=2500,icon='Spell_Holy_HolySmite'},
  ['Hakennetz']={t=500,icon='Ability_Ensnare'},
  ['Hammer des Zorns']={t=1000,icon='Ability_ThunderClap'},
  ['Hand von Iruxos']={t=5000,icon='Temp'},
  ['Heilen']={t=3000,icon='Spell_Holy_Heal02'},
  ['Heilen Bild (DND)']={t=3500,icon='Spell_Holy_Heal'},
  ['Heilende Berührung']={t=3500,icon='Spell_Nature_HealingTouch'},
  ['Heiliger Zorn']={t=2000,icon='Spell_Holy_Excorcism'},
  ['Heiliges Feuer']={t=3500,icon='Spell_Holy_SearingLight'},
  ['Heiliges Licht']={t=2500,icon='Spell_Holy_HolyBolt'},
  ['Heiliges Schmettern']={t=2500,icon='Spell_Holy_HolySmite'},
  ['Heilsprache']={t=1000,icon='Spell_Holy_Heal'},
  ['Heilsprache II']={t=1000,icon='Spell_Holy_Heal'},
  ['Heilungszauberschutz']={t=2000,icon='Spell_Holy_LayOnHands'},
  ['Helculars Marionetten beschwören']={t=3000,icon='Spell_Shadow_Haunting'},
  ['Helle Schmuckstücke']={t=5000,icon='INV_Misc_Orb_03'},
  ['Helles Lagerfeuer']={t=10000,icon='Spell_Fire_Fire'},
  ['Herrschaft der Seele']={t=3000,icon='Spell_Shadow_ShadowWordDominate'},
  ['Hervorragendes Manaöl']={t=3000,icon='Temp'},
  ['Hervorragendes Zauberöl']={t=3000,icon='Temp'},
  ['Herz von Hakkar - Molthor verzehrt das Herz']={t=2000,icon='Temp'},
  ['Heulende Wut']={t=5000,icon='Ability_BullRush'},
  ['Heuschreckenschwarm']={t=3000,icon='Spell_Nature_InsectSwarm'},
  ['Hippogryphenjunges']={t=1000,icon='Ability_Seal'},
  ['Hochexplosive Bombe']={t=1000,icon='Spell_Fire_SelfDestruct'},
  ['Hofhuhn beschwören']={t=1000,icon='Ability_Seal'},
  ['Holunderbeerenkuchen']={t=1000,icon='INV_Misc_Food_10'},
  ['Hyazinth-Ara beschwören']={t=1000,icon='Ability_Seal'},
  ['Höllenbestiendiener beschwören']={t=2000,icon='Spell_Shadow_SummonInfernal'},
  ['Höllenfeuer']={t=3000,icon='Spell_Fire_SelfDestruct'},
  ['Höllenfeuer II']={t=3000,icon='Spell_Fire_SelfDestruct'},
  ['Höllenfeuer III']={t=3000,icon='Spell_Fire_SelfDestruct'},
  ['Ichmans Signal platzieren']={t=5000,icon='Temp'},
  ['Ilkruds Wächter']={t=5000,icon='Spell_Shadow_SummonVoidWalker'},
  ['Illusionäre Traumbehüter beschwören']={t=1000,icon='Spell_Shadow_Teleport'},
  ['Illusionären Nachtmahr beschwören']={t=2000,icon='Spell_Fire_SealOfFire'},
  ['Illusionäres Gespenst beschwören']={t=1000,icon='Spell_Shadow_Teleport'},
  ['Illusionäres Trugbild beschwören']={t=2000,icon='Spell_Fire_SealOfFire'},
  ['Implosion']={t=10000,icon='Spell_Fire_SelfDestruct'},
  ['Inferno']={t=2000,icon='Spell_Shadow_SummonInfernal'},
  ['Inferno-Muschelschale']={t=3000,icon='Spell_Fire_SelfDestruct'},
  ['Inkantation der Manifestation']={t=2000,icon='Spell_Magic_LesserInvisibilty'},
  ['Inselfrosch beschwören']={t=1000,icon='Ability_Seal'},
  ['Instabile Substanz']={t=3000,icon='Spell_Fire_Incinerate'},
  ['Ishamuhale beschwören']={t=2000,icon='Spell_Shadow_LifeDrain'},
  ['Ivus Teleporter Bild DND']={t=1000,icon='Temp'},
  ['Ivus anrufen']={t=10000,icon='Temp'},
  ['J\'eevee beschwört ein Objekt']={t=2000,icon='Temp'},
  ['J\'eevee erlösen']={t=2000,icon='Temp'},
  ['Jarkals Übersetzung']={t=4500,icon='Spell_Holy_Restoration'},
  ['Jeztors Signal platzieren']={t=5000,icon='Temp'},
  ['Jubling beschwören']={t=1000,icon='Ability_Seal'},
  ['Julies Segen']={t=2000,icon='Spell_Holy_Renew'},
  ['Jungen Grimmlingflitzer beschwören']={t=1000,icon='Ability_Seal'},
  ['Kadraks Flagge']={t=2000,icon='INV_Banner_03'},
  ['Kahlen Worg rufen']={t=1300,icon='Spell_Shadow_ChillTouch'},
  ['Kakadu beschwören']={t=1000,icon='Ability_Seal'},
  ['Kakerlake beschwören']={t=1000,icon='Ability_Seal'},
  ['Kalaran-Fackel herbeizaubern']={t=1000,icon='Temp'},
  ['Kanone abfeuern']={t=2000,icon='Temp'},
  ['Kanonenfeuer']={t=1000,icon='Spell_Fire_FireBolt02'},
  ['Karangs Banner beschwören']={t=500,icon='Temp'},
  ['Kasernen-Teleporter']={t=2000,icon='Temp'},
  ['Kastanienbraune Stute']={t=3000,icon='Ability_Mount_RidingHorse'},
  ['Kerlonian wecken']={t=4500,icon='Temp'},
  ['Kettenblitzschlag']={t=2500,icon='Spell_Nature_ChainLightning'},
  ['Kettenbrand']={t=3000,icon='Spell_Shadow_ManaBurn'},
  ['Kettenheilung']={t=2500,icon='Spell_Nature_HealingWaveGreater'},
  ['Kev']={t=3000,icon='Spell_Fire_FireBolt'},
  ['Khadgars Öffnung']={t=10000,icon='INV_Misc_Key_14'},
  ['Klarer Rumsey-Rum']={t=1000,icon='INV_Drink_08'},
  ['Kleine Bronzebombe']={t=1000,icon='Spell_Fire_SelfDestruct'},
  ['Kleine Zephyriumladung']={t=5000,icon='Temp'},
  ['Klinge schärfen']={t=3000,icon='Temp'},
  ['Klinge schärfen II']={t=3000,icon='Temp'},
  ['Klinge schärfen III']={t=3000,icon='Temp'},
  ['Klinge schärfen IV']={t=3000,icon='Temp'},
  ['Klinge schärfen V']={t=3000,icon='Temp'},
  ['Klonen']={t=2500,icon='Spell_Shadow_BlackPlague'},
  ['Kloster-Teleporter']={t=2000,icon='Temp'},
  ['Knirschkiefer beschwören']={t=3000,icon='Temp'},
  ['Knochensplitter']={t=500,icon='Spell_Shadow_ScourgeBuild'},
  ['Kodokombobulator']={t=5000,icon='Trade_Fishing'},
  ['Kollossusschlag']={t=5000,icon='Temp'},
  ['Krabblergift']={t=2000,icon='Spell_Nature_NullifyPoison'},
  ['Kraft von Arko\'narin']={t=4000,icon='Spell_Nature_AstralRecal'},
  ['Kranker Brühschleimer']={t=2000,icon='Spell_Shadow_CreepingPlague'},
  ['Krankheit heilen']={t=2500,icon='Spell_Holy_NullifyDisease'},
  ['Krankheitsschuss']={t=2000,icon='Spell_Shadow_CallofBone'},
  ['Krankheitsstoß']={t=1500,icon='Spell_Nature_EarthBind'},
  ['Kreegs Hauweg Starkbier']={t=1000,icon='INV_Drink_05'},
  ['Kreis der Heilung']={t=3000,icon='Spell_Holy_BlessingOfProtection'},
  ['Kreis des Rufens herbeizaubern']={t=10000,icon='Temp'},
  ['Kreischergeist beschwören']={t=2000,icon='Temp'},
  ['Kriegsdonner']={t=500,icon='Ability_WarStomp'},
  ['Kristallblick']={t=2000,icon='Ability_GolemThunderClap'},
  ['Kristallblitzstrahl']={t=2000,icon='Spell_Shadow_Teleport'},
  ['Kristallener Schlummer']={t=2000,icon='Spell_Nature_Sleep'},
  ['Kristallkanone']={t=1000,icon='Temp'},
  ['Kristallstaubmörser']={t=500,icon='Spell_Fire_Fireball02'},
  ['Kroshius entzünden']={t=3000,icon='Temp'},
  ['Krähenhorstei einfrieren']={t=500,icon='Temp'},
  ['Krähenhorstei einfrieren - Prototyp']={t=500,icon='Temp'},
  ['Krähenhorstei holen']={t=500,icon='Temp'},
  ['Krähenhorstei stören']={t=1000,icon='Temp'},
  ['Krähenhorstwelpe - Spawningzauber']={t=500,icon='Temp'},
  ['Krähenhorstwelpen beschwören']={t=2000,icon='Temp'},
  ['Kräutersammeln']={t=5000,icon='Spell_Nature_NatureTouchGrow'},
  ['Kugelblitzschlag']={t=1000,icon='Spell_Lightning_LightningBolt01'},
  ['Käfig öffnen']={t=5000,icon='Temp'},
  ['König der Gordok']={t=1000,icon='INV_Crown_02'},
  ['Kürschnerei']={t=2000,icon='INV_Misc_Pelt_Wolf_01'},
  ['Laden']={t=5000,icon='Spell_Shadow_EvilEye'},
  ['Langschuss II']={t=4000,icon='Ability_Marksmanship'},
  ['Langschuss III']={t=4000,icon='Ability_Marksmanship'},
  ['Langsicht']={t=1000,icon='Ability_TownWatch'},
  ['Lebendige Flamme beschwören']={t=2000,icon='Spell_Fire_Fire'},
  ['Lebensdiebstahl']={t=1500,icon='Spell_Shadow_LifeDrain02'},
  ['Lebensechte Kröte beschwören']={t=1000,icon='Ability_Seal'},
  ['Lebensernte']={t=1000,icon='Spell_Shadow_Requiem'},
  ['Lederrückenschnappkiefer beschwören']={t=1000,icon='Ability_Seal'},
  ['Leereblitz']={t=3000,icon='Spell_Shadow_ShadowBolt'},
  ['Leerer Festtagskrug']={t=2000,icon='Temp'},
  ['Leerwandler beschwören']={t=10000,icon='Spell_Shadow_SummonVoidWalker'},
  ['Leerwandlerwächter']={t=3000,icon='Spell_Shadow_SummonVoidWalker'},
  ['Leopard']={t=3000,icon='Ability_Mount_JungleTiger'},
  ['Lepraheilmittel!']={t=2000,icon='Spell_Holy_FlashHeal'},
  ['Leuchtend grüner Roboschreiter']={t=3000,icon='Ability_Mount_MechaStrider'},
  ['Leuchtturm-Teleporter']={t=2000,icon='Temp'},
  ['Lichtbarriere']={t=2000,icon='Temp'},
  ['Lichtblitz']={t=1500,icon='Spell_Holy_FlashHeal'},
  ['Liebesrausch']={t=1000,icon='INV_Ammo_Arrow_02'},
  ['Lila Roboschreiter']={t=3000,icon='Ability_Mount_MechaStrider'},
  ['Linkens Bumerang']={t=500,icon='INV_Weapon_ShortBlade_10'},
  ['Lord Valthalaks Geist freigeben DND']={t=5000,icon='Temp'},
  ['Läuterung von Atiesh']={t=20000,icon='Temp'},
  ['Löschen']={t=2000,icon='Temp'},
  ['Löwengerippe platzieren']={t=2500,icon='INV_Misc_Bowl_01'},
  ['Macht von Ragnaros']={t=500,icon='Spell_Fire_SelfDestruct'},
  ['Macht von Shahram']={t=1000,icon='Spell_Nature_WispSplode'},
  ['Machthieb']={t=1000,icon='INV_Gauntlets_31'},
  ['Magathas Brandpulver']={t=1300,icon='Temp'},
  ['Magierblick']={t=3000,icon='Temp'},
  ['Magierkugel herstellen']={t=4000,icon='Temp'},
  ['Magierrobe herstellen']={t=4000,icon='Temp'},
  ['Magiestab beschwören']={t=2000,icon='INV_Staff_26'},
  ['Magische Bohnen pflanzen']={t=2000,icon='INV_Misc_Food_Wheat_02'},
  ['Magmaregen']={t=2000,icon='Temp'},
  ['Magmaschlag']={t=1000,icon='Spell_Fire_FlameShock'},
  ['Maibaum']={t=10000,icon='Spell_Shadow_Twilight'},
  ['Maine Coon beschwören']={t=1000,icon='Ability_Seal'},
  ['Majordomus-Teleporter - Bild']={t=1000,icon='Spell_Arcane_Blink'},
  ['Mal der Flammen']={t=1000,icon='Spell_Fire_Fireball'},
  ['Malathroms Diener']={t=1000,icon='Spell_Shadow_CorpseExplode'},
  ['Mana neutralisieren']={t=2000,icon='Spell_Shadow_DarkRitual'},
  ['Manabrand']={t=3000,icon='Spell_Shadow_ManaBurn'},
  ['Manaschwäche']={t=1500,icon='Ability_Creature_Poison_03'},
  ['Manasturm']={t=2000,icon='Spell_Frost_IceStorm'},
  ['Manifestation der Säuberung']={t=4000,icon='Temp'},
  ['Maschinengewehr']={t=500,icon='Ability_Marksmanship'},
  ['Massenbannung']={t=1000,icon='Spell_Shadow_Teleport'},
  ['Massenheilung']={t=1000,icon='Spell_Holy_GreaterHeal'},
  ['Massiver Geysir']={t=1500,icon='Spell_Frost_SummonWaterElemental'},
  ['Massiver Mörser']={t=3000,icon='Temp'},
  ['Matsch schleudern']={t=1000,icon='Spell_Nature_Sleep'},
  ['Maurys Fuß freigeben']={t=5000,icon='Temp'},
  ['Meboks Klugheitstrank']={t=3000,icon='Temp'},
  ['Mechanikreparaturset']={t=2000,icon='INV_Gizmo_03'},
  ['Mechanisches Eichhörnchen']={t=1000,icon='Spell_Shadow_Metamorphosis'},
  ['Mechanisches Huhn beschwören']={t=1000,icon='Ability_Seal'},
  ['Megavolt']={t=2000,icon='Spell_Nature_ChainLightning'},
  ['Melodische Ekstase']={t=1000,icon='Temp'},
  ['Merithras Erwachen']={t=2000,icon='Temp'},
  ['Miblons Köder']={t=2000,icon='INV_Misc_Food_50'},
  ['Mini-Pistole']={t=100,icon='INV_Musket_04'},
  ['Mirkfallonfungus']={t=3000,icon='Spell_Holy_HarmUndeadAura'},
  ['Mistelzweig']={t=1000,icon='INV_Misc_Branch_01'},
  ['Mit Köpfen sprechen']={t=5000,icon='Spell_Shadow_LifeDrain'},
  ['Mithrilschrapnellbombe']={t=1000,icon='Spell_Fire_SelfDestruct'},
  ['Mondfesteinladung']={t=5000,icon='Spell_Arcane_TeleportMoonglade'},
  ['Mondklaue beschwören']={t=3000,icon='Temp'},
  ['Mondpirschergestalt (keine Unsichtbarkeit)']={t=1000,icon='Ability_Hibernation'},
  ['Monstrositätenspucke']={t=2500,icon='Spell_Nature_CorrosiveBreath'},
  ['Moonbrook-Teleporter']={t=2000,icon='Temp'},
  ['Mor\'rogal verzaubern']={t=1300,icon='Temp'},
  ['Morastiger Matsch']={t=1000,icon='Spell_Nature_StrangleVines'},
  ['Mulvericks Signal platzieren']={t=5000,icon='Temp'},
  ['Murki beschwören']={t=1000,icon='Ability_Seal'},
  ['Murky beschwören']={t=1000,icon='Ability_Seal'},
  ['Myzrael beschwören']={t=10000,icon='Spell_Shadow_LifeDrain'},
  ['Mächtige Magie bannen']={t=4000,icon='Spell_Arcane_StarFire'},
  ['Mächtige Zephyriumladung']={t=5000,icon='Temp'},
  ['Mächtiges Riechsalz']={t=2000,icon='INV_Misc_Ammo_Gunpowder_01'},
  ['NG-5-Ladung einstellen (Blau)']={t=5000,icon='INV_Misc_Bomb_05'},
  ['NG-5-Ladung einstellen (Rot)']={t=5000,icon='INV_Misc_Bomb_05'},
  ['Nach Darnassus teleportieren - Ereignis']={t=1000,icon='Temp'},
  ['Nach Kobalt graben']={t=1500,icon='Temp'},
  ['Nachtkriecher']={t=5000,icon='Trade_Fishing'},
  ['Nachtsäbler']={t=3000,icon='Ability_Mount_BlackPanther'},
  ['Nachwachsen']={t=2000,icon='Spell_Nature_ResistNature'},
  ['Nagmaras Liebestrank']={t=1000,icon='Temp'},
  ['Narain!']={t=3000,icon='INV_Misc_Head_Gnome_01'},
  ['Naralex\' Alptraum']={t=2000,icon='Spell_Nature_Sleep'},
  ['Naturgleichgewicht']={t=3000,icon='Temp'},
  ['Naturgleichgewicht-Fehlschlag']={t=10000,icon='Temp'},
  ['Naturschwäche']={t=5000,icon='INV_Misc_QirajiCrystal_03'},
  ['Netheredelstein']={t=3000,icon='Temp'},
  ['Netherwandler beschwören']={t=4000,icon='Spell_Shadow_GatherShadows'},
  ['Neues Magiegeschoss (TEST)']={t=2000,icon='Temp'},
  ['Neulieferung']={t=2000,icon='Spell_Misc_Drink'},
  ['Nimboyas behängte Pike platzieren']={t=2000,icon='Temp'},
  ['Nostalgie']={t=4000,icon='Spell_Shadow_LifeDrain'},
  ['Nymphensittich beschwören']={t=1000,icon='Ability_Seal'},
  ['Obsidianschwarzer Raptor']={t=3000,icon='Ability_Mount_Raptor'},
  ['Olivfarbenen Schnappkiefer beschwören']={t=1000,icon='Ability_Seal'},
  ['Onyxiawelpen beschwören']={t=2000,icon='Temp'},
  ['Opfer verwandeln']={t=2000,icon='Spell_Magic_LesserInvisibilty'},
  ['Opferung']={t=1000,icon='Spell_Holy_DivineIntervention'},
  ['Orakel platzieren']={t=3000,icon='Temp'},
  ['Orangene Tigerkatze beschwören']={t=1000,icon='Ability_Seal'},
  ['Ouro Abtauchen Bild']={t=1500,icon='Spell_Fire_Volcano'},
  ['Ozzie explodiert']={t=2000,icon='Temp'},
  ['PX83-Enigmatron herstellen']={t=2000,icon='INV_Misc_Bowl_01'},
  ['Palamino-Hengst']={t=3000,icon='Ability_Mount_RidingHorse'},
  ['Palomino-Hengst']={t=3000,icon='Ability_Mount_RidingHorse'},
  ['Panda beschwören']={t=1000,icon='Ability_Seal'},
  ['Panther']={t=3000,icon='Ability_Mount_BlackPanther'},
  ['Pantherkäfigschlüssel']={t=5000,icon='Temp'},
  ['Parasit']={t=2000,icon='Ability_Poisons'},
  ['Partylaune']={t=1000,icon='Spell_Shadow_DarkSummoning'},
  ['Peon-Verkleidung']={t=3000,icon='Ability_Rogue_Disguise'},
  ['Pfeilfrosch beschwören']={t=1000,icon='Ability_Seal'},
  ['Phiole füllen']={t=5000,icon='Temp'},
  ['Pionierexplosion']={t=5000,icon='Spell_Fire_SelfDestruct'},
  ['Polaritätsveränderung']={t=3000,icon='Spell_Nature_Lightning'},
  ['Poley beschwören']={t=1000,icon='Ability_Seal'},
  ['Portal der Schmetterschilde']={t=1500,icon='Spell_Arcane_TeleportOrgrimmar'},
  ['Portal: Darnassus']={t=10000,icon='Spell_Arcane_PortalDarnassus'},
  ['Portal: Ironforge']={t=10000,icon='Spell_Arcane_PortalIronForge'},
  ['Portal: Karazhan']={t=10000,icon='Spell_Arcane_PortalUnderCity'},
  ['Portal: Orgrimmar']={t=10000,icon='Spell_Arcane_PortalOrgrimmar'},
  ['Portal: Stormwind']={t=10000,icon='Spell_Arcane_PortalStormWind'},
  ['Portal: Thunder Bluff']={t=10000,icon='Spell_Arcane_PortalThunderBluff'},
  ['Portal: Undercity']={t=10000,icon='Spell_Arcane_PortalUnderCity'},
  ['Prinzessin beschwört Portal']={t=10000,icon='Spell_Arcane_PortalIronForge'},
  ['Prismatische Barriere herbeirufen']={t=10000,icon='Temp'},
  ['Proudmoores Verteidigung']={t=3000,icon='Spell_Holy_BlessingOfProtection'},
  ['Präriehuhn beschwören']={t=1000,icon='Ability_Seal'},
  ['Präriehund beschwören']={t=1000,icon='Ability_Seal'},
  ['Psychometrie']={t=5000,icon='Spell_Holy_Restoration'},
  ['Purpurhände']={t=4000,icon='Spell_Shadow_SiphonMana'},
  ['Purpurnes Skelettschlachtross']={t=3000,icon='Ability_Mount_Undeadhorse'},
  ['Purpurrote Natter beschwören']={t=1000,icon='Ability_Seal'},
  ['Purpurroten Welpling beschwören']={t=1000,icon='Ability_Seal'},
  ['Pyroschlag']={t=6000,icon='Spell_Fire_Fireball02'},
  ['Q. Pidos Pfeil werfen']={t=1000,icon='Temp'},
  ['Quest - Sergra Darkthorn - Zauber']={t=3000,icon='Temp'},
  ['Quest - Teleport Spawn-out']={t=1000,icon='Temp'},
  ['Quest - Treant beschwören']={t=3000,icon='Spell_Nature_NatureTouchGrow'},
  ['Quest - Trollhelden beschwören - Bild']={t=30000,icon='Temp'},
  ['Ragnaros Auftauchen']={t=2900,icon='Spell_Fire_LavaSpawn'},
  ['Ragnaros beschwören']={t=10000,icon='Spell_Fire_LavaSpawn'},
  ['Rakete abfeuern']={t=3000,icon='INV_Ammo_Bullet_03'},
  ['Raketenschlag']={t=3000,icon='Temp'},
  ['Rappe']={t=3000,icon='Ability_Mount_NightmareHorse'},
  ['Raptorfeder']={t=5000,icon='Temp'},
  ['Raue Kupferbombe']={t=1000,icon='Spell_Fire_SelfDestruct'},
  ['Raues Dynamit']={t=1000,icon='Spell_Fire_SelfDestruct'},
  ['Razelikh beschwören']={t=10000,icon='Temp'},
  ['Rechtschaffene Flamme']={t=4000,icon='Spell_Holy_InnerFire'},
  ['Red Dragon Transform DND']={t=1000,icon='Temp'},
  ['Reitgreif beschwören']={t=3000,icon='Ability_BullRush'},
  ['Reitkodo']={t=3000,icon='INV_Misc_Head_Tauren_02'},
  ['Reitschildkröte']={t=3000,icon='Ability_Hunter_Pet_Turtle'},
  ['Rekonstruktion']={t=3000,icon='Temp'},
  ['Reliktbündel herstellen']={t=1000,icon='Temp'},
  ['Reliktfragment suchen']={t=5000,icon='Temp'},
  ['Reliktkasten öffnen']={t=5000,icon='Temp'},
  ['Rentierstaubeffekt']={t=8000,icon='Temp'},
  ['Rimblat lässt Blumen wachsen']={t=2000,icon='Temp'},
  ['Ringo wiederbeleben']={t=2500,icon='Temp'},
  ['Riss herstellen']={t=3000,icon='Temp'},
  ['Rissleuchtfeuer']={t=2000,icon='Spell_Nature_AbolishMagic'},
  ['Ritual der Beschwörung']={t=5000,icon='Temp'},
  ['Ritual der Verdammnis']={t=10000,icon='Spell_Arcane_PortalDarnassus'},
  ['Ritualglocke reparieren (DND)']={t=3000,icon='Temp'},
  ['Ritualkerze reparieren (DND)']={t=3000,icon='Temp'},
  ['Ritualknoten reaktivieren']={t=3000,icon='Temp'},
  ['Roboter beschwören']={t=1000,icon='Ability_Seal'},
  ['Robustes Dynamit']={t=1000,icon='Spell_Fire_SelfDestruct'},
  ['Rolle herstellen']={t=5000,icon='INV_Scroll_05'},
  ['Rosenblütenregen']={t=500,icon='INV_Misc_Dust_04'},
  ['Rotationsauslöser']={t=3000,icon='Temp'},
  ['Rotblauer Roboschreiter']={t=3000,icon='Ability_Mount_MechaStrider'},
  ['Rote Qirajipanzerdrohne beschwören']={t=3000,icon='INV_Misc_QirajiCrystal_02'},
  ['Roter Roboschreiter']={t=3000,icon='Ability_Mount_MechaStrider'},
  ['Roter Wolf']={t=3000,icon='Ability_Mount_BlackDireWolf'},
  ['Rotes Skelettpferd']={t=3000,icon='Ability_Mount_Undeadhorse'},
  ['Rotes Skelettschlachtross']={t=3000,icon='Ability_Mount_Undeadhorse'},
  ['Rotgescheckter Raptor']={t=3000,icon='Ability_Mount_Raptor'},
  ['Ruchloser Angriff 001']={t=1000,icon='Temp'},
  ['Ruf - Ahn\'Qiraj Tempelendgegner']={t=1000,icon='Temp'},
  ['Ruf - Booty Bay +500']={t=1000,icon='Temp'},
  ['Ruf - Everlook +500']={t=1000,icon='Temp'},
  ['Ruf - Gadgetzan +500']={t=1000,icon='Temp'},
  ['Ruf - Ratchet +500']={t=1000,icon='Temp'},
  ['Ruf der Leere']={t=3000,icon='Spell_Shadow_DeathCoil'},
  ['Ruf des Grabes']={t=2000,icon='Spell_Shadow_ChillTouch'},
  ['Ruf des Nether']={t=10000,icon='Temp'},
  ['Ruf erhöhen']={t=1000,icon='Temp'},
  ['Ruhestein']={t=10000,icon='INV_Misc_Rune_01'},
  ['Ruhigen mechanischen Yeti beschwören']={t=1000,icon='Ability_Seal'},
  ['Rumsey-Rum']={t=1000,icon='INV_Drink_03'},
  ['Rune des geschmolzenen Kerns löschen']={t=1000,icon='Temp'},
  ['Rune des Öffnens']={t=5000,icon='Temp'},
  ['Rutengangtrance']={t=5000,icon='Temp'},
  ['Ruul Snowhoof Formwandel (DND)']={t=1000,icon='Temp'},
  ['Rysons Himmelsauge']={t=1000,icon='Ability_Hunter_EagleEye'},
  ['Rysons Signal platzieren']={t=5000,icon='Temp'},
  ['Rysons allsehendes Auge']={t=1000,icon='Ability_Ambush'},
  ['Rückhand']={t=1000,icon='Spell_Shadow_LifeDrain'},
  ['Rückruf']={t=10000,icon='Temp'},
  ['Sabbernden Worg rufen']={t=1300,icon='Spell_Shadow_ChillTouch'},
  ['Safe öffnen']={t=5000,icon='Temp'},
  ['Salbe auftragen']={t=1300,icon='Temp'},
  ['Salve']={t=3000,icon='Ability_TheBlackArrow'},
  ['Salve II']={t=3000,icon='Ability_TheBlackArrow'},
  ['Samenkörner pflanzen']={t=5000,icon='Temp'},
  ['Samuels sterbliche Überreste begraben']={t=2000,icon='Temp'},
  ['Sandatem']={t=2000,icon='Spell_Fire_WindsofWoe'},
  ['Sandstoß']={t=2000,icon='Spell_Nature_Cyclone'},
  ['Saphiron']={t=20000,icon='Temp'},
  ['Sapta herstellen']={t=3000,icon='INV_Misc_Food_09'},
  ['Sargeras Odem']={t=2000,icon='Spell_Shadow_Metamorphosis'},
  ['Sarilus\' Elementare']={t=3000,icon='Spell_Shadow_RaiseDead'},
  ['Schaden verstärken']={t=2000,icon='Spell_Nature_AbolishMagic'},
  ['Scharlachrot-Auferstehung']={t=2000,icon='Spell_Holy_Resurrection'},
  ['Scharlachrote Natter beschwören']={t=1000,icon='Ability_Seal'},
  ['Schattenblinzeln']={t=500,icon='Spell_Shadow_DetectLesserInvisibility'},
  ['Schattenblitz']={t=3000,icon='Spell_Shadow_ShadowBolt'},
  ['Schattenblitzsalve']={t=3000,icon='Spell_Shadow_ShadowBolt'},
  ['Schattenflamme']={t=2000,icon='Spell_Fire_Incinerate'},
  ['Schattenhafen']={t=250,icon='Spell_Shadow_AntiShadow'},
  ['Schattennova II']={t=3000,icon='Spell_Shadow_ShadeTrueSight'},
  ['Schattenportal']={t=1000,icon='Spell_Shadow_SealOfKings'},
  ['Schattenschale']={t=1000,icon='Spell_Shadow_AntiShadow'},
  ['Schattenschlag beschwören']={t=10000,icon='Temp'},
  ['Schattenschwäche']={t=5000,icon='INV_Misc_QirajiCrystal_05'},
  ['Schattenwiderstand']={t=1000,icon='Spell_Frost_WizardMark'},
  ['Schattenzauberer beschwören']={t=5000,icon='Spell_Shadow_RaiseDead'},
  ['Schattenöl']={t=3000,icon='Temp'},
  ['Schatz-Horde beschwören']={t=1500,icon='Temp'},
  ['Schatz-Horde beschwören\' - Bild']={t=1500,icon='Temp'},
  ['Schecke']={t=3000,icon='Ability_Mount_RidingHorse'},
  ['Schild der Reflektion']={t=1000,icon='Spell_Shadow_Teleport'},
  ['Schildwache beschwören']={t=5000,icon='Spell_Nature_Purge'},
  ['Schimmel']={t=3000,icon='Ability_Mount_RidingHorse'},
  ['Schlachtross beschwören']={t=3000,icon='Spell_Nature_Swiftness'},
  ['Schlaf']={t=1500,icon='Spell_Nature_Sleep'},
  ['Schlag']={t=2000,icon='Temp'},
  ['Schlammtoxin']={t=2000,icon='Spell_Nature_Regenerate'},
  ['Schlangenstrunkranke heilen']={t=500,icon='Temp'},
  ['Schlangensäuberung']={t=30000,icon='Spell_Shadow_LifeDrain'},
  ['Schleichender Schimmelpilz']={t=3000,icon='Spell_Shadow_CreepingPlague'},
  ['Schleichendes Gift']={t=1000,icon='Spell_Nature_SlowPoison'},
  ['Schleichendes Gift II']={t=1000,icon='Spell_Nature_SlowPoison'},
  ['Schleier des Schattens']={t=1500,icon='Spell_Shadow_GatherShadows'},
  ['Schleimblitz']={t=2500,icon='Spell_Nature_CorrosiveBreath'},
  ['Schleimbombardement']={t=1000,icon='Temp'},
  ['Schließen']={t=1000,icon='Temp'},
  ['Schloss knacken']={t=5000,icon='Spell_Nature_MoonKey'},
  ['Schmelzschlag']={t=2000,icon='Spell_Fire_Fire'},
  ['Schmuckstück benutzen']={t=3000,icon='Temp'},
  ['Schmutz schleudern']={t=1000,icon='Spell_Nature_Sleep'},
  ['Schneeeule beschwören']={t=1000,icon='Ability_Seal'},
  ['Schneehasen beschwören']={t=1000,icon='Ability_Seal'},
  ['Schneemann']={t=1500,icon='INV_Ammo_Snowball'},
  ['Schneller Brauner']={t=3000,icon='Ability_Mount_RidingHorse'},
  ['Schneller Dämmerungssäbler']={t=3000,icon='Ability_Mount_JungleTiger'},
  ['Schneller Flammenzauberschutz']={t=1500,icon='Spell_Fire_SealOfFire'},
  ['Schneller Frostsäbler']={t=3000,icon='Ability_Mount_WhiteTiger'},
  ['Schneller Frostzauberschutz']={t=1500,icon='Spell_Fire_SealOfFire'},
  ['Schneller Grauwolf']={t=3000,icon='Ability_Mount_WhiteDireWolf'},
  ['Schneller Kampfrausch']={t=2000,icon='Spell_Nature_BloodLust'},
  ['Schneller Palomino']={t=3000,icon='Ability_Mount_RidingHorse'},
  ['Schneller Razzashiraptor']={t=3000,icon='Ability_Mount_Raptor'},
  ['Schneller Schattensäbler']={t=3000,icon='Ability_Mount_BlackPanther'},
  ['Schneller Sturmsäbler']={t=3000,icon='Ability_Mount_BlackPanther'},
  ['Schneller Waldwolf']={t=3000,icon='Ability_Mount_WhiteDireWolf'},
  ['Schneller blauer Raptor']={t=3000,icon='Ability_Mount_Raptor'},
  ['Schneller brauner Widder']={t=3000,icon='Ability_Mount_MountainRam'},
  ['Schneller brauner Wolf']={t=3000,icon='Ability_Mount_BlackDireWolf'},
  ['Schneller gelber Roboschreiter']={t=3000,icon='Ability_Mount_MechaStrider'},
  ['Schneller grauer Widder']={t=3000,icon='Ability_Mount_MountainRam'},
  ['Schneller grüner Roboschreiter']={t=3000,icon='Ability_Mount_MechaStrider'},
  ['Schneller olivfarbener Raptor']={t=3000,icon='Ability_Mount_Raptor'},
  ['Schneller orangener Raptor']={t=3000,icon='Ability_Mount_Raptor'},
  ['Schneller weißer Roboschreiter']={t=3000,icon='Ability_Mount_MechaStrider'},
  ['Schneller weißer Widder']={t=3000,icon='Ability_Mount_MountainRam'},
  ['Schneller zulianischer Tiger']={t=3000,icon='Ability_Mount_JungleTiger'},
  ['Schnelles weißes Ross']={t=3000,icon='Ability_Mount_RidingHorse'},
  ['Schnurri beschwören']={t=1000,icon='Ability_Seal'},
  ['Schnüffelnase beschwören']={t=2000,icon='Spell_Nature_ProtectionformNature'},
  ['Schnüffelnase-Befehl']={t=1500,icon='Spell_Shadow_LifeDrain'},
  ['Schock']={t=1000,icon='Temp'},
  ['Schockwelle']={t=2000,icon='Ability_Whirlwind'},
  ['Schreckensgeheul']={t=2000,icon='Spell_Shadow_DeathScream'},
  ['Schreckenskralle']={t=1000,icon='Spell_Shadow_ShadowPact'},
  ['Schreckensross herbeirufen']={t=3000,icon='Ability_Mount_Dreadsteed'},
  ['Schreckgespenst des Syndikats beschwören']={t=1000,icon='Spell_Shadow_Twilight'},
  ['Schredder herstellen']={t=1000,icon='INV_Misc_Gear_01'},
  ['Schredderrüstungsschmelze']={t=2000,icon='Spell_Fire_Incinerate'},
  ['Schrumpfen']={t=3000,icon='Spell_Shadow_AntiShadow'},
  ['Schusswaffe abfeuern']={t=1000,icon='Ability_Marksmanship'},
  ['Schwachen Trank trinken']={t=3000,icon='Spell_Holy_Heal'},
  ['Schwacher Frostblitz']={t=2200,icon='Spell_Frost_FrostBolt02'},
  ['Schwaches Manaöl']={t=3000,icon='Temp'},
  ['Schwaches Zauberöl']={t=3000,icon='Temp'},
  ['Schwachsinn']={t=1000,icon='Spell_Shadow_MindSteal'},
  ['Schwachsinn II']={t=1000,icon='Spell_Shadow_MindSteal'},
  ['Schwachsinn III']={t=1000,icon='Spell_Shadow_MindSteal'},
  ['Schwarze Königsnatter beschwören']={t=1000,icon='Ability_Seal'},
  ['Schwarze Qirajipanzerdrohne beschwören']={t=3000,icon='INV_Misc_QirajiCrystal_05'},
  ['Schwarzer Kriegskodo']={t=3000,icon='Ability_Mount_Kodo_01'},
  ['Schwarzer Kriegsraptor']={t=3000,icon='Ability_Mount_Raptor'},
  ['Schwarzer Kriegstiger']={t=3000,icon='Ability_Mount_BlackPanther'},
  ['Schwarzer Kriegswidder']={t=3000,icon='Ability_Mount_MountainRam'},
  ['Schwarzer Kriegswolf']={t=3000,icon='Ability_Mount_BlackDireWolf'},
  ['Schwarzer Pfeil']={t=2000,icon='Ability_TheBlackArrow'},
  ['Schwarzer Rumsey Rum']={t=1000,icon='INV_Drink_04'},
  ['Schwarzer Schlachtenschreiter']={t=3000,icon='Ability_Mount_MechaStrider'},
  ['Schwarzer Schlamm']={t=3000,icon='Spell_Shadow_CallofBone'},
  ['Schwarzer Widder']={t=3000,icon='Ability_Mount_MountainRam'},
  ['Schwarzer Wolf']={t=3000,icon='Ability_Mount_BlackDireWolf'},
  ['Schwarzes Schlachtross']={t=3000,icon='Ability_Mount_NightmareHorse'},
  ['Schweres Dynamit']={t=1000,icon='Spell_Fire_SelfDestruct'},
  ['Schwächliches Skelett beschwören']={t=10000,icon='Spell_Shadow_RaiseDead'},
  ['Schäumenden Verbannten verbannen']={t=1000,icon='Spell_Shadow_LifeDrain'},
  ['Schützen-Treffer']={t=2000,icon='Ability_Marksmanship'},
  ['Seelenbiss']={t=2000,icon='Spell_Shadow_SiphonMana'},
  ['Seelenbrecher']={t=2000,icon='Spell_Shadow_Haunting'},
  ['Seelenfeuer']={t=6000,icon='Spell_Fire_Fireball02'},
  ['Seelensauger']={t=2000,icon='Spell_Shadow_LifeDrain02'},
  ['Seelenstein-Auferstehung']={t=3000,icon='Spell_Shadow_SoulGem'},
  ['Seelenverzehr']={t=4000,icon='Ability_Racial_Cannibalize'},
  ['Seelenzertrümmerung']={t=2000,icon='Spell_Fire_Fire'},
  ['Segen von Shahram']={t=1000,icon='Spell_Holy_LayOnHands'},
  ['Segnung rufen']={t=1000,icon='Temp'},
  ['Selbst-Auferstehung']={t=5000,icon='Temp'},
  ['Selbstdetonation']={t=7000,icon='Spell_Fire_SelfDestruct'},
  ['Selbstzerstörung']={t=7000,icon='Spell_Fire_SelfDestruct'},
  ['Senegal beschwören']={t=1000,icon='Ability_Seal'},
  ['Sengende Flammen']={t=2000,icon='Spell_Fire_Immolation'},
  ['Sengender Schmerz']={t=1500,icon='Spell_Fire_SoulBurn'},
  ['Sergra Darkthorn']={t=3000,icon='Temp'},
  ['Seuchenwolke']={t=2000,icon='Spell_Shadow_CallofBone'},
  ['Shays Glocke']={t=4500,icon='Temp'},
  ['Shy-Rotam beschwören']={t=2500,icon='Temp'},
  ['Siamkatze beschwören']={t=1000,icon='Ability_Seal'},
  ['Sicheren Safe öffnen']={t=5000,icon='Temp'},
  ['Sicht verdunkeln']={t=2000,icon='Spell_Shadow_Fumble'},
  ['Sickernde Weide']={t=1000,icon='Spell_Nature_CorrosiveBreath'},
  ['Siegel aufheben']={t=2000,icon='Temp'},
  ['Signalfackel platzieren']={t=2300,icon='Temp'},
  ['Silberdietrich']={t=5000,icon='Temp'},
  ['Silberne Tigerkatze beschwören']={t=1000,icon='Ability_Seal'},
  ['Silithidenei einsammeln']={t=5000,icon='Temp'},
  ['Silithidenpocken']={t=2000,icon='Spell_Nature_NullifyDisease'},
  ['Skarabäuskasten öffnen']={t=5000,icon='Temp'},
  ['Skelett beschwören']={t=2000,icon='Spell_Shadow_RaiseDead'},
  ['Skelett der Atal\'ai beschwören']={t=1000,icon='Spell_Shadow_RaiseDead'},
  ['Skelettminenarbeiterexplosion']={t=5000,icon='Spell_Fire_SelfDestruct'},
  ['Skelettpferd']={t=3000,icon='Ability_Mount_Undeadhorse'},
  ['Skelettross']={t=3000,icon='Ability_Mount_Undeadhorse'},
  ['Sklavensauger']={t=1000,icon='Spell_Shadow_ChillTouch'},
  ['Slidores Signal platzieren']={t=5000,icon='Temp'},
  ['Smaragdfarbener Raptor']={t=3000,icon='Ability_Mount_Raptor'},
  ['Smaragdgrünen Welpling beschwören']={t=1000,icon='Ability_Seal'},
  ['Smokeys Sprengstoff platzieren']={t=3000,icon='Temp'},
  ['Sofort wirkendes Gift']={t=3000,icon='Ability_Poisons'},
  ['Sofort wirkendes Gift II']={t=3000,icon='Ability_Poisons'},
  ['Sofort wirkendes Gift III']={t=3000,icon='Ability_Poisons'},
  ['Sofort wirkendes Gift IV']={t=3000,icon='Ability_Poisons'},
  ['Sofort wirkendes Gift V']={t=3000,icon='Ability_Poisons'},
  ['Sofort wirkendes Gift VI']={t=3000,icon='Ability_Poisons'},
  ['Sofort wirkendes Toxin']={t=3000,icon='INV_Potion_19'},
  ['Sol H']={t=3000,icon='Spell_Frost_ManaRecharge'},
  ['Sol L']={t=3000,icon='Spell_Frost_ManaRecharge'},
  ['Sol M']={t=3000,icon='Spell_Frost_ManaRecharge'},
  ['Sol U']={t=3000,icon='Spell_Frost_ManaRecharge'},
  ['Sonnenwendwürstchen']={t=1000,icon='INV_Misc_Food_53'},
  ['Spalten']={t=2500,icon='Ability_Warrior_Cleave'},
  ['Spinndrüsen-Opferung']={t=10000,icon='Temp'},
  ['Spinnengott beschwören']={t=10000,icon='Spell_Shadow_LifeDrain'},
  ['Spitzer Stachel']={t=500,icon='Ability_ImpalingBolt'},
  ['Splittersteintroggverkleidung']={t=3000,icon='Ability_Rogue_Disguise'},
  ['Springblitzschlag']={t=3000,icon='Spell_Nature_Lightning'},
  ['Spukgeister']={t=2000,icon='Spell_Shadow_BlackPlague'},
  ['Spukphantome']={t=2000,icon='Spell_Shadow_BlackPlague'},
  ['Stachelsalve']={t=500,icon='Ability_ImpalingBolt'},
  ['Stahlroboschreiter']={t=3000,icon='Ability_Mount_MechaStrider'},
  ['Stalvans Fluch']={t=1000,icon='Spell_Shadow_ShadowPact'},
  ['Starke Schmerzen']={t=1000,icon='Spell_Shadow_ShadowWordPain'},
  ['Starren']={t=500,icon='Temp'},
  ['Staubwolke']={t=1500,icon='Ability_Hibernation'},
  ['Steif frieren']={t=2500,icon='Spell_Frost_Glacier'},
  ['Steinhaut']={t=6000,icon='Spell_Nature_EnchantArmor'},
  ['Steinschwingengargoyles beschwören']={t=10000,icon='Spell_Shadow_UnsummonBuilding'},
  ['Steinzwerg wecken\'-Bild']={t=1500,icon='Spell_Nature_Earthquake'},
  ['Sternenfeuer']={t=3500,icon='Spell_Arcane_StarFire'},
  ['Stichschatten']={t=2000,icon='Spell_Shadow_ChillTouch'},
  ['Stille']={t=1500,icon='Spell_Holy_Silence'},
  ['Stinkbombe neutralisieren']={t=5000,icon='Temp'},
  ['Stinkbombe werfen']={t=2000,icon='INV_Misc_Bowl_01'},
  ['Strahlungsblitz']={t=3000,icon='Spell_Shadow_CorpseExplode'},
  ['Strebenaktivierung']={t=5000,icon='Temp'},
  ['Streitross beschwören']={t=3000,icon='Ability_Mount_Charger'},
  ['Streitwidder der Stormpike']={t=3000,icon='Ability_Mount_MountainRam'},
  ['Stumpfe Waffe erweitern']={t=3000,icon='Temp'},
  ['Stumpfe Waffe erweitern II']={t=3000,icon='Temp'},
  ['Stumpfe Waffe erweitern III']={t=3000,icon='Temp'},
  ['Stumpfe Waffe erweitern IV']={t=3000,icon='Temp'},
  ['Stumpfe Waffe intensivieren V']={t=3000,icon='Temp'},
  ['Sturmblitz']={t=1000,icon='INV_Hammer_01'},
  ['Stärke der Frostmane']={t=1000,icon='Spell_Nature_UndyingStrength'},
  ['Stärke des Alters']={t=2000,icon='Spell_Shadow_Requiem'},
  ['Störung']={t=3000,icon='Temp'},
  ['Sukkubus beschwören']={t=10000,icon='Spell_Shadow_SummonSuccubus'},
  ['Sumpfbrühschlammer beschwören']={t=2500,icon='Spell_Shadow_BlackPlague'},
  ['Sumpfgeist beschwören']={t=1500,icon='Spell_Nature_AbolishMagic'},
  ['Superkristall']={t=6000,icon='Temp'},
  ['Symbol des Lebens']={t=10000,icon='Spell_Holy_Resurrection'},
  ['Syndikatsbombe']={t=3000,icon='Spell_Shadow_MindBomb'},
  ['Syndikatsverkleidung']={t=3000,icon='Ability_Rogue_Disguise'},
  ['Säuberungstotem herstellen']={t=5000,icon='Spell_Shadow_LifeDrain'},
  ['Säulenerforschung']={t=5000,icon='Temp'},
  ['Säure von Hakkar']={t=1000,icon='Spell_Nature_Acid_01'},
  ['Säurespritzer']={t=2000,icon='Spell_Nature_Acid_01'},
  ['Säurespucke']={t=3000,icon='Spell_Nature_Acid_01'},
  ['Südmeerkanonenfeuer']={t=5000,icon='Spell_Fire_FireBolt02'},
  ['Südmeerpiratenverkleidung']={t=3000,icon='Ability_Rogue_Disguise'},
  ['Süße Träume']={t=1000,icon='INV_ValentinesChocolate03'},
  ['Taelan Tod']={t=2000,icon='Temp'},
  ['Talvashs Halskettenreparatur']={t=4500,icon='Spell_Holy_Restoration'},
  ['Tammras Schössling']={t=1300,icon='Temp'},
  ['Taschendieb (PT)']={t=5000,icon='Temp'},
  ['Tausend Klingen']={t=250,icon='INV-Sword_53'},
  ['Teleporter']={t=1000,icon='Spell_Magic_LesserInvisibilty'},
  ['Teleporter zu Abtei von Nordhain']={t=2000,icon='Temp'},
  ['Teleportieren: Darnassus']={t=10000,icon='Spell_Arcane_TeleportDarnassus'},
  ['Teleportieren: Ironforge']={t=10000,icon='Spell_Arcane_TeleportIronForge'},
  ['Teleportieren: Moonglade']={t=10000,icon='Spell_Arcane_TeleportMoonglade'},
  ['Teleportieren: Orgrimmar']={t=10000,icon='Spell_Arcane_TeleportOrgrimmar'},
  ['Teleportieren: Stormwind']={t=10000,icon='Spell_Arcane_TeleportStormWind'},
  ['Teleportieren: Thunder Bluff']={t=10000,icon='Spell_Arcane_TeleportThunderBluff'},
  ['Teleportieren: Undercity']={t=10000,icon='Spell_Arcane_TeleportUnderCity'},
  ['Temperaturanzeige']={t=2000,icon='Temp'},
  ['Temporalriss versetzen']={t=5000,icon='Temp'},
  ['Terky beschwören']={t=1000,icon='Ability_Seal'},
  ['Termiten fangen']={t=5000,icon='Temp'},
  ['Termitenfass öffnen']={t=5000,icon='Temp'},
  ['Terrorknurren']={t=1000,icon='Ability_Racial_Cannibalize'},
  ['Terrorwolf']={t=3000,icon='Ability_Mount_WhiteDireWolf'},
  ['Tervoshs Diener beschwören']={t=4000,icon='Spell_Frost_Wisp'},
  ['Test Klinge schärfen']={t=3000,icon='Temp'},
  ['Test der Lehre']={t=2000,icon='Spell_Nature_EarthBind'},
  ['Teufelsfluch']={t=4000,icon='Temp'},
  ['Teufelsjäger beschwören']={t=10000,icon='Spell_Shadow_SummonFelHunter'},
  ['Teufelsjäger der Witherbark beschwören']={t=3000,icon='Spell_Shadow_SummonFelHunter'},
  ['Teufelsross beschwören']={t=3000,icon='Spell_Nature_Swiftness'},
  ['Tharnariun-Heilmittel 1']={t=750,icon='Spell_Nature_RemoveDisease'},
  ['Tharnariuns Heilen']={t=500,icon='Spell_Nature_MagicImmunity'},
  ['Thelrin beschwören']={t=1000,icon='Temp'},
  ['Theurgiker beschwören']={t=5000,icon='Spell_Nature_Purge'},
  ['Thistlenettles Zug']={t=2000,icon='Spell_Shadow_Haunting'},
  ['Thoriumgranate']={t=1000,icon='Spell_Fire_SelfDestruct'},
  ['Thules Fluch']={t=2000,icon='Spell_Shadow_ShadowPact'},
  ['Thules Wut']={t=1500,icon='Spell_Shadow_UnholyFrenzy'},
  ['Thunderhorn-Brunnen säubern']={t=10000,icon='Temp'},
  ['Thunds Ruf']={t=1500,icon='Spell_Frost_Wisp'},
  ['Tiefer Schlummer']={t=1000,icon='Spell_Shadow_Cripple'},
  ['Tier besänftigen']={t=1500,icon='Ability_Hunter_BeastSoothe'},
  ['Tier ermächtigen']={t=500,icon='Spell_Shadow_DeathPact'},
  ['Tier freigeben']={t=5000,icon='Spell_Nature_SpiritWolf'},
  ['Tier wiederbeleben']={t=10000,icon='Ability_Hunter_BeastSoothe'},
  ['Tiger']={t=3000,icon='Ability_Mount_JungleTiger'},
  ['Time Stop Visual DND']={t=3000,icon='Temp'},
  ['Timmys Fluch']={t=1000,icon='Spell_Shadow_ShadowPact'},
  ['Tintenschauer']={t=1000,icon='Spell_Nature_Sleep'},
  ['Tod und Verfall']={t=2000,icon='Spell_Shadow_DeathAndDecay'},
  ['Todesminen-Dynamit']={t=2000,icon='Spell_Fire_SelfDestruct'},
  ['Todesstreitross']={t=3000,icon='Ability_Mount_Undeadhorse'},
  ['Totembündel der Witherbark herstellen']={t=2000,icon='Spell_Lightning_LightningBolt01'},
  ['Totenbett']={t=2000,icon='Spell_Shadow_Twilight'},
  ['Totenerweckung']={t=1000,icon='Spell_Shadow_RaiseDead'},
  ['Toxinspeichel']={t=500,icon='Spell_Nature_CorrosiveBreath'},
  ['Toxinspucke']={t=2500,icon='Spell_Nature_CorrosiveBreath'},
  ['Toxinvernebler platzieren']={t=3000,icon='INV_Cask_01'},
  ['Toxischer Blitz']={t=2500,icon='Spell_Nature_CorrosiveBreath'},
  ['Trank trinken']={t=3000,icon='Spell_Holy_Heal'},
  ['Trankwurf']={t=2000,icon='Spell_Misc_Drink'},
  ['Traumesriss heraufbeschwören']={t=10000,icon='Temp'},
  ['Treantteleporter']={t=2000,icon='Temp'},
  ['Treantverbündete beschwören']={t=1500,icon='Spell_Nature_ForceOfNature'},
  ['Trelanes Eiskälteberührung']={t=3000,icon='Spell_Shadow_UnsummonBuilding'},
  ['Triage']={t=7000,icon='Temp'},
  ['Truhe öffnen']={t=5000,icon='Temp'},
  ['Tödliches Gift']={t=3000,icon='Ability_Rogue_DualWeild'},
  ['Tödliches Gift II']={t=3000,icon='Ability_Rogue_DualWeild'},
  ['Tödliches Gift III']={t=3000,icon='Ability_Rogue_DualWeild'},
  ['Tödliches Gift IV']={t=3000,icon='Ability_Rogue_DualWeild'},
  ['Tödliches Gift V']={t=3000,icon='Ability_Rogue_DualWeild'},
  ['Tödliches Toxin+']={t=3000,icon='Spell_Nature_CorrosiveBreath'},
  ['Türkisfarbener Raptor']={t=3000,icon='Ability_Mount_Raptor'},
  ['Uldaman Sub-Boss Agro']={t=5000,icon='Spell_Nature_EarthBindTotem'},
  ['Uldaman-Boss-Aggro']={t=5000,icon='Spell_Nature_EarthBindTotem'},
  ['Uldaman-Schlüsselstab']={t=5000,icon='Temp'},
  ['Umarmung der Witwe']={t=500,icon='Spell_Arcane_Blink'},
  ['Umhang erfüllen - Schwacher Schutz']={t=5000,icon='Spell_Holy_GreaterHeal'},
  ['Umhang erfüllen - Schwacher Widerstand']={t=5000,icon='Spell_Holy_GreaterHeal'},
  ['Umis Yeti freigeben']={t=2000,icon='Temp'},
  ['Umschließende Gespinste']={t=2000,icon='Spell_Nature_EarthBind'},
  ['Unbefeuerte Klinge platzieren']={t=1000,icon='Temp'},
  ['Ungeschick']={t=1000,icon='Spell_Shadow_Fumble'},
  ['Ungeschick II']={t=1000,icon='Spell_Shadow_Fumble'},
  ['Ungeschick III']={t=1000,icon='Spell_Shadow_Fumble'},
  ['Ungeschmiedetes Siegel platzieren']={t=500,icon='Temp'},
  ['Unheiliger Fluch']={t=1000,icon='Spell_Shadow_CurseOfMannoroth'},
  ['Unlackierter Roboschreiter']={t=3000,icon='Ability_Mount_MechaStrider'},
  ['Unsichtbar - Bärenfalle platzieren']={t=2000,icon='Temp'},
  ['Unsichtbarkeit']={t=3000,icon='Spell_Nature_Invisibilty'},
  ['Untergebenen beschwören']={t=500,icon='Temp'},
  ['Untote fesseln']={t=1500,icon='Spell_Nature_Slow'},
  ['Untote vertreiben']={t=1500,icon='Spell_Holy_TurnUndead'},
  ['Untoten Skarabäus erwecken']={t=1000,icon='Spell_Shadow_Contagion'},
  ['Uralte Glyphen zeichnen']={t=10000,icon='Temp'},
  ['Urokdiener verschwinden']={t=2000,icon='Temp'},
  ['Urtumgeister beschwören']={t=2000,icon='Temp'},
  ['Urzeitleopard']={t=3000,icon='Ability_Mount_JungleTiger'},
  ['Uthers Tribut']={t=2000,icon='Temp'},
  ['Verbannen']={t=1500,icon='Spell_Shadow_Cripple'},
  ['Verbreitetes Kätzchen beschwören']={t=2000,icon='INV_Box_PetCarrier_01'},
  ['Verbrennen']={t=2000,icon='Spell_Shadow_ChillTouch'},
  ['Verbrennung']={t=5000,icon='Temp'},
  ['Verdammnisgebell\' lehren']={t=1000,icon='Temp'},
  ['Verderbnis']={t=2000,icon='Spell_Shadow_AbominationExplosion'},
  ['Verderbten Brühschlammer freigeben']={t=5000,icon='INV_Potion_19'},
  ['Verderbter Redpath']={t=2000,icon='Temp'},
  ['Verderbtes Kätzchen beschwören']={t=1000,icon='Ability_Seal'},
  ['Verfallene Beweglichkeit']={t=2000,icon='Spell_Holy_HarmUndeadAura'},
  ['Verfallene Stärke']={t=2000,icon='Spell_Holy_HarmUndeadAura'},
  ['Verführung']={t=1500,icon='Spell_Shadow_MindSteal'},
  ['Verführungsdrüse anwenden']={t=2500,icon='INV_Misc_Bowl_01'},
  ['Vergebung']={t=1000,icon='Temp'},
  ['Vergiftete Harpune']={t=2000,icon='Ability_Poisons'},
  ['Vergifteter Schuss']={t=2000,icon='Ability_Poisons'},
  ['Vergrößern']={t=2000,icon='Spell_Nature_Strength'},
  ['Verheerer der Magram beschwören']={t=4000,icon='Spell_Shadow_RaiseDead'},
  ['Verhexung']={t=2000,icon='Spell_Nature_Polymorph'},
  ['Verhexung von Rabenklaue']={t=2000,icon='Spell_Shadow_Charm'},
  ['Verigans Faust schmieden']={t=600,icon='Spell_Holy_RighteousFury'},
  ['Verkleidung als Hexer von Dalaran']={t=3000,icon='Ability_Rogue_Disguise'},
  ['Verkrüppeln']={t=3000,icon='Spell_Shadow_Cripple'},
  ['Verkrüppelndes Gift']={t=3000,icon='Ability_PoisonSting'},
  ['Verlangsamendes Gift']={t=1000,icon='Spell_Nature_SlowPoison'},
  ['Verlassene Fertigkeiten']={t=2500,icon='Spell_Shadow_AntiShadow'},
  ['Verschmelzende Brühschlammer']={t=500,icon='INV_Potion_12'},
  ['Versengen']={t=1500,icon='Spell_Fire_SoulBurn'},
  ['Versteinert - Kanalisierung wirken - Bild']={t=3000,icon='Spell_Nature_Cyclone'},
  ['Verteidigung aktivieren']={t=5000,icon='Temp'},
  ['Verwahrungskasten herstellen']={t=500,icon='Temp'},
  ['Verwandlung']={t=1500,icon='Spell_Nature_Polymorph'},
  ['Verwandlung: Kuh']={t=1500,icon='Spell_Nature_Polymorph_Cow'},
  ['Verwandlung: Schildkröte']={t=1500,icon='Ability_Hunter_Pet_Turtle'},
  ['Verwandlung: Schwein']={t=1500,icon='Spell_Magic_PolymorphPig'},
  ['Verzauberndes Schlaflied']={t=1000,icon='Spell_Shadow_SoothingKiss'},
  ['Verzauberte Schnelligkeit']={t=3000,icon='Spell_Nature_Invisibilty'},
  ['Verzauberter Resonitkristall']={t=5000,icon='Temp'},
  ['Verzaubertes Gaeasamenkorn']={t=5000,icon='Temp'},
  ['Violetter Raptor']={t=3000,icon='Ability_Mount_Raptor'},
  ['Viper beschwören']={t=1500,icon='Spell_Nature_ResistMagic'},
  ['Vipore-Katzengestalt DND']={t=1000,icon='Temp'},
  ['Vipores Signal platzieren']={t=5000,icon='Temp'},
  ['Vision hervorrufen']={t=30000,icon='Spell_Shadow_LifeDrain'},
  ['Vollständige Heilung']={t=1000,icon='Temp'},
  ['Vom Azshara-Turm teleportieren']={t=1000,icon='Spell_Nature_EarthBind'},
  ['Voodoo']={t=1000,icon='Spell_Shadow_AntiShadow'},
  ['Voodooverhexung']={t=1000,icon='Spell_Shadow_CurseOfMannoroth'},
  ['Vorführraum - Studenten transformieren']={t=1000,icon='Temp'},
  ['Waffe erfüllen - Wildtiertöter']={t=5000,icon='Spell_Holy_GreaterHeal'},
  ['Waffe schärfen - Kritisch']={t=3000,icon='Temp'},
  ['Wagen beschädigen']={t=2000,icon='Spell_Fire_Fire'},
  ['Wahre Erfüllung']={t=500,icon='Spell_Shadow_Charm'},
  ['Wahrsageschale herstellen']={t=2500,icon='INV_Misc_Bowl_01'},
  ['Waisenkind rufen']={t=1000,icon='Ability_Seal'},
  ['Waldfrosch beschwören']={t=1000,icon='Ability_Seal'},
  ['Waldkaninchen beschwören']={t=1000,icon='Ability_Seal'},
  ['Wandernde Seuche']={t=2000,icon='Spell_Shadow_CallofBone'},
  ['Wappen der Vergeltung']={t=1000,icon='INV_Shield_19'},
  ['Waroshs Transformation']={t=1000,icon='Temp'},
  ['Wasser der Seher herstellen']={t=5000,icon='Spell_Shadow_LifeDrain'},
  ['Wasserblase']={t=1000,icon='Spell_Frost_Wisp'},
  ['Wasserelementar beschwören']={t=2000,icon='Spell_Shadow_SealOfKings'},
  ['Wehklagen der Banshee']={t=2000,icon='Spell_Shadow_Possession'},
  ['Weisheit der Winterax']={t=1000,icon='Ability_Ambush'},
  ['Weiter Rundschlag']={t=4000,icon='Ability_Whirlwind'},
  ['Weißer Roboschreiter']={t=3000,icon='Ability_Mount_MechaStrider'},
  ['Weißer Widder']={t=3000,icon='Ability_Mount_MountainRam'},
  ['Weißes Kätzchen beschwören']={t=1000,icon='Ability_Seal'},
  ['Weißes Plymouth-Rock-Huhn beschwören']={t=1000,icon='Ability_Seal'},
  ['Weißes Tigerbaby beschwören']={t=1000,icon='Ability_Seal'},
  ['Welkberührung']={t=2000,icon='Spell_Nature_Drowsy'},
  ['Welken']={t=1500,icon='Spell_Nature_NullifyDisease'},
  ['Welle der Heilung']={t=3000,icon='Spell_Nature_MagicImmunity'},
  ['Welle der Heilung von Antu\'sul']={t=1000,icon='Spell_Holy_Heal02'},
  ['Wellenbrecher']={t=2000,icon='Spell_Frost_FrostNova'},
  ['Werwolfirrbild beschwören']={t=1000,icon='Spell_Shadow_Teleport'},
  ['Werwolfschrecken rufen']={t=1300,icon='Spell_Shadow_ChillTouch'},
  ['Westfall-Teleporter']={t=2000,icon='Temp'},
  ['Wichtel beschwören']={t=10000,icon='Spell_Shadow_SummonImp'},
  ['Wichtel freigeben']={t=2000,icon='Temp'},
  ['Wiedergeburt']={t=2000,icon='Spell_Nature_Reincarnation'},
  ['Wilde Regeneration']={t=3000,icon='Spell_Nature_Rejuvenation'},
  ['Wildgeist']={t=3000,icon='Spell_Nature_SpiritWolf'},
  ['Wildgeist II']={t=3000,icon='Spell_Nature_SpiritWolf'},
  ['Wildmane-Brunnen säubern']={t=10000,icon='Temp'},
  ['Wildtier ängstigen']={t=1500,icon='Ability_Druid_Cower'},
  ['Wildtierklauen']={t=1000,icon='Spell_Nature_Regeneration'},
  ['Wildtierklauen II']={t=1000,icon='Spell_Nature_Regeneration'},
  ['Wildtierklauen III']={t=1000,icon='Spell_Nature_Regeneration'},
  ['Wille von Shahram']={t=1000,icon='Spell_Holy_MindVision'},
  ['Willensverfall']={t=2000,icon='Spell_Holy_HarmUndeadAura'},
  ['Windsor Tod DND']={t=1500,icon='Temp'},
  ['Windsor liest Schrifttafeln DND']={t=10000,icon='Temp'},
  ['Windstoß']={t=2000,icon='Spell_Nature_EarthBind'},
  ['Winnas Kätzchen freigeben']={t=1000,icon='Ability_Seal'},
  ['Winterhoof-Brunnen säubern']={t=10000,icon='Temp'},
  ['Winterschlaf']={t=1500,icon='Spell_Nature_Sleep'},
  ['Winterspringfrostsäbler']={t=3000,icon='Ability_Mount_PinkTiger'},
  ['Winterwolf']={t=3000,icon='Ability_Mount_WhiteDireWolf'},
  ['Winzigen Grünen Drachen beschwören']={t=1000,icon='Ability_Seal'},
  ['Winzigen Roten Drachen beschwören']={t=1000,icon='Ability_Seal'},
  ['Wirbelndes Sperrfeuer']={t=1500,icon='INV_Spear_05'},
  ['Wird aufgefüllt']={t=3000,icon='Temp'},
  ['Witz erzählen']={t=2000,icon='Spell_Shadow_LifeDrain'},
  ['Worgwelpen beschwören']={t=1000,icon='Ability_Seal'},
  ['Worgwelpen einfangen']={t=2500,icon='Temp'},
  ['Wort des Tauens']={t=5000,icon='Temp'},
  ['Worte von Celebras rezitieren']={t=3000,icon='Temp'},
  ['Wucherwurzeln']={t=1500,icon='Spell_Nature_StrangleVines'},
  ['Wundgift']={t=3000,icon='INV_Misc_Herb_16'},
  ['Wurmschwung']={t=1000,icon='INV_Misc_MonsterScales_05'},
  ['Würgeseuche']={t=2000,icon='Spell_Shadow_CallofBone'},
  ['Xorothianisches Schreckensross herbeirufen']={t=5000,icon='Temp'},
  ['Yennikus Befreiung']={t=4000,icon='Spell_Shadow_LifeDrain'},
  ['Zauber \'Arugal einbrüten\'']={t=2000,icon='Temp'},
  ['Zauber-Ablenkung (NYI)']={t=1000,icon='Temp'},
  ['Zauberwache beschwören']={t=5000,icon='Spell_Nature_Purge'},
  ['Zauberöl']={t=3000,icon='Temp'},
  ['Zeichenset']={t=7000,icon='Temp'},
  ['Zeitraffer']={t=2000,icon='Spell_Arcane_PortalOrgrimmar'},
  ['Zeitstopp']={t=3000,icon='Temp'},
  ['Zelt zerstören']={t=2500,icon='Temp'},
  ['Zergling beschwören']={t=1000,icon='Ability_Seal'},
  ['Zerschmettern']={t=1500,icon='Ability_Warrior_DecisiveStrike'},
  ['Zeug abbrechen']={t=2000,icon='Spell_Shadow_CurseOfAchimonde'},
  ['Zombie beschwören']={t=2000,icon='Spell_Shadow_RaiseDead'},
  ['Zorn']={t=2000,icon='Spell_Nature_AbolishMagic'},
  ['Zornklaue freigeben']={t=10000,icon='Temp'},
  ['Zünder für Feuerwerksraketen herstellen']={t=2000,icon='INV_Musket_04'},
  ['Zünder für Raketenbündel herstellen']={t=2000,icon='INV_Misc_EngGizmos_03'},
  ['[PH] Nach Auberdine teleportieren']={t=2000,icon='Temp'},
  ['[PH] Nach Booty Bay teleportieren']={t=2000,icon='Temp'},
  ['[PH] Nach Grom\'Gol teleportieren']={t=2000,icon='Temp'},
  ['[PH] Nach Orgrimmar teleportieren']={t=2000,icon='Temp'},
  ['[PH] Nach Ratchet teleportieren']={t=2000,icon='Temp'},
  ['[PH] Nach Theramore teleportieren']={t=2000,icon='Temp'},
  ['[PH] Nach Undercity teleportieren']={t=2000,icon='Temp'},
  ['[PH] Zu Balthule teleportieren']={t=2000,icon='Temp'},
  ['[PH] Zum Hafen von Menethil teleportieren']={t=2000,icon='Temp'},
  ['[PH] Zum Teufelswald teleportieren']={t=2000,icon='Temp'},
  ['Ätzende Giftspucke']={t=2500,icon='Spell_Nature_CorrosiveBreath'},
  ['Ätzende Säure']={t=1500,icon='Spell_Nature_Acid_01'},
  ['Ätzende Säurespucke']={t=3000,icon='Spell_Nature_Acid_01'},
  ['Ätzgift']={t=1500,icon='Spell_Nature_CorrosiveBreath'},
  ['Öffnen']={t=5000,icon='Temp'},
  ['Öffnung']={t=5000,icon='Temp'},
  ['Übergießen']={t=5000,icon='Temp'},
  ['Überragender Heilungszauberschutz']={t=2000,icon='Spell_Holy_LayOnHands'},
}

pfUI_locale["deDE"]["debuffs"] = {
  ['AE Charm']={[0]=300.0,},
  ['Ablenkende Spucke']={[0]=15.0,},
  ['Ablenkender Schmerz']={[0]=15.0,},
  ['Ablenkungsbewegung']={[0]=7.0,},
  ['Abschreckknurren']={[0]=30.0,},
  ['Absitzschuss']={[0]=2.0,},
  ['Abstoßendes Starren']={[0]=8.0,},
  ['Abtrennung']={[0]=300.0,},
  ['Adlerklaue']={[0]=15.0,},
  ['Alle Fesseln entfernen']={[0]=1.0,},
  ['Alptraumkreatur']={[0]=30.0,},
  ['Angriff des Frostbrands']={[0]=8.0,},
  ['Angriffsreihenfolge']={[0]=10.0,},
  ['Anspringblutung']={[0]=18.0,},
  ['Anspringen']={[0]=2.0,},
  ['Anub\'Rekhans Aura']={[0]=5.0,},
  ['Arantir\'s Zorn']={[0]=6.0,},
  ['Arkane Geschosse']={[1]=3.0,[2]=4.0,[3]=5.0,[4]=5.0,[5]=5.0,[6]=5.0,[7]=5.0,[8]=5.0,[0]=5.0,},
  ['Arkane Intelligenz']={[0]=1800.0,},
  ['Arkane Wucht']={[0]=8.0,},
  ['Arkanschwäche']={[0]=45.0,},
  ['Arlokks Mal']={[0]=120.0,},
  ['Arthas\' Gabe']={[0]=180.0,},
  ['Arugals Fluch']={[0]=10.0,},
  ['Arugals Gabe']={[0]=300.0,},
  ['Aspekt von Arlokk']={[0]=2.0,},
  ['Aspekt von Jeklik']={[0]=5.0,},
  ['Aspekt von Mar\'li']={[0]=6.0,},
  ['Aspekt von Venoxis']={[0]=10.0,},
  ['Auferweckung der Seele']={[0]=1800.0,},
  ['Aufreißungen']={[0]=60.0,},
  ['Aufschlitzen']={[0]=8.0,},
  ['Aufspießen']={[0]=15.0,},
  ['Auge von Immol\'thar']={[0]=4.0,},
  ['Augenhacker']={[0]=12.0,},
  ['Aura der Furcht']={[0]=3.0,},
  ['Aura der Pein']={[0]=8.0,},
  ['Aura des Befehls']={[0]=30.0,},
  ['Aura des Blutfürsten']={[0]=5.0,},
  ['Aura des Kampfes']={[0]=10.0,},
  ['Aura-Absaugen (PT)']={[0]=15.0,},
  ['Auraschock']={[0]=300.0,},
  ['Ausbrennende Flammen']={[0]=900.0,},
  ['Axt werfen']={[0]=2.0,},
  ['Axtwirbel']={[0]=2.0,},
  ['Axtwurf']={[0]=3.0,},
  ['Azrethocs Stampfen']={[0]=5.0,},
  ['Baggererkrankung']={[0]=300.0,},
  ['Balnazzar - Transformieren - Betäuben']={[0]=5.0,},
  ['Bansheefluch']={[0]=12.0,},
  ['Bansheekreischen']={[0]=5.0,},
  ['Bedrohlicher Blick']={[0]=6.0,},
  ['Bedrohliches Knurren']={[0]=30.0,},
  ['Befreite Seele beschwören']={[0]=60.0,},
  ['Befrieden']={[0]=10.0,},
  ['Beherrschung']={[0]=15.0,},
  ['Belästigen']={[1]=10.0,[2]=20.0,[3]=30.0,[0]=30.0,},
  ['Benebelnder Schmerz']={[0]=10.0,},
  ['Benommen']={[0]=4.0,},
  ['Berauschendes Gift']={[0]=120.0,},
  ['Berittenen Ritter beschwören']={[0]=120.0,},
  ['Berserker']={[0]=30.0,},
  ['Berührung der Schwäche']={[0]=120.0,},
  ['Berührung des Flammenschockers']={[0]=3.0,},
  ['Berührung von Rabenklaue']={[0]=5.0,},
  ['Besitz ergreifen']={[0]=120.0,},
  ['Bestrahlt']={[0]=60.0,},
  ['Besudelte Gedanken']={[0]=600.0,},
  ['Besudeltes Blut']={[0]=10.0,},
  ['Betäuben']={[0]=2.0,},
  ['Betäubender Schlag']={[1]=3.0,[2]=5.0,[0]=5.0,},
  ['Betäubender Stoß']={[0]=4.0,},
  ['Betäubung abfangen']={[0]=3.0,},
  ['Beweglichkeit VIII']={[0]=3600.0,},
  ['Biss der Fäulnis']={[0]=30.0,},
  ['Blackout']={[0]=3.0,},
  ['Blenden']={[0]=10.0,},
  ['Blinzeln']={[0]=1.0,},
  ['Blitzeis']={[0]=5.0,},
  ['Blutblütengift']={[0]=30.0,},
  ['Blutfluch']={[0]=600.0,},
  ['Blutgeheul']={[0]=15.0,},
  ['Blutheilung']={[0]=6.0,},
  ['Blutsauger']={[0]=5.0,},
  ['Blutsturz']={[0]=15.0,},
  ['Bluttrinker']={[0]=8.0,},
  ['Blutung']={[0]=6.0,},
  ['Bodenbeben']={[0]=2.0,},
  ['Bodenzerkracher']={[0]=3.0,},
  ['Brandungskriecher zähmen']={[0]=900.0,},
  ['Brandzeichen der Schädelesse']={[0]=30.0,},
  ['Brennende Winde']={[0]=8.0,},
  ['Brennendes Adrenalin']={[0]=20.0,},
  ['Brunnen des Lichts']={[0]=180.0,},
  ['Brutgebrechen: Blau']={[0]=600.0,},
  ['Brutgebrechen: Bronze']={[0]=600.0,},
  ['Brutgebrechen: Grün']={[0]=600.0,},
  ['Brutgebrechen: Rot']={[0]=600.0,},
  ['Brutgebrechen: Schwarz']={[0]=600.0,},
  ['Brutkraft: Blau']={[0]=6.0,},
  ['Brutkraft: Bronze']={[0]=5.0,},
  ['Brutkraft: Grün']={[0]=6.0,},
  ['Brutkraft: Rot']={[0]=5.0,},
  ['Buße']={[0]=6.0,},
  ['Chemie-Schälung']={[0]=10.0,},
  ['Chromatische Mutation']={[0]=300.0,},
  ['Dampfstrahl']={[0]=10.0,},
  ['Demoralisieren']={[0]=30.0,},
  ['Demoralisierender Ruf']={[0]=30.0,},
  ['Demoralisierendes Gebrüll']={[0]=30.0,},
  ['Demoralisierungsruf']={[0]=30.0,},
  ['Der Segen von Nordrassil']={[0]=10.0,},
  ['Detonierendes Mana']={[0]=5.0,},
  ['Dezimieren']={[0]=3.0,},
  ['Dickflüssigkeitsfieber']={[0]=600.0,},
  ['Dirks intelligentes Gift']={[0]=30.0,},
  ['Diskombobulieren']={[0]=12.0,},
  ['Donnerknall']={[1]=10.0,[2]=14.0,[3]=18.0,[4]=22.0,[5]=26.0,[6]=30.0,[0]=30.0,},
  ['Donnerkracher']={[0]=2.5,},
  ['Donnerschock']={[0]=5.0,},
  ['Dornen']={[0]=600.0,},
  ['Dornenfluch']={[0]=180.0,},
  ['Dornensalve']={[0]=2.0,},
  ['Drohende Verdammnis']={[0]=10.0,},
  ['Drohruf']={[0]=8.0,},
  ['Druckwelle']={[0]=6.0,},
  ['Druidenschlummer']={[0]=15.0,},
  ['Dröhnendes Gebrüll']={[0]=3.0,},
  ['Dunkle Energie']={[0]=300.0,},
  ['Dunkle Seuche']={[0]=90.0,},
  ['Dunkler Schlamm']={[0]=300.0,},
  ['Durchbohren']={[0]=9.0,},
  ['Durchdringendes Heulen']={[0]=6.0,},
  ['Durchdringendes Kreischen']={[0]=6.0,},
  ['Dämon zerschmettern']={[0]=5.0,},
  ['Dämonengabel']={[0]=25.0,},
  ['Dämonensklave']={[0]=300.0,},
  ['Eberangriff']={[0]=1.0,},
  ['Egelgift']={[0]=40.0,},
  ['Eierexplosion']={[0]=3.0,},
  ['Einfangen']={[0]=5.0,},
  ['Einhüllend']={[0]=6.0,},
  ['Einhüllende Flammen']={[0]=6.0,},
  ['Einhüllende Gespinste']={[0]=8.0,},
  ['Einhüllende Winde']={[0]=10.0,},
  ['Einhüllendes Gespinst']={[0]=8.0,},
  ['Einlullendes Gift']={[0]=8.0,},
  ['Einschlag']={[0]=2.0,},
  ['Einschüchterndes Gebrüll']={[0]=8.0,},
  ['Einschüchterndes Knurren']={[0]=5.0,},
  ['Einschüchterung']={[0]=3.0,},
  ['Einsperren']={[0]=30.0,},
  ['Einstechen']={[0]=10.0,},
  ['Eisblitz']={[0]=2.0,},
  ['Eisblitzschutz']={[0]=5.0,},
  ['Eisgrabmal']={[0]=10.0,},
  ['Eisiger Griff']={[0]=5.0,},
  ['Eiskalte Berührung']={[0]=8.0,},
  ['Eiskalter Atem']={[0]=12.0,},
  ['Eisketten']={[1]=15.0,[2]=20.0,[0]=20.0,},
  ['Eisklaue']={[0]=6.0,},
  ['Eisklauenbären zähmen']={[0]=20.0,},
  ['Eiskälte']={[0]=15.0,},
  ['Eiskälte III PROC']={[0]=5.0,},
  ['Eiskältefalle']={[1]=10.0,[2]=15.0,[3]=20.0,[0]=20.0,},
  ['Eiskälteklaue']={[0]=5.0,},
  ['Eiskühlenova']={[0]=10.0,},
  ['Eisnova']={[0]=2.0,},
  ['Eisschlag']={[0]=10.0,},
  ['Eiszapfen']={[0]=10.0,},
  ['Eitrige Galle']={[0]=45.0,},
  ['Eitriger Atem']={[0]=30.0,},
  ['Eitriger Gestank']={[0]=10.0,},
  ['Eitriges Enzym']={[0]=300.0,},
  ['Ektoplasmadestillierer']={[0]=3.0,},
  ['Elektrifiziertes Netz']={[0]=10.0,},
  ['Elementarfeuer']={[0]=8.0,},
  ['Elementarverwundbarkeit']={[0]=30.0,},
  ['Elende Kälte']={[0]=120.0,},
  ['Elixier der Riesen']={[0]=1200.0,},
  ['Entkräften']={[0]=120.0,},
  ['Entkräftende Berührung']={[0]=120.0,},
  ['Entkräftender Sturmangriff']={[0]=8.0,},
  ['Entscheidende Wunde']={[0]=8.0,},
  ['Entwaffnen']={[0]=10.0,},
  ['Entweihende Aura']={[0]=5.0,},
  ['Entweiht']={[0]=10.0,},
  ['Entzünden']={[0]=4.0,},
  ['Erbarmungsloses Gift']={[0]=120.0,},
  ['Erdbindung']={[0]=5.0,},
  ['Erdbohrersäure']={[0]=30.0,},
  ['Erdengriff']={[0]=4.0,},
  ['Erdrosseln']={[0]=18.0,},
  ['Erdschock']={[0]=2.0,},
  ['Erdstampfer']={[0]=5.0,},
  ['Erfrierung']={[0]=5.0,},
  ['Erkrankte Spucke']={[0]=10.0,},
  ['Ermutigung']={[0]=1800.0,},
  ['Erschauerndes Gebrüll']={[0]=5.0,},
  ['Erschrecken']={[0]=4.0,},
  ['Erschreckendes Gebrüll']={[0]=5.0,},
  ['Erschütternder Schlag']={[0]=5.0,},
  ['Erschütternder Schuss']={[0]=4.0,},
  ['Erwachsenen Ebenenschreiter zähmen']={[0]=900.0,},
  ['Erz schmelzen']={[0]=20.0,},
  ['Eskhandars Krallenhieb']={[0]=30.0,},
  ['Essenz des Roten']={[0]=180.0,},
  ['Essenz extrahieren']={[0]=12.0,},
  ['Explodieren']={[0]=2.5,},
  ['Fackelexplosion']={[0]=30.0,},
  ['Fackelwurf']={[0]=30.0,},
  ['Falle']={[0]=10.0,},
  ['Fangzahn der Kristallspinne']={[0]=10.0,},
  ['Fassexplosion']={[0]=3.0,},
  ['Faultier']={[0]=3.0,},
  ['Faust des Ragnaros']={[0]=5.0,},
  ['Feenfeuer']={[0]=40.0,},
  ['Feenfeuer (Tiergestalt)']={[0]=40.0,},
  ['Fehlgeschlagene Verwandlung']={[0]=8.0,},
  ['Felsen']={[0]=10.0,},
  ['Festes Kneifen']={[0]=5.0,},
  ['Festfrieren']={[0]=10.0,},
  ['Feuerball']={[1]=4.0,[2]=6.0,[3]=6.0,[4]=8.0,[5]=8.0,[6]=8.0,[7]=8.0,[8]=8.0,[9]=8.0,[10]=8.0,[11]=8.0,[12]=8.0,[0]=8.0,},
  ['Feuerbrand']={[0]=15.0,},
  ['Feuerbrandfalle']={[0]=15.0,},
  ['Feuerfestfuror']={[0]=3600.0,},
  ['Feuerfestverstärkung']={[0]=3600.0,},
  ['Feuermeer']={[0]=30.0,},
  ['Feuerschutz']={[0]=3600.0,},
  ['Feuerschwall']={[0]=3.0,},
  ['Feuerschwäche']={[0]=45.0,},
  ['Feuerverwundbarkeit']={[0]=30.0,},
  ['Fieberhafte Erschöpfung']={[0]=1800.0,},
  ['Fieberseuche']={[0]=180.0,},
  ['Fieser Trick']={[0]=4.0,},
  ['Finkle-Generator beschwören']={[0]=5.0,},
  ['Fixieren']={[0]=10.0,},
  ['Flammen anstacheln']={[0]=60.0,},
  ['Flammen verstärken']={[0]=30.0,},
  ['Flammenpeitsche']={[0]=45.0,},
  ['Flammenpuffer']={[0]=45.0,},
  ['Flammenschock']={[0]=12.0,},
  ['Flasche Gift']={[0]=30.0,},
  ['Fleddern']={[0]=2.0,},
  ['Fleisch entzünden']={[0]=60.0,},
  ['Fleisch zerreißen']={[0]=12.0,},
  ['Fluch der Besserung']={[0]=180.0,},
  ['Fluch der Elemente']={[0]=300.0,},
  ['Fluch der Erschöpfung']={[0]=12.0,},
  ['Fluch der Feuerbrand']={[0]=300.0,},
  ['Fluch der Heilung']={[0]=180.0,},
  ['Fluch der Herzschinder']={[0]=180.0,},
  ['Fluch der Machtlosigkeit']={[0]=120.0,},
  ['Fluch der Pein']={[0]=24.0,},
  ['Fluch der Rache']={[0]=900.0,},
  ['Fluch der Schatten']={[0]=300.0,},
  ['Fluch der Schreckensfelsoger']={[0]=60.0,},
  ['Fluch der Schwäche']={[0]=120.0,},
  ['Fluch der Seuchenratte']={[0]=14.0,},
  ['Fluch der Sprachen']={[0]=30.0,},
  ['Fluch der Stämme']={[0]=1800.0,},
  ['Fluch der Tollkühnheit']={[0]=120.0,},
  ['Fluch der Torheit']={[0]=120.0,},
  ['Fluch der Totenwaldfelle']={[0]=120.0,},
  ['Fluch der Verdammnis']={[0]=60.0,},
  ['Fluch der Zungen']={[0]=15.0,},
  ['Fluch des Auges']={[0]=120.0,},
  ['Fluch des Dunkelmeisters']={[0]=60.0,},
  ['Fluch des Elementarlords']={[0]=60.0,},
  ['Fluch des Seuchenfürsten']={[0]=10.0,},
  ['Fluch des gefallenen Magram']={[0]=900.0,},
  ['Fluch von Hakkar']={[0]=120.0,},
  ['Fluch von Tuten\'kash']={[0]=900.0,},
  ['Flüchtige Infektion']={[0]=120.0,},
  ['Flüssiges Metall']={[0]=15.0,},
  ['Frost']={[0]=10.0,},
  ['Frost Nova']={[0]=8.0,},
  ['Frostaura']={[0]=5.0,},
  ['Frostbeulen']={[0]=15.0,},
  ['Frostblitz']={[1]=5.0,[2]=6.0,[3]=6.0,[4]=7.0,[5]=7.0,[6]=8.0,[7]=8.0,[8]=9.0,[9]=9.0,[10]=9.0,[11]=9.0,[0]=9.0,},
  ['Frostblitz-Salve']={[0]=8.0,},
  ['Frostblitzsalve']={[0]=4.0,},
  ['Frostnova']={[0]=8.0,},
  ['Frostschock']={[0]=8.0,},
  ['Frostschuss']={[0]=10.0,},
  ['Frostschutz']={[0]=3600.0,},
  ['Frostschwäche']={[0]=45.0,},
  ['Furcht']={[1]=10.0,[2]=15.0,[3]=20.0,[0]=20.0,},
  ['Furcht (NYI)']={[0]=15.0,},
  ['Furchterregendes Kreischen']={[0]=6.0,},
  ['Fässchenfalle']={[0]=2.0,},
  ['Fünf-Fettfinger-Pressur-Herzexplosionstechnik']={[0]=30.0,},
  ['Gallkröten-Infektion']={[0]=180.0,},
  ['Gebrechlichkeit']={[0]=60.0,},
  ['Gedanken beherrschen']={[0]=10.0,},
  ['Gedanken vergiften']={[0]=15.0,},
  ['Gedanken verseuchen']={[0]=600.0,},
  ['Gedankenbeben']={[0]=600.0,},
  ['Gedankenbenebelndes Gift']={[0]=10.0,},
  ['Gedankenbenebelndes Gift II']={[0]=12.0,},
  ['Gedankenbenebelndes Gift III']={[0]=14.0,},
  ['Gedankenbesänftigung']={[0]=15.0,},
  ['Gedankenkontrolle']={[0]=60.0,},
  ['Gedankenschinden']={[0]=3.0,},
  ['Gedankenverfall']={[0]=30.0,},
  ['Gedankenzersplitterung']={[0]=3.0,},
  ['Geflüster des C\'Thun']={[0]=20.0,},
  ['Gegenangriff']={[0]=5.0,},
  ['Gegenzauber']={[0]=10.0,},
  ['Gegenzauber - zum Schweigen gebracht']={[0]=4.0,},
  ['Geheilte Celebrianranke beschwören']={[0]=604800.0,},
  ['Gehennas Fluch']={[0]=300.0,},
  ['Geist anlocken']={[0]=5.0,},
  ['Geist einfangen']={[0]=9.0,},
  ['Geist von Darrowshire beschwören']={[0]=60.0,},
  ['Geist von Mondklaue']={[0]=120.0,},
  ['Geist von Zandalar']={[0]=7200.0,},
  ['Geisterhafter Stoß']={[0]=7.0,},
  ['Gemeinsame Bande']={[0]=4.0,},
  ['Gemütliches Feuer']={[0]=60.0,},
  ['Gepanzerten Skorpid zähmen']={[0]=900.0,},
  ['Gepflanzt']={[0]=3.0,},
  ['Gerechtigkeit des Hochlords']={[0]=5.0,},
  ['Gespinst']={[0]=8.0,},
  ['Gespinst II']={[0]=10.0,},
  ['Gespinst III']={[0]=12.0,},
  ['Gespinstexplosion']={[0]=10.0,},
  ['Gespinstschauer']={[0]=10.0,},
  ['Gesundheit II']={[0]=3600.0,},
  ['Gesundheit wegsaugen']={[0]=15.0,},
  ['Geysir']={[0]=5.0,},
  ['Gezeiten-Glücksbringer']={[0]=3.0,},
  ['Ghulfäulnis']={[0]=600.0,},
  ['Ghulseuche']={[0]=1800.0,},
  ['Gift']={[0]=30.0,},
  ['Gift der Atal\'ai']={[0]=30.0,},
  ['Giftangriff']={[0]=9.0,},
  ['Giftaura']={[0]=12.0,},
  ['Giftblitz']={[0]=10.0,},
  ['Giftblitzsalve']={[0]=10.0,},
  ['Gifthautsekret']={[0]=30.0,},
  ['Giftiger Katalysator']={[0]=120.0,},
  ['Giftiger Stich']={[0]=15.0,},
  ['Giftspucke']={[0]=15.0,},
  ['Giftstachel']={[0]=45.0,},
  ['Giftwolke']={[0]=45.0,},
  ['Gizlocks Attrappe - Spott']={[0]=5.0,},
  ['Gletschergebrüll']={[0]=3.0,},
  ['Gnomen-Gedankenkontrollkappe']={[0]=20.0,},
  ['Gnomentodesstrahl']={[0]=4.0,},
  ['Golemaggs Vertrauen']={[0]=2.0,},
  ['Greifende Ranken']={[0]=10.0,},
  ['Griff des Befehls']={[0]=10.0,},
  ['Grollflosse']={[0]=20.0,},
  ['Großbrand']={[0]=10.0,},
  ['Große Unsichtbarkeit entdecken']={[0]=600.0,},
  ['Große Verwandlung']={[0]=20.0,},
  ['Großen Klippeneber zähmen']={[0]=20.0,},
  ['Großes Arkanelixier']={[0]=1800.0,},
  ['Grässlicher Schrecken']={[0]=5.0,},
  ['Göttlicher Willen']={[0]=1800.0,},
  ['Hagelsturm']={[0]=3.0,},
  ['Hakennetz']={[0]=10.0,},
  ['Hallendes Gebrüll']={[0]=20.0,},
  ['Hammer der Gerechtigkeit']={[1]=3.0,[2]=4.0,[3]=5.0,[4]=6.0,[0]=6.0,},
  ['Hammer des Richters']={[0]=10.0,},
  ['Hand von Ragnaros']={[0]=2.0,},
  ['Hand von Thaurissan']={[0]=5.0,},
  ['Handschnappen']={[0]=8.0,},
  ['Harsche Winde']={[0]=1.0,},
  ['Hass bis Null']={[0]=1.7,},
  ['Heiliger Schlag']={[0]=4.0,},
  ['Heiliges Feuer']={[0]=10.0,},
  ['Heiliges Wort: Seelenstärke']={[0]=1800.0,},
  ['Heiliges Zerreißen']={[0]=60.0,},
  ['Heiligschutz']={[0]=3600.0,},
  ['Heimtückisches Zerfleischen']={[0]=15.0,},
  ['Herausforderndes Gebrüll']={[0]=6.0,},
  ['Herausforderungsruf']={[0]=6.0,},
  ['Herrschaft der Seele']={[0]=60.0,},
  ['Herz von Hakkar Beschwörungskreis herstellen']={[0]=30.0,},
  ['Herz von Hakkar \'Explosion\' herstellen']={[0]=30.0,},
  ['Herz von Hakkar \'Riss\' herstellen']={[0]=30.0,},
  ['Heulende Klinge']={[0]=30.0,},
  ['Heulende Wut']={[0]=300.0,},
  ['Heuschreckenschwarm']={[0]=6.0,},
  ['Hexenmeisterschrecken']={[0]=2.0,},
  ['Hieb']={[1]=2.0,[2]=3.0,[3]=4.0,[0]=4.0,},
  ['Hirnhacker']={[0]=30.0,},
  ['Hirnschaden']={[0]=30.0,},
  ['Hochentwickelte Zielattrappe Spawneffekt']={[0]=10.0,},
  ['Höllenbestiengeist einfangen']={[0]=9.0,},
  ['Immunität']={[0]=1800.0,},
  ['Inferno Effekt']={[0]=2.0,},
  ['Inferno-Muschelschale']={[0]=10.0,},
  ['Infizierte Wunde']={[0]=300.0,},
  ['Infizierter Biss']={[0]=180.0,},
  ['Infiziertes Rückgrat']={[0]=300.0,},
  ['Inkantation der Manifestation']={[0]=600.0,},
  ['Insektenschwarm']={[0]=12.0,},
  ['Intelligenz IX']={[0]=3600.0,},
  ['Irdene Wächter wecken']={[0]=5.0,},
  ['Irrbilder von Jin\'do']={[0]=20.0,},
  ['Isalien beschwören']={[0]=600.0,},
  ['Jarien and Sothos beschwören']={[0]=600.0,},
  ['Jeff Dummy 1']={[0]=30.0,},
  ['Jeff Dummy 2']={[0]=30.0,},
  ['Jucken']={[0]=8.0,},
  ['Juckreiz']={[0]=8.0,},
  ['Jäger episch Anti-Cheat DND']={[0]=60.0,},
  ['Kadaverwürmer']={[0]=600.0,},
  ['Kahlholz-Fluch']={[0]=60.0,},
  ['Kaltauge']={[0]=15.0,},
  ['Kampfbefehl']={[0]=6.0,},
  ['Kampfnetz']={[0]=10.0,},
  ['Kampfrausch']={[0]=15.0,},
  ['Katalysator des Zaraschwarms']={[0]=30.0,},
  ['Ketten von Kel\'Thuzad']={[0]=20.0,},
  ['Klagender Toter']={[0]=6.0,},
  ['Klauengriff']={[0]=4.0,},
  ['Klebriger Teer']={[0]=4.0,},
  ['Kniesehne']={[0]=15.0,},
  ['Knochenverhüttung']={[0]=20.0,},
  ['Knockout']={[0]=6.0,},
  ['Knurren']={[0]=3.0,},
  ['Knöchel durchstechen']={[0]=3.0,},
  ['Kodostampfen']={[0]=3.0,},
  ['Konzentration']={[0]=10.0,},
  ['Kopfkracher']={[0]=20.0,},
  ['Kopfnuss']={[1]=25.0,[2]=35.0,[3]=45.0,[0]=45.0,},
  ['Kopfzerkracher']={[0]=2.0,},
  ['Kormok beschwören']={[0]=600.0,},
  ['Krabblergift']={[0]=300.0,},
  ['Krallenhieb']={[0]=9.0,},
  ['Kranker Brühschleimer']={[0]=120.0,},
  ['Krankheitsschuss']={[0]=300.0,},
  ['Krankheitsstoß']={[0]=20.0,},
  ['Kreischen der Vergangenheit']={[0]=5.0,},
  ['Kreischergeist beschwören']={[0]=120.0,},
  ['Kreuzfahrer-Hammer']={[0]=4.0,},
  ['Kreuzfahrerstoß']={[0]=30.0,},
  ['Kriegsdonner']={[0]=2.0,},
  ['Kristallblick']={[0]=6.0,},
  ['Kristallener Schlummer']={[0]=15.0,},
  ['Kristallschwächer']={[0]=120.0,},
  ['Krückstock des Sklaventreibers']={[0]=30.0,},
  ['Käfer explodieren lassen']={[0]=4.0,},
  ['Käfer mutieren']={[0]=240.0,},
  ['Kälte']={[0]=1.5,},
  ['Kühlung']={[0]=30.0,},
  ['Lange Benommenheit']={[0]=6.0,},
  ['Larvenglibber']={[0]=6.0,},
  ['Lebende Bombe']={[0]=8.0,},
  ['Lebensentzug']={[0]=30.0,},
  ['Lebenssauger']={[0]=12.0,},
  ['Leereblitz']={[0]=10.0,},
  ['Lehrling beschwören']={[0]=230.0,},
  ['Lord Valthalak beschwören']={[0]=600.0,},
  ['Lucifrons Fluch']={[0]=300.0,},
  ['Luftblasen']={[0]=10.0,},
  ['Luftschlinge']={[0]=2.0,},
  ['Lähmendes Gift']={[0]=8.0,},
  ['Lähmendes Toxin']={[0]=60.0,},
  ['Macht von Shahram']={[0]=5.0,},
  ['Madenglibber']={[0]=6.0,},
  ['Madenschleim']={[0]=1800.0,},
  ['Magenta-Kappen-Erkrankung']={[0]=1200.0,},
  ['Magie entdecken']={[0]=120.0,},
  ['Magiereflexion']={[0]=10.0,},
  ['Magmafesseln']={[0]=15.0,},
  ['Magmaspritzer']={[0]=30.0,},
  ['Magmaspucke']={[0]=30.0,},
  ['Mal der Detonation']={[0]=30.0,},
  ['Mal der Flammen']={[0]=120.0,},
  ['Mal der Flammmen']={[0]=120.0,},
  ['Mal der Natur']={[0]=12.0,},
  ['Mal der Wildnis']={[0]=1800.0,},
  ['Mal des Frosts']={[0]=12.0,},
  ['Mal des Jägers']={[0]=120.0,},
  ['Mal von Blaumeux']={[0]=75.0,},
  ['Mal von Kazzak']={[0]=60.0,},
  ['Mal von Korth\'azz']={[0]=75.0,},
  ['Mal von Morgraine']={[0]=75.0,},
  ['Mal von Zeliek']={[0]=75.0,},
  ['Malowns Zerschmetterer']={[0]=2.0,},
  ['Mana entziehen']={[0]=5.0,},
  ['Mana entzünden']={[0]=300.0,},
  ['Manabrand']={[0]=8.0,},
  ['Manaschwäche']={[0]=10.0,},
  ['Marduk den Schwarzen beschwören']={[0]=300.0,},
  ['Marduks Fluch']={[0]=5.0,},
  ['Maschine steuern']={[0]=60.0,},
  ['Massenheilung']={[0]=12.0,},
  ['Massives Beben']={[0]=2.0,},
  ['Matsch schleudern']={[0]=15.0,},
  ['Menge verprügeln']={[0]=5.0,},
  ['Mentale Kontrolle']={[0]=120.0,},
  ['Mirkfallonfungus']={[0]=2700.0,},
  ['Mobilitätsstörung']={[0]=20.0,},
  ['Mondfestglück']={[0]=1800.0,},
  ['Mondfestglück!']={[0]=1800.0,},
  ['Mondfeuer']={[1]=9.0,[2]=12.0,[3]=12.0,[4]=12.0,[5]=12.0,[6]=12.0,[7]=12.0,[8]=12.0,[9]=12.0,[10]=12.0,[0]=12.0,},
  ['Monstrositätenspucke']={[0]=10.0,},
  ['Moosbedeckte Füße']={[0]=180.0,},
  ['Moosbedeckte Hände']={[0]=180.0,},
  ['Mor Grayhoof beschwören']={[0]=600.0,},
  ['Morastiger Matsch']={[0]=5.0,},
  ['Muskelriss']={[0]=5.0,},
  ['Mutagene Injektion']={[0]=10.0,},
  ['Nachtseeles Klagelied']={[0]=15.0,},
  ['Nachwirkung']={[0]=5.0,},
  ['Naralex\' Alptraum']={[0]=15.0,},
  ['Naraxis-Gespinst']={[0]=30.0,},
  ['Naturschutz']={[0]=3600.0,},
  ['Naturschwäche']={[0]=45.0,},
  ['Nekrotisches Gift']={[0]=30.0,},
  ['Net-o-Matik']={[0]=10.0,},
  ['Netz']={[0]=10.0,},
  ['Netzwache']={[0]=20.0,},
  ['Netzwirbel']={[0]=7.0,},
  ['Neutralisieren']={[0]=8.0,},
  ['Niederschlagen']={[0]=2.0,},
  ['Niedriger Prankenhieb']={[0]=12.0,},
  ['Nierenhieb']={[1]=0,[2]=1.0,},
  ['Offene Wunde']={[0]=8.0,},
  ['Ohrenbetäubendes Kreischen']={[0]=8.0,},
  ['Opfern']={[0]=8.0,},
  ['Panik']={[0]=8.0,},
  ['Paralysieren']={[0]=30.0,},
  ['Parasit']={[0]=75.0,},
  ['Peinigender Schmerz']={[0]=15.0,},
  ['Peitsche']={[0]=2.0,},
  ['Peitschen']={[0]=2.0,},
  ['Peitschenkrautumschlingung']={[0]=18.0,},
  ['Peitschenkrautwurzeln']={[0]=15.0,},
  ['Phantomstoß']={[0]=20.0,},
  ['Pilzwucher']={[0]=90.0,},
  ['Pirschenden Nachtsäbler zähmen']={[0]=900.0,},
  ['Präriepirscher zähmen']={[0]=900.0,},
  ['Psychischer Schrei']={[0]=8.0,},
  ['Pyroschlag']={[0]=12.0,},
  ['Rache']={[0]=600.0,},
  ['Rache der Knarzklauen']={[0]=6.0,},
  ['Rache des Flammenschockers']={[0]=2.0,},
  ['Rachebetäubung']={[0]=3.0,},
  ['Rasender Befehl']={[0]=10.0,},
  ['Rasender Sturzflug']={[0]=2.0,},
  ['Rattennova']={[0]=10.0,},
  ['Rauchbombe']={[0]=4.0,},
  ['Rauchwolke']={[0]=3.0,},
  ['Rechtschaffene Schwächung']={[0]=10.0,},
  ['Rechtschaffenes Feuer']={[0]=8.0,},
  ['Reflexionsfeld']={[0]=5.0,},
  ['Regeneration IV']={[0]=3600.0,},
  ['Reinigung']={[0]=2.0,},
  ['Rhahk\'Zor-Zerschmettern']={[0]=3.0,},
  ['Richturteil der Gerechtigkeit']={[0]=10.0,},
  ['Richturteil der Weisheit']={[0]=10.0,},
  ['Richturteil des Kreuzfahrers']={[0]=10.0,},
  ['Richturteil des Lichts']={[0]=10.0,},
  ['Riposte']={[0]=6.0,},
  ['Rissleuchtfeuer']={[0]=60.0,},
  ['Ritter beschwören']={[0]=180.0,},
  ['Ritualkerzenaura']={[0]=6.0,},
  ['Ruf des Grabes']={[0]=60.0,},
  ['Rußschicht']={[0]=10.0,},
  ['Räuderipper']={[0]=30.0,},
  ['Rückhand']={[0]=2.0,},
  ['Rüstung IV']={[0]=3600.0,},
  ['Rüstung auflösen']={[0]=20.0,},
  ['Rüstung durchstechen']={[0]=20.0,},
  ['Rüstung schwächen']={[0]=30.0,},
  ['Rüstung spalten']={[0]=15.0,},
  ['Rüstung zerreißen']={[0]=30.0,},
  ['Rüstung zerschmettern']={[0]=30.0,},
  ['Rüstungsschmelze']={[0]=60.0,},
  ['Rüstungszertrümmerung']={[0]=45.0,},
  ['Sargeras Odem']={[0]=90.0,},
  ['Schaden verstärken']={[0]=10.0,},
  ['Schadenschild']={[0]=10.0,},
  ['Schallexplosion']={[0]=10.0,},
  ['Schattenbefehl']={[0]=15.0,},
  ['Schattenblitz']={[0]=6.0,},
  ['Schattenbrand']={[0]=5.0,},
  ['Schattenflamme']={[0]=10.0,},
  ['Schattenhornfluch']={[0]=300.0,},
  ['Schattenmal']={[0]=15.0,},
  ['Schattenpirscher-Stich']={[0]=5.0,},
  ['Schattenpirscher-Streich']={[0]=5.0,},
  ['Schattenschutz']={[0]=3600.0,},
  ['Schattenschwinges Schatten']={[0]=8.0,},
  ['Schattenschwäche']={[0]=45.0,},
  ['Schattenverwundbarkeit']={[0]=15.0,},
  ['Schattenwort: Schmerz']={[0]=18.0,},
  ['Scheckigen Terroreber zähmen']={[0]=900.0,},
  ['Schildhieb']={[0]=6.0,},
  ['Schildhieb - zum Schweigen gebracht']={[0]=3.0,},
  ['Schildschlag']={[0]=2.0,},
  ['Schlachtgewand der Macht']={[0]=6.0,},
  ['Schlachtruf']={[0]=900.0,},
  ['Schlachtruf der Drachentöter']={[0]=7200.0,},
  ['Schlachtstandarte']={[0]=3.0,},
  ['Schlaf']={[1]=20.0,[2]=30.0,[0]=30.0,},
  ['Schlafwandeln']={[0]=10.0,},
  ['Schlag des Ungleichgewichts']={[0]=6.0,},
  ['Schlamm']={[0]=3.0,},
  ['Schlammflossenfungus']={[0]=8.0,},
  ['Schlammtoxin']={[0]=45.0,},
  ['Schlangenbiss']={[0]=15.0,},
  ['Schleichende Pest']={[0]=20.0,},
  ['Schleichender Schimmelpilz']={[0]=60.0,},
  ['Schleichendes Gift']={[0]=30.0,},
  ['Schleichendes Gift II']={[0]=30.0,},
  ['Schleier der Dunkelheit']={[0]=7.0,},
  ['Schleier des Schattens']={[0]=15.0,},
  ['Schleimblitz']={[0]=6.0,},
  ['Schleimdurchfall']={[0]=1800.0,},
  ['Schleimexplosion']={[0]=5.0,},
  ['Schleimstrahl']={[0]=3.0,},
  ['Schlotternachtsschreck']={[0]=6.0,},
  ['Schmarotzerschlange']={[0]=10.0,},
  ['Schmutz schleudern']={[0]=10.0,},
  ['Schnapptritt']={[0]=2.0,},
  ['Schneeleoparden zähmen']={[0]=900.0,},
  ['Schockwelle']={[0]=2.0,},
  ['Schreckensgeheul']={[1]=10.0,[2]=15.0,[0]=15.0,},
  ['Schreckenskralle']={[0]=60.0,},
  ['Schreckliches Geheul']={[0]=3.0,},
  ['Schreckliches Kreischen']={[0]=4.0,},
  ['Schreddern']={[0]=12.0,},
  ['Schrei']={[0]=4.0,},
  ['Schrumpfen']={[0]=120.0,},
  ['Schrumpfstrahl']={[0]=20.0,},
  ['Schwacher Skorpiongifteffekt']={[0]=60.0,},
  ['Schwaches Gift']={[0]=12.0,},
  ['Schwachsinn']={[0]=30.0,},
  ['Schwachsinn II']={[0]=30.0,},
  ['Schwachsinn III']={[0]=30.0,},
  ['Schwanzpeitscher']={[0]=2.0,},
  ['Schwarze Fäulnis']={[0]=1800.0,},
  ['Schwarzer Pfeil']={[0]=30.0,},
  ['Schwarzer Schlamm']={[0]=120.0,},
  ['Schwere Wunde']={[0]=10.0,},
  ['Schwingen der Verzweiflung']={[0]=6.0,},
  ['Schwäche aufdecken']={[0]=5.0,},
  ['Schwächefieber']={[0]=21.0,},
  ['Schwächende Krankheit']={[0]=30.0,},
  ['Schwärende Bisse']={[0]=1800.0,},
  ['Schwärender Ausschlag']={[0]=1800.0,},
  ['Schädelkracher']={[0]=2.0,},
  ['Seele einfangen']={[0]=60.0,},
  ['Seelen-Siphon']={[0]=10.0,},
  ['Seelenbrand']={[0]=16.0,},
  ['Seelenbrecher']={[0]=30.0,},
  ['Seelendieb']={[0]=15.0,},
  ['Seelenkorruption']={[0]=15.0,},
  ['Seelensauger']={[0]=10.0,},
  ['Seelenzapfer']={[0]=12.0,},
  ['Segen des Kriegshäuptlings']={[0]=3600.0,},
  ['Segen des Siphons']={[0]=30.0,},
  ['Segen des schwarzen Marsches']={[0]=6.0,},
  ['Sehnenriss']={[0]=8.0,},
  ['Sehnenschnitt']={[0]=8.0,},
  ['Sengende Flammen']={[0]=9.0,},
  ['Sengschlag']={[0]=30.0,},
  ['Seuche']={[0]=40.0,},
  ['Seuchennebel']={[0]=8.0,},
  ['Seuchenwolke']={[0]=240.0,},
  ['Shazzrahs Fluch']={[0]=300.0,},
  ['Sicht verdunkeln']={[0]=12.0,},
  ['Sickernde Weide']={[0]=30.0,},
  ['Silithidenpocken']={[0]=1800.0,},
  ['Skorpidgift']={[0]=10.0,},
  ['Skorpidstich']={[0]=20.0,},
  ['Smariss\' Aura']={[0]=10.0,},
  ['Smite-Schmettern']={[0]=3.0,},
  ['Smite-Stampfen']={[0]=10.0,},
  ['Sol L']={[0]=5.0,},
  ['Solarplexus']={[0]=4.0,},
  ['Spinnengift']={[0]=30.0,},
  ['Spinnenkuss']={[0]=10.0,},
  ['Spinnennetz']={[0]=1.0,},
  ['Spott']={[0]=3.0,},
  ['Sprengfalle']={[0]=20.0,},
  ['Springflut']={[0]=4.0,},
  ['Spukgeister']={[0]=300.0,},
  ['Spukphantome']={[0]=300.0,},
  ['Spöttischer Schlag']={[0]=6.0,},
  ['Stachelstich']={[0]=300.0,},
  ['Stalvans Fluch']={[0]=600.0,},
  ['Stampfen']={[0]=10.0,},
  ['Starkes Spalten']={[0]=10.0,},
  ['Stasis']={[0]=15.0,},
  ['Stasis-Test']={[0]=5.0,},
  ['Statische Leitung']={[0]=15.0,},
  ['Staubwolke']={[0]=12.0,},
  ['Steif frieren']={[0]=10.0,},
  ['Sternenfeuerbetäubung']={[0]=3.0,},
  ['Sternensplitter']={[0]=6.0,},
  ['Stich des Flügeldrachen']={[0]=12.0,},
  ['Stichschatten']={[0]=1800.0,},
  ['Stichschuss']={[0]=15.0,},
  ['Stille']={[0]=5.0,},
  ['Stinkfalle']={[0]=120.0,},
  ['Strahlenvergiftung']={[0]=25.0,},
  ['Straucheln']={[0]=3.0,},
  ['Streitkolbenbetäubung']={[0]=3.0,},
  ['Streuschuss']={[0]=4.0,},
  ['Strigidkreischer zähmen']={[0]=900.0,},
  ['Sturmangriff']={[0]=4.0,},
  ['Sturmangriffsbetäubung']={[0]=1.0,},
  ['Sturmblitz']={[0]=5.0,},
  ['Sturmschlag']={[0]=12.0,},
  ['Sturzflieger zähmen']={[0]=900.0,},
  ['Stygischer Griff']={[0]=5.0,},
  ['Stärke entziehen']={[0]=300.0,},
  ['Sul\'thraze']={[0]=15.0,},
  ['Super Schrumpfstrahl']={[0]=20.0,},
  ['Sägebiss']={[0]=30.0,},
  ['Säure von Hakkar']={[0]=60.0,},
  ['Säuresalve']={[0]=25.0,},
  ['Säureschleim']={[0]=30.0,},
  ['Säurespritzer']={[0]=30.0,},
  ['Säurespucke']={[0]=30.0,},
  ['TWEEP']={[0]=11.0,},
  ['Taelans Leiden']={[0]=2.0,},
  ['Terrorknurren']={[0]=15.0,},
  ['Test Strike W35']={[0]=10.0,},
  ['Test Strike W50']={[0]=10.0,},
  ['Test Zerfetzen']={[0]=12.0,},
  ['Tetanus']={[0]=1200.0,},
  ['Teufelshundgeist einfangen']={[0]=9.0,},
  ['Teufelssaurierhaken']={[0]=10.0,},
  ['Teufelstampfen']={[0]=3.0,},
  ['Thelrin beschwören']={[0]=600.0,},
  ['Thistlenettles Zug']={[0]=8.0,},
  ['Thules Fluch']={[0]=240.0,},
  ['Thules Wut']={[0]=120.0,},
  ['Tiefe Wunde']={[0]=12.0,},
  ['Tiefer Schlaf']={[0]=10.0,},
  ['Tiefer Schlummer']={[0]=15.0,},
  ['Tier besänftigen']={[0]=15.0,},
  ['Timmys Fluch']={[0]=60.0,},
  ['Tintenschauer']={[0]=15.0,},
  ['Toben']={[0]=2.5,},
  ['Tod durch Ertrinken']={[0]=300.0,},
  ['Todesmantel']={[0]=3.0,},
  ['Tollkühnes Stürmen']={[0]=30.0,},
  ['Tollwut']={[0]=600.0,},
  ['Tollwütiges Maul']={[0]=30.0,},
  ['Tornado']={[0]=4.0,},
  ['Totenbett']={[0]=10.0,},
  ['Toxinspeichel']={[0]=120.0,},
  ['Toxinverpestung']={[0]=60.0,},
  ['Toxische Salve']={[0]=15.0,},
  ['Treant fangen']={[0]=5.0,},
  ['Trelanes Eiskälteberührung']={[0]=12.0,},
  ['Tritt']={[0]=5.0,},
  ['Tritt - zum Schweigen gebracht']={[0]=2.0,},
  ['Tunnelgräbersäure']={[0]=30.0,},
  ['Tödliche Wunde']={[0]=15.0,},
  ['Tödlicher Stich']={[0]=12.0,},
  ['Tödlicher Stoß']={[0]=10.0,},
  ['Tödliches Egelgift']={[0]=45.0,},
  ['Tödliches Gift']={[0]=12.0,},
  ['Tödliches Gift II']={[0]=12.0,},
  ['Tödliches Gift III']={[0]=12.0,},
  ['Tödliches Gift IV']={[0]=12.0,},
  ['Tödliches Gift V']={[0]=12.0,},
  ['Tödliches Spalten']={[0]=5.0,},
  ['Tödliches Toxin']={[0]=12.0,},
  ['Tödliches Toxin II']={[0]=12.0,},
  ['Tödliches Toxin III']={[0]=12.0,},
  ['Tödliches Toxin IV']={[0]=12.0,},
  ['Tödliches Toxin+']={[0]=180.0,},
  ['Umarmung der Witwe']={[0]=30.0,},
  ['Umschließende Gespinste']={[0]=6.0,},
  ['Umschlingen']={[0]=8.0,},
  ['Unausweichliches Schicksal']={[0]=10.0,},
  ['Unerträgliche Schmerzen']={[0]=180.0,},
  ['Ungeschick']={[0]=30.0,},
  ['Ungeschick II']={[0]=30.0,},
  ['Ungeschick III']={[0]=30.0,},
  ['Ungewollte Transformation']={[0]=30.0,},
  ['Unglaublicher Gestank']={[0]=6.0,},
  ['Unheiliger Fluch']={[0]=12.0,},
  ['Unterbrechen (PT)']={[0]=30.0,},
  ['Untertauchen']={[0]=60.0,},
  ['Untote fesseln']={[1]=30.0,[2]=40.0,[3]=50.0,[0]=50.0,},
  ['Untote vertreiben']={[1]=10.0,[2]=15.0,[3]=20.0,[0]=20.0,},
  ['Uralte Hysterie']={[0]=900.0,},
  ['Uralte Verzweiflung']={[0]=5.0,},
  ['Uralter Schrecken']={[0]=900.0,},
  ['Uups!']={[0]=10.0,},
  ['Vampirumarmung']={[0]=60.0,},
  ['Veknisskatalysator']={[0]=30.0,},
  ['Verbannen']={[1]=20.0,[2]=30.0,[0]=30.0,},
  ['Verbesserte Kniesehne']={[0]=5.0,},
  ['Verbesserter Skorpidstich']={[0]=20.0,},
  ['Verbesserter erschütternder Schuss']={[0]=3.0,},
  ['Verbessertes Zurechtstutzen']={[0]=5.0,},
  ['Verbrennen']={[0]=60.0,},
  ['Verbrühen']={[0]=4.0,},
  ['Verderbnis']={[1]=12.0,[2]=15.0,[3]=18.0,[4]=18.0,[5]=18.0,[6]=18.0,[7]=18.0,[0]=18.0,},
  ['Verderbnis der Erde']={[0]=10.0,},
  ['Verderbte Ausdauer']={[0]=4.0,},
  ['Verderbte Beweglichkeit']={[0]=4.0,},
  ['Verderbte Furcht']={[0]=2.0,},
  ['Verderbte Intelligenz']={[0]=4.0,},
  ['Verderbte Stärke']={[0]=4.0,},
  ['Verdorbene Heilung']={[0]=30.0,},
  ['Verdorbene Totems']={[0]=30.0,},
  ['Verdrehte Reflexion']={[0]=45.0,},
  ['Verfallene Beweglichkeit']={[0]=300.0,},
  ['Verfallene Stärke']={[0]=300.0,},
  ['Verfluchte Klinge']={[0]=20.0,},
  ['Verfluchtes Blut']={[0]=600.0,},
  ['Verführung']={[0]=15.0,},
  ['Vergiftete Harpune']={[0]=60.0,},
  ['Vergifteter Schuss']={[0]=75.0,},
  ['Verheeren']={[0]=2.0,},
  ['Verhexung']={[0]=10.0,},
  ['Verhexung der Schwäche']={[0]=120.0,},
  ['Verhexung von Jammal\'an']={[0]=10.0,},
  ['Verhexung von Rabenklaue']={[0]=30.0,},
  ['Verhinderungsruf']={[0]=60.0,},
  ['Verkrüppeln']={[0]=20.0,},
  ['Verkrüppelndes Gift']={[0]=12.0,},
  ['Verlangsamen']={[1]=10.0,[2]=15.0,[0]=15.0,},
  ['Verlangsamender Brühschlammer']={[0]=10.0,},
  ['Verlangsamendes Gift']={[0]=25.0,},
  ['Verlassene Fertigkeiten']={[0]=300.0,},
  ['Verletzender Schlag']={[0]=5.0,},
  ['Verpestende Fäulnis']={[0]=240.0,},
  ['Verschiedene beschwören']={[0]=600.0,},
  ['Verschlingende Seuche']={[0]=24.0,},
  ['Verschwinden']={[0]=20.0,},
  ['Verseuchung']={[0]=60.0,},
  ['Versklaven']={[0]=15.0,},
  ['Versteinern']={[0]=8.0,},
  ['Verwandlung']={[1]=20.0,[2]=30.0,[3]=40.0,[4]=50.0,[0]=50.0,},
  ['Verwandlung: Huhn']={[0]=10.0,},
  ['Verwandlung: Kuh']={[0]=50.0,},
  ['Verwandlung: Schaf']={[0]=10.0,},
  ['Verwandlung: Schildkröte']={[0]=50.0,},
  ['Verwandlung: Schwein']={[0]=50.0,},
  ['Verwandlungsstrahl']={[0]=4.0,},
  ['Verwesendes Fleisch']={[0]=10.0,},
  ['Verwirrtheit']={[0]=15.0,},
  ['Verwundbar']={[0]=3.0,},
  ['Verwunden']={[1]=9.0,[2]=12.0,[3]=15.0,[4]=18.0,[5]=21.0,[6]=21.0,[7]=21.0,[0]=21.0,},
  ['Verzauberndes Schlaflied']={[0]=10.0,},
  ['Verzehren']={[0]=15.0,},
  ['Verzögerung']={[0]=10.0,},
  ['Vipernbiss']={[0]=8.0,},
  ['Virulentes Gift']={[0]=30.0,},
  ['Voodooverhexung']={[0]=120.0,},
  ['Vorarbeitergift']={[0]=60.0,},
  ['Vorgeschmack des Wahnsinns']={[0]=3.0,},
  ['Waffe ergreifen']={[0]=15.0,},
  ['Wahnsinn verursachen']={[0]=6.0,},
  ['Wahre Erfüllung']={[0]=20.0,},
  ['Waldweberlauerer zähmen']={[0]=900.0,},
  ['Wandernde Seuche']={[0]=300.0,},
  ['Wankelmut']={[0]=60.0,},
  ['Wasseratmung']={[0]=600.0,},
  ['Wassergestank']={[0]=3600.0,},
  ['Wehklagen der Banshee']={[0]=12.0,},
  ['Welkberührung']={[0]=120.0,},
  ['Welken']={[0]=21.0,},
  ['Welkende Hitze']={[0]=900.0,},
  ['Welkendes Gift']={[0]=180.0,},
  ['Welkstoß']={[0]=8.0,},
  ['Wellenbrecher']={[0]=10.0,},
  ['Wilde Attacke']={[0]=4.0,},
  ['Wilde Magie']={[0]=30.0,},
  ['Wilde Verwandlung']={[0]=20.0,},
  ['Wilder Angriff']={[0]=5.0,},
  ['Wilder Sturmangriff']={[0]=30.0,},
  ['Wilder Sturmangriff II']={[0]=30.0,},
  ['Wilder Sturmangriff III']={[0]=30.0,},
  ['Wilder Sturmangriff IV']={[0]=30.0,},
  ['Wilder Sturmangriff V']={[0]=30.0,},
  ['Wildtier zähmen']={[0]=20.0,},
  ['Wildtier ängstigen']={[1]=10.0,[2]=15.0,[3]=20.0,[0]=20.0,},
  ['Wille von Hakkar']={[0]=20.0,},
  ['Willensverfall']={[0]=1200.0,},
  ['Windschnitter']={[0]=20.0,},
  ['Windsor gibt Pferd frei DND']={[0]=55.0,},
  ['Windstoß']={[0]=4.0,},
  ['Winterkälte']={[0]=15.0,},
  ['Winterschlaf']={[1]=20.0,[2]=30.0,[3]=40.0,[0]=40.0,},
  ['Wirbeltrip']={[0]=2.0,},
  ['Wirbelwind']={[0]=2.0,},
  ['Witwenbiss']={[0]=4.0,},
  ['Wucherwurzeln']={[1]=12.0,[2]=15.0,[3]=18.0,[4]=21.0,[5]=24.0,[6]=27.0,[0]=27.0,},
  ['Wunde']={[0]=25.0,},
  ['Wundgift']={[0]=15.0,},
  ['Würgeseuche']={[0]=300.0,},
  ['Wütender Biss']={[0]=6.0,},
  ['Wüterich anlocken']={[0]=2.0,},
  ['Zangengriff']={[0]=15.0,},
  ['Zauber-Verwundbarkeit']={[0]=5.0,},
  ['Zauberschutz von Laze']={[0]=3.0,},
  ['Zaubersperre']={[1]=6.0,[2]=8.0,[0]=8.0,},
  ['Zeitgeber für Kristallkörper']={[0]=7200.0,},
  ['Zeitraffer']={[0]=8.0,},
  ['Zerfetzen']={[0]=12.0,},
  ['Zerfleischen']={[0]=15.0,},
  ['Zerhäckseln']={[0]=6.0,},
  ['Zermalmen']={[0]=2.0,},
  ['Zerreißendes Spalten']={[0]=30.0,},
  ['Zerschmettern']={[0]=2.0,},
  ['Zerstückeln']={[0]=10.0,},
  ['Zielattrappe Spawneffekt']={[0]=5.0,},
  ['Zorn der Winde']={[0]=12.0,},
  ['Zorn des Seuchenfürsten']={[0]=10.0,},
  ['Zuchtmeister der Dunkeleisenzwerge-Tod']={[0]=6.0,},
  ['Zuls Aura herstellen']={[0]=30.0,},
  ['Zurechtstutzen']={[0]=10.0,},
  ['Zuschlagen']={[0]=4.0,},
  ['scaler test']={[0]=10.0,},
  ['Ätzende Giftspucke']={[0]=10.0,},
  ['Ätzende Säure']={[0]=300.0,},
  ['Ätzende Säurespucke']={[0]=10.0,},
  ['Ätzender Brühschlammer']={[0]=60.0,},
  ['Ätzgift']={[0]=30.0,},
  ['Überlebensinstinkt']={[0]=2.0,},
  ['Überraschungs-Angriff']={[0]=2.5,},
}

-- custom entries not detected by DBC extractor
pfUI_locale["deDE"]["debuffs"]['Kältekegel']={[0]=8.0,} -- Cone of Cold

pfUI_locale["deDE"]["totems"] = {
  ["Totem der Krankheitsreinigung"] = "spell_nature_diseasecleansingtotem",
  ["Totem des Erdelementars"] = "spell_nature_earthelemental_totem",
  ["Totem der Erdbindung"] = "spell_nature_strengthofearthtotem02",
  ["Totem des Feuerelementars"] = "spell_fire_elemental_totem",
  ["Totem der Feuernova"] = "spell_fire_sealoffire",
  ["Totem des Feuerwiderstands"] = "spell_fireresistancetotem_01",
  ["Totem der Flammenzunge"] = "spell_nature_guardianward",
  ["Totem des Frostwiderstands"] = "spell_frostresistancetotem_01",
  ["Totem der luftgleichen Anmut"] = "spell_nature_invisibilitytotem",
  ["Totem der Erdung"] = "spell_nature_groundingtotem",
  ["Totem des heilenden Flusses"] = "Inv_spear_04",
  ["Totem des glühenden Magmas"] = "spell_fire_selfdestruct",
  ["Totem der Manaquelle"] = "spell_nature_manaregentotem",
  ["Totem der Manaflut"] = "spell_frost_summonwaterelemental",
  ["Totem des Naturwiderstands"] = "spell_nature_natureresistancetotem",
  ["Totem der Giftreinigung"] = "spell_nature_poisoncleansingtotem",
  ["Totem der Verbrennung"] = "spell_fire_searingtotem",
  ["Totem des Wachens"] = "spell_nature_removecurse",
  ["Totem der Steinklaue"] = "spell_nature_stoneclawtotem",
  ["Totem der Steinhaut"] = "spell_nature_stoneskintotem",
  ["Totem der Erdstärke"] = "spell_nature_earthbindtotem",
  ["Totem des Ingrimms"] = "spell_fire_totemofwrath",
  ["Totem des Erdstoßes"] = "spell_nature_tremortotem",
  ["Totem des Windzorns"] = "spell_nature_windfury",
  ["Totem der Windmauer"] = "spell_nature_earthbind",
  ["Totem des stürmischen Zorns"] = "spell_nature_slowingtotem",
}

pfUI_locale["deDE"]["icons"] = {
  ["Krankheit aufheben"] = "Spell_Nature_NullifyDisease",
  ["Vergiftung aufheben"] = "Spell_Nature_NullifyPoison_02",
  ["Vergiftung aufheben - Effekt"] = "Spell_Nature_NullifyPoison_02",
  ["MG-Turm aktivieren"] = "INV_Weapon_Rifle_10",
  ["Adrenalinrausch"] = "Spell_Shadow_ShadowWordDominate",
  ["Nachwirkung"] = "Spell_Fire_Fire",
  ["Aggression"] = "Ability_Racial_Avatar",
  ["Peinigende Flammen"] = "Spell_Fire_BlueImmolation",
  ["Gezielter Schuss"] = "INV_Spear_07",
  ["Alchimie"] = "Trade_Alchemy",
  ["Hinterhalt"] = "Ability_Rogue_Ambush",
  ["Fluch verstärken"] = "Spell_Shadow_Contagion",
  ["Magie verstärken"] = "Spell_Holy_FlashHeal",
  ["Seelenstärke der Ahnen"] = "Spell_Nature_UndyingStrength",
  ["Heilung der Ahnen"] = "Spell_Nature_UndyingStrength",
  ["Wissen der Ahnen"] = "Spell_Shadow_GrimWard",
  ["Geist der Ahnen"] = "Spell_Nature_Regenerate",
  ["Uralte Hysterie"] = "Spell_Shadow_UnholyFrenzy",
  ["Beruhigendes Gift"] = "Spell_Nature_SlowPoison",
  ["Aggressionskontrolle"] = "Spell_Holy_BlessingOfStamina",
  ["Seelenpein"] = "Spell_Shadow_GatherShadows",
  ["Vorahnung"] = "Spell_Nature_MirrorImage",
  ["Wassergestalt"] = "Ability_Druid_AquaticForm",
  ["Arkanschlag"] = "Spell_Arcane_Blast",
  ["Arkane Brillanz"] = "Spell_Holy_ArcaneIntellect",
  ["Arkane Konzentration"] = "Spell_Shadow_ManaBurn",
  ["Arkane Energie"] = "Spell_Holy_MindVision",
  ["Arkane Explosion"] = "Spell_Nature_WispSplode",
  ["Arkaner Fokus"] = "Spell_Holy_Devotion",
  ["Arkane Seelenstärke"] = "Spell_Arcane_ArcaneResilience",
  ["Arkaner Einschlag"] = "Spell_Nature_WispSplode",
  ["Arkane Instabilität"] = "Spell_Shadow_Teleport",
  ["Arkane Intelligenz"] = "Spell_Holy_MagicalSentry",
  ["Arkane Meditation"] = "Spell_Shadow_SiphonMana",
  ["Arkaner Geist"] = "Spell_Shadow_Charm",
  ["Arkane Geschosse"] = "Spell_Nature_StarFall",
  ["Arkane Kraft"] = "Spell_Arcane_ArcanePotency",
  ["Arkane Macht"] = "Spell_Nature_Lightning",
  ["Arkanwiderstand"] = "Spell_Nature_StarFall",
  ["Arkaner Schuss"] = "Ability_ImpalingBolt",
  ["Arkaner Schleier"] = "Spell_Magic_LesserInvisibilty",
  ["Arkanes Feingefühl"] = "Spell_Holy_DispelMagic",
  ["Arkaner Strom"] = "Spell_Shadow_Teleport",
  ["Arkane Verwundbarkeit"] = "Spell_Shadow_SoulLeech_2",
  ["Arktische Reichweite"] = "Spell_Shadow_DarkRitual",
  ["Arktische Winde"] = "Spell_Frost_ArcticWinds",
  ["Rüstung des Glaubens"] = "Spell_Holy_BlessingOfProtection",
  ["Rüstungsschmied"] = "Trade_BlackSmithing",
  ["Aspekt des Wildtiers"] = "Ability_Mount_PinkTiger",
  ["Aspekt des Geparden"] = "Ability_Mount_JungleTiger",
  ["Aspekt des Falken"] = "Spell_Nature_RavenForm",
  ["Aspekt des Affen"] = "Ability_Hunter_AspectOfTheMonkey",
  ["Aspekt des Rudels"] = "Ability_Mount_WhiteTiger",
  ["Aspekt der Viper"] = "Ability_Hunter_AspectoftheViper",
  ["Aspekt der Wildnis"] = "Spell_Nature_ProtectionformNature",
  ["Astraler Rückruf"] = "Spell_Nature_AstralRecal",
  ["Angreifen"] = "Temp",
  ["Angreifen"] = "Temp",
  ["Automatischer Schuss"] = "Ability_Whirlwind",
  ["Schild des Rächers"] = "Spell_Holy_AvengersShield",
  ["Zornige Vergeltung"] = "Spell_Holy_AvengineWrath",
  ["Vermeidung"] = "Spell_Magic_LesserInvisibilty",
  ["Axt-Spezialisierung"] = "INV_Axe_06",
  ["Heimzahlen"] = "Spell_Fire_PlayingWithFire",
  ["Meucheln"] = "Ability_BackStab",
  ["Abzeichen der Schwarmwache"] = "INV_Misc_AhnQirajTrinket_04",
  ["Gleichgewicht der Kräfte"] = "Ability_Druid_BalanceofPower",
  ["Dunkle Macht"] = "Spell_Shadow_DeathPact",
  ["Verbannen"] = "Spell_Shadow_Cripple",
  ["Bansheefluch"] = "Spell_Nature_Drowsy",
  ["Baumrinde"] = "Spell_Nature_StoneClawTotem",
  ["Sperrfeuer"] = "Ability_UpgradeMoonGlaive",
  ["Hieb"] = "Ability_Druid_Bash",
  ["Einfaches Lagerfeuer"] = "Spell_Fire_Fire",
  ["Kampfsturm"] = "Ability_Whirlwind",
  ["Schlachtruf"] = "Ability_Warrior_BattleShout",
  ["Kampfhaltung"] = "Ability_Warrior_OffensiveStance",
  ["Kampfhaltung Passiv"] = "Ability_Warrior_OffensiveStance",
  ["Bärengestalt"] = "Ability_Racial_BearForm",
  ["Wildtierkunde"] = "Ability_Physical_Taunt",
  ["Wildtierschlächter"] = "INV_Misc_Pelt_Bear_Ruin_02",
  ["Wildtierausbildung"] = "Ability_Hunter_BeastCall02",
  ["Segnung"] = "Spell_Frost_WindWalkOn",
  ["Berserkerwut"] = "Spell_Nature_AncestralGuardian",
  ["Berserkerhaltung"] = "Ability_Racial_Avatar",
  ["Berserkerhaltung - Passiv"] = "Ability_Racial_Avatar",
  ["Berserker"] = "Racial_Troll_Berserk",
  ["Wildtierdisziplin"] = "Spell_Nature_AbolishMagic",
  ["Erhöhte Tiergeschwindigkeit"] = "Ability_Druid_Dash",
  ["Zorn des Wildtiers"] = "Ability_Druid_FerociousBite",
  ["Verbindende Heilung"] = "Spell_Holy_BlindingHeal",
  ["Beißen"] = "Ability_Racial_Cannibalize",
  ["Schwarzer Pfeil"] = "Ability_TheBlackArrow",
  ["Blackout"] = "Spell_Shadow_GatherShadows",
  ["Schmiedekunst"] = "Trade_BlackSmithing",
  ["Klingenwirbel"] = "Ability_Warrior_PunishingBlow",
  ["Klingendrehen"] = "Ability_Warrior_Challange",
  ["Klingenwendung"] = "Ability_Rogue_BladeTwisting",
  ["Druckwelle"] = "Spell_Holy_Excorcism_02",
  ["Heiße Sohlen"] = "Spell_Fire_BurningSpeed",
  ["Gesegnete Erholung"] = "Spell_Holy_BlessedRecovery",
  ["Gesegnete Abhärtung"] = "Spell_Holy_BlessedResillience",
  ["Segen von Auchindoun"] = "INV_Battery_02",
  ["Segen der Freiheit"] = "Spell_Holy_SealOfValor",
  ["Segen der Könige"] = "Spell_Magic_MageArmor",
  ["Segen des Lichts"] = "Spell_Holy_PrayerOfHealing02",
  ["Segen der Macht"] = "Spell_Holy_FistOfJustice",
  ["Segen des Schutzes"] = "Spell_Holy_SealOfProtection",
  ["Segen der Opferung"] = "Spell_Holy_SealOfSacrifice",
  ["Segen der Rettung"] = "Spell_Holy_SealOfSalvation",
  ["Segen des Refugiums"] = "Spell_Nature_LightningShield",
  ["Segen der Weisheit"] = "Spell_Holy_SealOfWisdom",
  ["Blenden"] = "Spell_Shadow_MindSteal",
  ["Blendungspulver"] = "INV_Misc_Ammo_Gunpowder_02",
  ["Blinzeln"] = "Spell_Arcane_Blink",
  ["Blizzard"] = "Spell_Frost_IceStorm",
  ["Blocken"] = "Ability_Defend",
  ["Blutwahnsinn"] = "Spell_Shadow_SummonImp",
  ["Blutraserei"] = "Ability_Warrior_BloodFrenzy",
  ["Kochendes Blut"] = "Racial_Orc_BerserkerStrength",
  ["Blutpakt"] = "Spell_Shadow_BloodBoil",
  ["Kampfrausch"] = "Spell_Nature_BloodLust",
  ["Blutrausch"] = "Ability_Racial_BloodRage",
  ["Blutdurst"] = "Spell_Nature_BloodLust",
  ["Eber Strumangriff"] = "Spell_Shadow_VampiricAura",
  ["Donnernde Stimme"] = "Spell_Nature_Purge",
  ["Bogenspezialisierung"] = "INV_Weapon_Bow_12",
  ["Bogen"] = "INV_Weapon_Bow_05",
  ["Helles Lagerfeuer"] = "Spell_Fire_Fire",
  ["Spröde Rüstung"] = "Spell_Shadow_GrimWard",
  ["Brutaler Hieb"] = "Ability_Druid_Bash",
  ["Brennendes Adrenalin"] = "INV_Gauntlets_03",
  ["Brennende Seele"] = "Spell_Fire_Fire",
  ["Brennender Wunsch"] = "Spell_Shadow_PsychicScream",
  ["Tier rufen"] = "Ability_Hunter_BeastCall",
  ["Ruf der Flamme"] = "Spell_Fire_Immolation",
  ["Ruf des Donners"] = "Spell_Nature_CallStorm",
  ["Ruf des Nexus"] = "Spell_Holy_MindVision",
  ["Tarnung"] = "Ability_Stealth",
  ["Kannibalismus"] = "Ability_Racial_Cannibalize",
  ["Katzengestalt"] = "Ability_Druid_CatForm",
  ["Katastrophe"] = "Spell_Fire_WindsofWoe",
  ["Verbessertes Sternenfeuer"] = "Spell_Arcane_StarFire",
  ["Kettenheilung"] = "Spell_Nature_HealingWaveGreater",
  ["Kettenblitzschlag"] = "Spell_Nature_ChainLightning",
  ["Herausforderndes Gebrüll"] = "Ability_Druid_ChallangingRoar",
  ["Herausforderungsruf"] = "Ability_BullRush",
  ["Sturmangriff"] = "Ability_Warrior_Charge",
  ["Wut-Aufladung-Bonus-Effekt"] = "Ability_Warrior_Charge",
  ["Sturmangriffsbetäubung"] = "Spell_Frost_Stun",
  ["Züchtigung"] = "Spell_Holy_Chastise",
  ["Fieser Trick"] = "Ability_CheapShot",
  ["Kälte"] = "Spell_Frost_IceStorm",
  ["Kreis der Heilung"] = "Spell_Holy_CircleOfRenewal",
  ["Klaue"] = "Ability_Druid_Rake",
  ["Reinigung des Glaubens"] = "Spell_Holy_Renew",
  ["Freizaubern"] = "Spell_Shadow_ManaBurn",
  ["Spalten"] = "Ability_Warrior_Cleave",
  ["Falleneffizienz"] = "Spell_Nature_TimeStop",
  ["Mantel der Schatten"] = "Spell_Shadow_NetherCloak",
  ["Schließen"] = "Temp",
  ["Stoff"] = "INV_Chest_Cloth_21",
  ["Grober Wetzstein"] = "INV_Stone_SharpeningStone_02",
  ["Kobrareflexe"] = "Spell_Nature_GuardianWard",
  ["Kaltblütigkeit"] = "Spell_Ice_Lament",
  ["Kälteeinbruch"] = "Spell_Frost_WizardMark",
  ["Durchhaltevermögen des Kämpfers"] = "Spell_Nature_AncestralGuardian",
  ["Verbrennung"] = "Spell_Fire_SealOfFire",
  ["Befehlsgewalt"] = "Ability_Warrior_WarCry",
  ["Verbesserter Schlachtruf"] = "Ability_Warrior_BattleShout",
  ["Befehlsruf"] = "Ability_Warrior_RallyingCry",
  ["Aura der Konzentration"] = "Spell_Holy_MindSooth",
  ["Erschütterung"] = "Spell_Fire_Fireball",
  ["Erschütternder Schlag"] = "Ability_ThunderBolt",
  ["Erschütterndes Sperrfeuer"] = "Spell_Arcane_StarFire",
  ["Erschütternder Schuss"] = "Spell_Frost_Stun",
  ["Kältekegel"] = "Spell_Frost_Glacier",
  ["Feuersbrunst"] = "Spell_Fire_Fireball",
  ["Großbrand"] = "Spell_Fire_Incinerate",
  ["Essen herbeizaubern"] = "INV_Misc_Food_10",
  ["Manaachat herbeizaubern"] = "INV_Misc_Gem_Emerald_01",
  ["Manacitrin herbeizaubern"] = "INV_Misc_Gem_Opal_01",
  ["Manasmaragd herbeizaubern"] = "INV_Misc_Gem_Stone_01",
  ["Manajadestein herbeizaubern"] = "INV_Misc_Gem_Emerald_02",
  ["Manarubin herbeizaubern"] = "INV_Misc_Gem_Ruby_01",
  ["Wasser herbeizaubern"] = "INV_Drink_06",
  ["Weihe"] = "Spell_Holy_InnerFire",
  ["Magie verzehren"] = "Spell_Arcane_StudentOfMagic",
  ["Schatten verzehren"] = "Spell_Shadow_AntiShadow",
  ["Ansteckung"] = "Spell_Shadow_PainfulAfflictions",
  ["Konvektion"] = "Spell_Nature_WispSplode",
  ["Überzeugung"] = "Spell_Holy_RetributionAura",
  ["Kochkunst"] = "INV_Misc_Food_15",
  ["Verderbnis"] = "Spell_Shadow_AbominationExplosion",
  ["Gegenangriff"] = "Ability_Warrior_Challange",
  ["Gegenzauber"] = "Spell_Frost_IceShock",
  ["Gegenzauber - zum Schweigen gebracht"] = "Spell_Frost_IceShock",
  ["Ducken"] = "Ability_Druid_Cower",
  ["Feuerstein herstellen"] = "INV_Ammo_FireTar",
  ["Feuerstein herstellen (Grösser)"] = "INV_Ammo_FireTar",
  ["Feuerstein herstellen (Kleiner)"] = "INV_Ammo_FireTar",
  ["Feuerstein herstellen (Großer)"] = "INV_Ammo_FireTar",
  ["Gesundheitsstein herstellen"] = "INV_Stone_04",
  ["Gesundheitsstein herstellen (Grösser)"] = "INV_Stone_04",
  ["Gesundheitsstein herstellen (Kleiner)"] = "INV_Stone_04",
  ["Gesundheitsstein herstellen (Großer)"] = "INV_Stone_04",
  ["Gesundheitsstein herstellen (Klein)"] = "INV_Stone_04",
  ["Seelenstein herstellen"] = "Spell_Shadow_SoulGem",
  ["Seelenstein herstellen (Grösser)"] = "Spell_Shadow_SoulGem",
  ["Seelenstein herstellen (Kleiner)"] = "Spell_Shadow_SoulGem",
  ["Seelenstein herstellen (Großer)"] = "Spell_Shadow_SoulGem",
  ["Seelenstein herstellen (Klein)"] = "Spell_Shadow_SoulGem",
  ["Zauberstein herstellen"] = "INV_Misc_Gem_Sapphire_01",
  ["Zauberstein herstellen (Grösser)"] = "INV_Misc_Gem_Sapphire_01",
  ["Zauberstein herstellen (Großer)"] = "INV_Misc_Gem_Sapphire_01",
  ["Zauberstein herstellen (meisterlich)"] = "INV_Misc_Gem_Sapphire_01",
  ["Schleichende Paralyse"] = "Spell_Nature_TimeStop",
  ["Verkrüppeln"] = "Spell_Shadow_Cripple",
  ["Verkrüppelndes Gift"] = "Ability_PoisonSting",
  ["Verkrüppelndes Gift II"] = "Ability_PoisonSting",
  ["Kritische Masse"] = "Spell_Nature_WispHeal",
  ["Armbrüste"] = "INV_Weapon_Crossbow_01",
  ["Grausamkeit"] = "Ability_Rogue_Eviscerate",
  ["Kreuzzug"] = "Spell_Holy_Crusade",
  ["Aura des Kreuzfahrers"] = "Spell_Holy_CrusaderAura",
  ["Kreuzfahrerstoß"] = "Spell_Holy_CrusaderStrike",
  ["Grüner Daumen"] = "INV_Misc_Flower_01",
  ["Krankheit heilen"] = "Spell_Holy_NullifyDisease",
  ["Vergiftung heilen"] = "Spell_Nature_NullifyPoison",
  ["Fluch der Pein"] = "Spell_Shadow_CurseOfSargeras",
  ["Fluch der Verdammnis"] = "Spell_Shadow_AuraOfDarkness",
  ["Fluch der Verdammnis'-Effekt"] = "Spell_Shadow_AuraOfDarkness",
  ["Fluch der Erschöpfung"] = "Spell_Shadow_GrimWard",
  ["Fluch der Torheit"] = "Spell_Shadow_MindRot",
  ["Fluch der Tollkühnheit"] = "Spell_Shadow_UnholyStrength",
  ["Fluch der Schatten"] = "Spell_Shadow_CurseOfAchimonde",
  ["Fluch der Sprachen"] = "Spell_Shadow_CurseOfTounges",
  ["Fluch der Schwäche"] = "Spell_Shadow_CurseOfMannoroth",
  ["Fluch der Elemente"] = "Spell_Shadow_ChillTouch",
  ["Wirbelsturm"] = "Spell_Nature_EarthBind",
  ["Dolch-Spezialisierung"] = "INV_Weapon_ShortBlade_05",
  ["Dolche"] = "Ability_SteelMelee",
  ["Magie dämpfen"] = "Spell_Nature_AbolishMagic",
  ["Dunkles Sperrfeuer"] = "Spell_Shadow_PainSpike",
  ["Dunkler Pakt"] = "Spell_Shadow_DarkRitual",
  ["Dunkelheit"] = "Spell_Shadow_Twilight",
  ["Spurt"] = "Ability_Druid_Dash",
  ["Dämmersteinkrebs"] = "Ability_Hunter_Pet_Crab",
  ["Benommen"] = "Spell_Frost_Stun",
  ["Tödlicher Unterbrechungseffekt"] = "INV_ThrowingKnife_06",
  ["Tödliches Gift"] = "Ability_Rogue_DualWeild",
  ["Tödliches Gift II"] = "Ability_Rogue_DualWeild",
  ["Tödliches Gift III"] = "Ability_Rogue_DualWeild",
  ["Tödliches Gift IV"] = "Ability_Rogue_DualWeild",
  ["Tödliches Gift V"] = "Ability_Rogue_DualWeild",
  ["Tödliches Gift VI"] = "Ability_Rogue_DualWeild",
  ["Tödliches Gift VII"] = "Ability_Rogue_DualWeild",
  ["Tödlicher Wurf"] = "INV_ThrowingKnife_06",
  ["Todesmantel"] = "Spell_Shadow_DeathCoil",
  ["Todeswunsch"] = "Spell_Shadow_DeathPact",
  ["Tiefe Wunde"] = "Ability_BackStab",
  ["Tiefe Wunden"] = "Ability_BackStab",
  ["Verteidigung"] = "Ability_Racial_ShadowMeld",
  ["Verteidigungshaltung"] = "Ability_Warrior_DefensiveStance",
  ["Verteidigungshaltung - Passiv"] = "Ability_Warrior_DefensiveStance",
  ["Trotz"] = "Ability_Warrior_InnerRage",
  ["Abwehr"] = "Ability_Parry",
  ["Irrbilder von Jin'do"] = "Spell_Shadow_UnholyFrenzy",
  ["Dämonenrüstung"] = "Spell_Shadow_RagingScream",
  ["Dämonenhaut"] = "Spell_Shadow_RagingScream",
  ["Dämonische Ägide"] = "Spell_Shadow_RagingScream",
  ["Dämonische Umarmung"] = "Spell_Shadow_Metamorphosis",
  ["Dämonische Raserei"] = "Spell_Shadow_DeathPact",
  ["Dämonisches Wissen"] = "Spell_Shadow_ImprovedVampiricEmbrace",
  ["Dämonische Seelenstärke"] = "Spell_Shadow_DemonicFortitude",
  ["Dämonische Opferung"] = "Spell_Shadow_PsychicScream",
  ["Dämonische Taktiken"] = "Spell_Shadow_DemonicTactics",
  ["Demoralisierendes Gebrüll"] = "Ability_Druid_DemoralizingRoar",
  ["Demoralisierender Ruf"] = "Ability_Warrior_WarCry",
  ["Verdichteter Wetzstein"] = "INV_Stone_SharpeningStone_05",
  ["Verzweifeltes Gebet"] = "Spell_Holy_Restoration",
  ["Zerstörerische Reichweite"] = "Spell_Shadow_CorpseExplode",
  ["Entdecken"] = "Ability_Hibernation",
  ["Große Unsichtbarkeit entdecken"] = "Spell_Shadow_DetectInvisibility",
  ["Unsichtbarkeit entdecken"] = "Spell_Shadow_DetectInvisibility",
  ["Geringe Unsichtbarkeit entdecken"] = "Spell_Shadow_DetectLesserInvisibility",
  ["Magie entdecken"] = "Spell_Holy_Dizzy",
  ["Fallen entdecken"] = "Ability_Spy",
  ["Abschreckung"] = "Ability_Whirlwind",
  ["Verwüsten"] = "INV_Sword_11",
  ["Verwüstung"] = "Spell_Fire_FlameShock",
  ["Aura der Hingabe"] = "Spell_Holy_DevotionAura",
  ["Magie verschlingen"] = "Spell_Nature_Purge",
  ["Magie verschlingen' - Effekt"] = "Spell_Nature_Purge",
  ["Verschlingende Seuche"] = "Spell_Shadow_BlackPlague",
  ["Diplomatie"] = "INV_Misc_Note_02",
  ["Terrorbärengestalt"] = "Ability_Racial_BearForm",
  ["Gemeinheiten"] = "Spell_Shadow_SummonSuccubus",
  ["Verbesserte Kopfnuss"] = "Ability_Sap",
  ["Entwaffnen"] = "Ability_Warrior_Disarm",
  ["Falle entschärfen"] = "Spell_Shadow_GrimWard",
  ["Totem der Krankheitsreinigung"] = "Spell_Nature_DiseaseCleansingTotem",
  ["Entzaubern"] = "Spell_Holy_RemoveCurse",
  ["Rückzug"] = "Ability_Rogue_Feint",
  ["Tier freigeben"] = "Spell_Nature_SpiritWolf",
  ["Magiebannung"] = "Spell_Holy_DispelMagic",
  ["Ablenken"] = "Ability_Rogue_Distract",
  ["Ablenkender Schuss"] = "Spell_Arcane_Blink",
  ["Sturzflug"] = "Spell_Shadow_BurningSpirit",
  ["Göttliche Gunst"] = "Spell_Holy_Heal",
  ["Göttlicher Furor"] = "Spell_Holy_SealOfWrath",
  ["Göttliche Eingebung"] = "Spell_Holy_DivineIllumination",
  ["Göttliche Weisheit"] = "Spell_Nature_Sleep",
  ["Göttliches Eingreifen"] = "Spell_Nature_TimeStop",
  ["Göttlicher Schutz"] = "Spell_Holy_Restoration",
  ["Gottesschild"] = "Spell_Holy_DivineIntervention",
  ["Göttlicher Wille"] = "Spell_Holy_DivineSpirit",
  ["Göttliche Stärke"] = "Ability_GolemThunderClap",
  ["Göttlicher Zorn"] = "Spell_Holy_SearingLight",
  ["Ausweichen"] = "Spell_Nature_Invisibilty",
  ["Verdammnisfeuer"] = "Spell_Fire_Incinerate",
  ["Drachenodem"] = "INV_Misc_Head_Dragon_01",
  ["Drachenschuppenlederverarbeitung"] = "INV_Misc_MonsterScales_03",
  ["Blutsauger"] = "Spell_Shadow_LifeDrain02",
  ["Mana entziehen"] = "Spell_Shadow_SiphonMana",
  ["Seelendieb"] = "Spell_Shadow_Haunting",
  ["Traumloser Schlaf"] = "Spell_Nature_Sleep",
  ["Trinken"] = "INV_Drink_07",
  ["Beidhändigkeit"] = "Ability_DualWield",
  ["Beidhändigkeits-Spezialisierung"] = "Ability_DualWield",
  ["Duell"] = "Temp",
  ["Staubwolke"] = "Ability_Hibernation",
  ["Adlerauge"] = "Ability_Hunter_EagleEye",
  ["Totem des Erdelementars"] = "Spell_Nature_EarthElemental_Totem",
  ["Erdschild"] = "Spell_Nature_SkinofEarth",
  ["Erdschock"] = "Spell_Nature_EarthShock",
  ["Erdbindung"] = "Spell_Nature_StrengthOfEarthTotem02",
  ["Totem der Erdbindung"] = "Spell_Nature_StrengthOfEarthTotem02",
  ["Erdschlag"] = "Spell_Nature_AbolishMagic",
  ["Effizienz"] = "Spell_Frost_WizardMark",
  ["Elementare Verwüstung"] = "Spell_Fire_ElementalDevastation",
  ["Elementarfokus"] = "Spell_Shadow_ManaBurn",
  ["Elementarfuror"] = "Spell_Fire_Volcano",
  ["Elementarlederverarbeitung"] = "Trade_LeatherWorking",
  ["Elementarbeherrschung"] = "Spell_Nature_WispHeal",
  ["Elementare Präzision"] = "Spell_Nature_ElementalPrecision_1",
  ["Elementarwetzstein"] = "INV_Stone_02",
  ["Elementarwaffen"] = "Spell_Fire_FlameTounge",
  ["Elunes Anmut"] = "Spell_Holy_ElunesGrace",
  ["Flüchtigkeit"] = "Spell_Magic_LesserInvisibilty",
  ["Glutsturm"] = "Spell_Fire_SelfDestruct",
  ["Machtvolle arkane Geschosse"] = "Spell_Nature_StarFall",
  ["Machtvolle Verderbnis"] = "Spell_Shadow_AbominationExplosion",
  ["Machtvoller Feuerball"] = "Spell_Fire_FlameBolt",
  ["Machtvoller Frostblitz"] = "Spell_Frost_FrostBolt02",
  ["Machtvolle Heilung"] = "Spell_Holy_GreaterHeal",
  ["Machtvolle Verjüngung"] = "Ability_Druid_EmpoweredRejuvination",
  ["Machtvolle Berührung"] = "Ability_Druid_EmpoweredTouch",
  ["Entzückter Wassergeist"] = "INV_Wand_01",
  ["Verzauberkunst"] = "Trade_Engraving",
  ["Durchhaltevermögen"] = "Spell_Nature_UnyeildingStamina",
  ["Belastbarkeit-Ausbildung"] = "Spell_Nature_Reincarnation",
  ["Ingenieurskunst"] = "Trade_Engineering",
  ["Technologist"] = "INV_Misc_Gear_01",
  ["Wutanfall"] = "Ability_Druid_Enrage",
  ["Angereicherter Manakeks"] = "INV_Misc_Fork&Knife",
  ["Dämonensklave"] = "Spell_Shadow_EnslaveDemon",
  ["Wucherwurzeln"] = "Spell_Nature_StrangleVines",
  ["Einfangen"] = "Spell_Nature_StrangleVines",
  ["Vergiften"] = "Ability_Rogue_Disembowel",
  ["Entfesselungskünstler"] = "Ability_Rogue_Trip",
  ["Entrinnen"] = "Spell_Shadow_ShadowWard",
  ["Ausweiden"] = "Ability_Rogue_Eviscerate",
  ["Hervorrufung"] = "Spell_Nature_Purge",
  ["Hinrichten"] = "INV_Sword_48",
  ["Exorzismus"] = "Spell_Holy_Excorcism_02",
  ["Wacher Geist"] = "INV_Enchant_EssenceEternalLarge",
  ["Sprengfalle"] = "Spell_Fire_SelfDestruct",
  ["Sprengfalle'-Effekt"] = "Spell_Fire_SelfDestruct",
  ["Rüstung schwächen"] = "Ability_Warrior_Riposte",
  ["Schwäche aufdecken"] = "Ability_Hunter_SniperShot",
  ["Gas extrahieren"] = "Spell_Nature_AbolishMagic",
  ["Auge um Auge"] = "Spell_Holy_EyeforanEye",
  ["Auge von Kilrogg"] = "Spell_Shadow_EvilEye",
  ["Auge des Sturms"] = "Spell_Shadow_SoulLeech_2",
  ["Augen des Wildtiers"] = "Ability_EyeOfTheOwl",
  ["Verblassen"] = "Spell_Magic_LesserInvisibilty",
  ["Feenfeuer"] = "Spell_Nature_FaerieFire",
  ["Feenfeuer (Tiergestalt)"] = "Spell_Nature_FaerieFire",
  ["Fanatismus"] = "Spell_Holy_Fanaticism",
  ["Fernsicht"] = "Spell_Nature_FarSight",
  ["Verhängnisvolle Affäre"] = "Spell_Shadow_Shadowfury",
  ["Furcht"] = "Spell_Shadow_Possession",
  ["Furchtzauberschutz"] = "Spell_Holy_Excorcism",
  ["Tier füttern"] = "Ability_Hunter_BeastTraining",
  ["Tier füttern Effekt"] = "Ability_Hunter_BeastTraining",
  ["Rückkopplung"] = "Spell_Shadow_RitualOfSacrifice",
  ["Totstellen"] = "Ability_Rogue_FeignDeath",
  ["Finte"] = "Ability_Rogue_Feint",
  ["Teufelsrüstung"] = "Spell_Shadow_FelArmour",
  ["Teufelskonzentration"] = "Spell_Shadow_FingerOfDeath",
  ["Teufelsbeherrschung"] = "Spell_Nature_RemoveCurse",
  ["Teufelsenergie"] = "Spell_Shadow_PsychicScream",
  ["Teufelsintelligenz"] = "Spell_Holy_MagicalSentry",
  ["Teufelswut"] = "Spell_Fire_ElementalDevastation",
  ["Teufelsausdauer"] = "Spell_Shadow_AntiShadow",
  ["Teufelsfeuer"] = "Spell_Fire_Fireball",
  ["Katzenhafte Anmut"] = "INV_Feather_01",
  ["Wilde Aggression"] = "Ability_Druid_DemoralizingRoar",
  ["Wilde Attacke"] = "Ability_Hunter_Pet_Bear",
  ["Instinkt der Wildnis"] = "Ability_Ambush",
  ["Katzenhafte Schnelligkeit"] = "Spell_Nature_SpiritWolf",
  ["Wilder Biss"] = "Ability_Druid_FerociousBite",
  ["Wilde Eingebung"] = "Ability_Hunter_FerociousInspiration",
  ["Wildheit"] = "INV_Misc_MonsterClaw_04",
  ["Fetisch"] = "INV_Misc_Horn_01",
  ["Kräutersuche"] = "INV_Misc_Flower_02",
  ["Mineraliensuche"] = "Spell_Nature_Earthquake",
  ["Schatzsucher"] = "Racial_Dwarf_FindTreasure",
  ["Schwächen aufspüren"] = "Ability_Rogue_FindWeakness",
  ["Feuerschlag"] = "Spell_Fire_Fireball",
  ["Feueratem"] = "Spell_Fire_Burnout",
  ["Totem des Feuerelementars"] = "Spell_Fire_Elemental_Totem",
  ["Totem der Feuernova"] = "Spell_Fire_SealOfFire",
  ["Feuermacht"] = "Spell_Fire_Immolation",
  ["Feuerwiderstand"] = "Spell_Fire_FireArmor",
  ["Aura des Feuerwiderstands"] = "Spell_Fire_SealOfFire",
  ["Totem des Feuerwiderstands"] = "Spell_FireResistanceTotem_01",
  ["Feuerschild"] = "Spell_Fire_FireArmor",
  ["Feuerverwundbarkeit"] = "Spell_Fire_SoulBurn",
  ["Feuerzauberschutz"] = "Spell_Fire_FireArmor",
  ["Feuer Verwundbarkeit"] = "INV_Misc_QirajiCrystal_02",
  ["Feuerball"] = "Spell_Fire_FlameBolt",
  ["Feuerblitz"] = "Spell_Fire_FireBolt",
  ["Erste Hilfe"] = "Spell_Holy_SealOfSacrifice",
  ["Angeln"] = "Trade_Fishing",
  ["Angel"] = "Trade_Fishing",
  ["Faustwaffen-Spezialisierung"] = "INV_Gauntlets_04",
  ["Faustwaffen"] = "INV_Gauntlets_04",
  ["Flammenschock"] = "Spell_Fire_FlameShock",
  ["Flammenwerfen"] = "Spell_Fire_Flare",
  ["Flammenstoß"] = "Spell_Fire_SelfDestruct",
  ["Flammenwerfer"] = "Spell_Fire_Incinerate",
  ["Angriff der Flammenzunge"] = "Spell_Fire_FlameTounge",
  ["Totem der Flammenzunge"] = "Spell_Nature_GuardianWard",
  ["Waffe der Flammenzunge"] = "Spell_Fire_FlameTounge",
  ["Leuchtfeuer"] = "Spell_Fire_Flare",
  ["Blitzheilung"] = "Spell_Holy_FlashHeal",
  ["Lichtblitz"] = "Spell_Holy_FlashHeal",
  ["Fluggestalt"] = "Ability_Druid_FlightForm",
  ["Schlaghagel"] = "Ability_GhoulFrenzy",
  ["Fokussiertes Zauberwirken"] = "Spell_Arcane_Blink",
  ["Fokussierte Gedanken"] = "Spell_Nature_FocusedMind",
  ["Fokussierte Energie"] = "Spell_Shadow_FocusedPower",
  ["Fokussierte Wut"] = "Ability_Warrior_FocusedRage",
  ["Gebündeltes Sternenlicht"] = "INV_Staff_01",
  ["Essen"] = "INV_Misc_Fork&Knife",
  ["Vorahnung"] = "Spell_Holy_RemoveCurse",
  ["Naturgewalt"] = "Ability_Druid_ForceofNature",
  ["Macht des Willens"] = "Spell_Nature_SlowingTotem",
  ["Eiskältefalle"] = "Spell_Frost_ChainsOfIce",
  ["Eiskältefalle"] = "Spell_Frost_ChainsOfIce",
  ["Rasende Regeneration"] = "Ability_BullRush",
  ["Raserei"] = "INV_Misc_MonsterClaw_03",
  ["Wilde Eingebung"] = "INV_Misc_MonsterClaw_03",
  ["Frostrüstung"] = "Spell_Frost_FrostArmor02",
  ["Frostschlag"] = "Spell_Frost_FrostBolt02",
  ["Frost-Kanalisierung"] = "Spell_Frost_Stun",
  ["Frostnova"] = "Spell_Frost_FrostNova",
  ["Frostwiderstand"] = "Spell_Frost_FrostWard",
  ["Aura des Frostwiderstands"] = "Spell_Frost_WizardMark",
  ["Totem des Frostwiderstands"] = "Spell_FrostResistanceTotem_01",
  ["Frostschock"] = "Spell_Frost_FrostShock",
  ["Frostfalle"] = "Spell_Frost_FreezingBreath",
  ["Frostfalle-Aura"] = "Spell_Frost_FrostNova",
  ["Frostzauberschutz"] = "Spell_Frost_FrostWard",
  ["Schutz des Frostes"] = "Spell_Frost_FrostWard",
  ["Frost Verwundbarkeit"] = "INV_Misc_QirajiCrystal_04",
  ["Erfrierung"] = "Spell_Frost_FrostArmor",
  ["Frostblitz"] = "Spell_Frost_FrostBolt02",
  ["Frostbrandangriff"] = "Spell_Frost_FrostBrand",
  ["Waffe des Frostbrands"] = "Spell_Frost_FrostBrand",
  ["Tiefgefroren"] = "Spell_Frost_FrozenCore",
  ["Wutgeheul"] = "Ability_Hunter_Pet_Wolf",
  ["Furor"] = "Spell_Holy_BlessingOfStamina",
  ["Wut der zerschmetternden Wellen"] = "Spell_Nature_UnrelentingStorm",
  ["Erdrosseln"] = "Ability_Rogue_Garrote",
  ["Erdrossseln - Stille"] = "Ability_Rogue_Garrote",
  ["Geisterwolf"] = "Spell_Nature_SpiritWolf",
  ["Geisterhafter Stoß"] = "Spell_Shadow_Curse",
  ["Geschenk des Lebens"] = "INV_Misc_Gem_Pearl_05",
  ["Geschenk der Natur"] = "Spell_Nature_ProtectionformNature",
  ["Gabe der Naaru"] = "Spell_Holy_HolyProtection",
  ["Gabe der Wildnis"] = "Spell_Nature_Regeneration",
  ["Solarplexus"] = "Ability_Gouge",
  ["Luftgleiche Anmut"] = "Spell_Nature_InvisibilityTotem",
  ["Totem der luftgleichen Anmut"] = "Spell_Nature_InvisibilityTotem",
  ["Große Ausdauer"] = "Spell_Nature_UnyeildingStamina",
  ["Großer Segen der Könige"] = "Spell_Magic_GreaterBlessingofKings",
  ["Großer Segen des Lichts"] = "Spell_Holy_GreaterBlessingofLight",
  ["Großer Segen der Macht"] = "Spell_Holy_GreaterBlessingofKings",
  ["Großer Segen der Rettung"] = "Spell_Holy_GreaterBlessingofSalvation",
  ["Großer Segen des Refugiums"] = "Spell_Holy_GreaterBlessingofSanctuary",
  ["Großer Segen der Weisheit"] = "Spell_Holy_GreaterBlessingofWisdom",
  ["Großer traumloser Schlaf"] = "Spell_Nature_Sleep",
  ["Große Heilung"] = "Spell_Holy_GreaterHeal",
  ["Schrecklicher Wurf"] = "Ability_BackStab",
  ["Grimmige Reichweite"] = "Spell_Shadow_CallofBone",
  ["Totem der Erdung"] = "Spell_Nature_GroundingTotem",
  ["Totem der Erdung Effekt"] = "Spell_Nature_GroundingTotem",
  ["Kriechen"] = "Temp",
  ["Knurren"] = "Ability_Physical_Taunt",
  ["Gunst des Hüters"] = "Spell_Holy_SealOfProtection",
  ["Schusswaffenspezialisierung"] = "INV_Musket_03",
  ["Schusswaffen"] = "INV_Weapon_Rifle_01",
  ["Hammer der Gerechtigkeit"] = "Spell_Holy_SealOfMight",
  ["Hammer des Zorns"] = "Ability_ThunderClap",
  ["Kniesehne"] = "Ability_ShockWave",
  ["Belästigen"] = "Ability_Hunter_Harass",
  ["Zähigkeit"] = "INV_Helmet_23",
  ["Eile"] = "INV_Potion_108",
  ["Falkenauge"] = "Ability_TownWatch",
  ["Heilen"] = "Spell_Holy_Heal",
  ["Heilfokus"] = "Spell_Holy_HealingFocus",
  ["Heilendes Licht"] = "Spell_Holy_HolyBolt",
  ["Heilender Fluss"] = "INV_Spear_04",
  ["Totem des heilenden Flusses"] = "INV_Spear_04",
  ["Heilende Berührung"] = "Spell_Nature_HealingTouch",
  ["Welle der Heilung"] = "Spell_Nature_MagicImmunity",
  ["Pfad der Heilung"] = "Spell_Nature_HealingWay",
  ["Lebenslinie"] = "Spell_Shadow_LifeDrain",
  ["Herz der Wildnis"] = "Spell_Holy_BlessingOfAgility",
  ["Schwerer Wetzstein"] = "INV_Stone_SharpeningStone_03",
  ["Höllenfeuer"] = "Spell_Fire_Incinerate",
  ["Höllenfeuer - Effekt"] = "Spell_Fire_Incinerate",
  ["Überlegenheit des Höllenfeuers"] = "INV_BannerPVP_02",
  ["Blutsturz"] = "Spell_Shadow_LifeDrain",
  ["Kräutersammeln"] = "Spell_Nature_NatureTouchGrow",
  ["Kräuterkunde"] = "Spell_Nature_NatureTouchGrow",
  ["Heldenhafter Stoß"] = "Ability_Rogue_Ambush",
  ["Heldentum"] = "Ability_Shaman_Heroism",
  ["Verhexung der Schwäche"] = "Spell_Shadow_FingerOfDeath",
  ["Winterschlaf"] = "Spell_Nature_Sleep",
  ["Heiliges Feuer"] = "Spell_Holy_SearingLight",
  ["Heiliges Licht"] = "Spell_Holy_HolyBolt",
  ["Heilige Nova"] = "Spell_Holy_HolyNova",
  ["Heilige Macht"] = "Spell_Holy_Power",
  ["Heilige Reichweite"] = "Spell_Holy_Purify",
  ["Heiliger Schild"] = "Spell_Holy_BlessingOfProtection",
  ["Heiliger Schock"] = "Spell_Holy_SearingLight",
  ["Macht des Glaubens"] = "Spell_Holy_SealOfSalvation",
  ["Heiliger Zorn"] = "Spell_Holy_Excorcism",
  ["Ehrenloses Ziel"] = "Spell_Magic_LesserInvisibilty",
  ["Pferdreiten"] = "Spell_Nature_Swiftness",
  ["Schreckgeheul"] = "Spell_Shadow_DeathScream",
  ["Humanoidentöten"] = "Spell_Holy_PrayerOfHealing",
  ["Mal des Jägers"] = "Ability_Hunter_SniperShot",
  ["Hurrikan"] = "Spell_Nature_Cyclone",
  ["Hypothermie"] = "Spell_Fire_BlueImmolation",
  ["Eisrüstung"] = "Spell_Frost_FrostArmor02",
  ["Eisbarriere"] = "Spell_Ice_Lament",
  ["Eisblock"] = "Spell_Frost_Frost",
  ["Eisschollen"] = "Spell_Frost_IceFloes",
  ["Eislanze"] = "Spell_Frost_FrostBlast",
  ["Eissplitter"] = "Spell_Frost_IceShard",
  ["Eisige Adern"] = "Spell_Frost_ColdHearted",
  ["Entzünden"] = "Spell_Fire_Incinerate",
  ["Mana entzünden"] = "Spell_Fire_Incinerate",
  ["Illumination"] = "Spell_Holy_GreaterHeal",
  ["Feuerbrand"] = "Spell_Fire_Immolation",
  ["Feuerbrandfalle"] = "Spell_Fire_FlameShock",
  ["Feuerbrandfalle"] = "Spell_Fire_FlameShock",
  ["Einschlag"] = "Spell_Fire_MeteorStorm",
  ["Durchbohren"] = "Ability_SearingArrow",
  ["Aufspießender Stachel"] = "Spell_Frost_IceShard",
  ["Verbesserter Hinterhalt"] = "Ability_Rogue_Ambush",
  ["Verbesserte arkane Geschosse"] = "Spell_Nature_StarFall",
  ["Verbesserter Arkaner Schuss"] = "Ability_ImpalingBolt",
  ["Verbesserter Aspekt des Falken"] = "Spell_Nature_RavenForm",
  ["Verbesserter Aspekt des Affen"] = "Ability_Hunter_AspectOfTheMonkey",
  ["Verbessertes Meucheln"] = "Ability_BackStab",
  ["Verbessertes Sperrfeuer"] = "Ability_UpgradeMoonGlaive",
  ["Verbesserte Berserkerwut"] = "Spell_Nature_AncestralGuardian",
  ["Verbesserter Segen der Macht"] = "Spell_Holy_FistOfJustice",
  ["Verbesserter Segen der Weisheit"] = "Spell_Holy_SealOfWisdom",
  ["Verbessertes Blinzeln"] = "Spell_Arcane_Blink",
  ["Verbesserter Blizzard"] = "Spell_Frost_IceStorm",
  ["Verbesserter Blutrausch"] = "Ability_Racial_BloodRage",
  ["Verbesserte Kettenheilung"] = "Spell_Nature_HealingWaveGreater",
  ["Verbesserter Kettenblitzschlag"] = "Spell_Nature_ChainLightning",
  ["Verbesserter Herausforderungsruf"] = "Ability_Warrior_Challange",
  ["Verbesserter Sturmangriff"] = "Ability_Warrior_Charge",
  ["Verbessertes Spalten"] = "Ability_Warrior_Cleave",
  ["Verbesserte Aura der Konzentration"] = "Spell_Holy_MindSooth",
  ["Verbesserter Erschütternder Schuss"] = "Spell_Frost_Stun",
  ["Verbesserter Kältekegel"] = "Spell_Frost_Glacier",
  ["Verbesserte Verderbnis"] = "Spell_Shadow_AbominationExplosion",
  ["Verbesserter Gegenzauber"] = "Spell_Frost_IceShock",
  ["Verbesserter Fluch der Pein"] = "Spell_Shadow_CurseOfSargeras",
  ["Verbesserter Fluch der Schwäche"] = "Spell_Shadow_CurseOfMannoroth",
  ["Verbesserter Demoralisierender Ruf"] = "Ability_Warrior_WarCry",
  ["Verbesserte Aura der Hingabe"] = "Spell_Holy_DevotionAura",
  ["Verbessertes Entwaffnen"] = "Ability_Warrior_Disarm",
  ["Verbesserter Blutsauger"] = "Spell_Shadow_LifeDrain02",
  ["Verbesserter Seelendieb"] = "Spell_Shadow_Haunting",
  ["Verbesserter Wutanfall"] = "Ability_Druid_Enrage",
  ["Verbesserter Dämonensklave"] = "Spell_Shadow_EnslaveDemon",
  ["Verbessertes Ausweiden"] = "Ability_Rogue_Eviscerate",
  ["Verbessertes Hinrichten"] = "INV_Sword_48",
  ["Verbessertes Rüstung schwächen"] = "Ability_Warrior_Riposte",
  ["Verbesserte Augen des Wildtiers"] = "Ability_EyeOfTheOwl",
  ["Verbessertes Verblassen"] = "Spell_Magic_LesserInvisibilty",
  ["Verbessertes Totstellen"] = "Ability_Rogue_FeignDeath",
  ["Verbesserter Feuerschlag"] = "Spell_Fire_Fireball",
  ["Verbesserte Feuertotems"] = "Spell_Fire_SealOfFire",
  ["Verbesserter Feuerzauberschutz"] = "Spell_Fire_FireArmor",
  ["Verbesserter Feuerball"] = "Spell_Fire_FlameBolt",
  ["Verbesserter Feuerblitz"] = "Spell_Fire_FireBolt",
  ["Verbesserter Flammenstoß"] = "Spell_Fire_SelfDestruct",
  ["Verbesserter Lichtblitz"] = "Spell_Holy_FlashHeal",
  ["Verbesserte Frostnova"] = "Spell_Frost_FreezingBreath",
  ["Verbesserter Frostblitz"] = "Spell_Frost_FrostBolt02",
  ["Verbesserter Geisterwolf"] = "Spell_Nature_SpiritWolf",
  ["Verbesserter Solarplexus"] = "Ability_Gouge",
  ["Verbesserter Hammer der Gerechtigkeit"] = "Spell_Holy_SealOfMight",
  ["Verbesserte Kniesehne"] = "Ability_ShockWave",
  ["Verbesserte Heilung"] = "Spell_Holy_Heal02",
  ["Verbesserte Heilende Berührung"] = "Spell_Nature_HealingTouch",
  ["Verbesserte Welle der Heilung"] = "Spell_Nature_MagicImmunity",
  ["Verbesserte Lebenslinie"] = "Spell_Shadow_LifeDrain",
  ["Verbesserter Gesundheitsstein"] = "INV_Stone_04",
  ["Verbesserter Heldenhafter Stoß"] = "Ability_Rogue_Ambush",
  ["Verbesserter Heiliger Schild"] = "Spell_Holy_BlessingOfProtection",
  ["Verbessertes Schreckensgeheul"] = "Spell_Shadow_DeathScream",
  ["Verbessertes Mal des Jägers"] = "Ability_Hunter_SniperShot",
  ["Verbesserter Feuerbrand"] = "Spell_Fire_Immolation",
  ["Verbesserter Wichtel"] = "Spell_Shadow_SummonImp",
  ["Verbessertes Inneres Feuer"] = "Spell_Holy_InnerFire",
  ["Verbessertes Abfangen"] = "Ability_Rogue_Sprint",
  ["Verbesserter Drohruf"] = "Ability_GolemThunderClap",
  ["Verbessertes Richturteil"] = "Spell_Holy_RighteousFury",
  ["Verbesserter Tritt"] = "Ability_Kick",
  ["Verbesserter Nierenhieb"] = "Ability_Rogue_KidneyShot",
  ["Verbesserte Schmerzenspeitsche"] = "Spell_Shadow_Curse",
  ["Verbesserte Handauflegung"] = "Spell_Holy_LayOnHands",
  ["Verbesserte Rudelführer"] = "Spell_Nature_UnyeildingStamina",
  ["Verbesserter Aderlass"] = "Spell_Shadow_BurningSpirit",
  ["Verbesserter Blitzschlag"] = "Spell_Nature_Lightning",
  ["Verbesserter Blitzschlag-Schild"] = "Spell_Nature_LightningShield",
  ["Verbessertes Totem der glühenden Magma"] = "Spell_Fire_SelfDestruct",
  ["Verbesserter Manabrand"] = "Spell_Shadow_ManaBurn",
  ["Verbesserter Manaschild"] = "Spell_Shadow_DetectLesserInvisibility",
  ["Verbessertes Totem der Manaquelle"] = "Spell_Nature_ManaRegenTotem",
  ["Verbessertes Mal der Wildnis"] = "Spell_Nature_Regeneration",
  ["Verbessertes Tier heilen"] = "Ability_Hunter_MendPet",
  ["Verbesserter Gedankenschlag"] = "Spell_Shadow_UnholyFrenzy",
  ["Verbessertes Mondfeuer"] = "Spell_Nature_StarFall",
  ["Verbesserter tödlicher Stoß"] = "Ability_Warrior_SavageBlow",
  ["Verbesserter Griff der Natur"] = "Spell_Nature_NaturesWrath",
  ["Verbessertes Überwältigen"] = "INV_Sword_05",
  ["Verbessertes Machtwort: Seelenstärke"] = "Spell_Holy_WordFortitude",
  ["Verbessertes Machtwort: Schild"] = "Spell_Holy_PowerWordShield",
  ["Verbessertes Gebet der Heilung"] = "Spell_Holy_PrayerOfHealing02",
  ["Verbesserter Psychischer Schrei"] = "Spell_Shadow_PsychicScream",
  ["Verbessertes Nachwachsen"] = "Spell_Nature_ResistNature",
  ["Verbesserte Reinkarnation"] = "Spell_Nature_Reincarnation",
  ["Verbesserte Verjüngung"] = "Spell_Nature_Rejuvenation",
  ["Verbessertes Verwunden"] = "Ability_Gouge",
  ["Verbesserte Erneuerung"] = "Spell_Holy_Renew",
  ["Verbesserte Aura der Vergeltung"] = "Spell_Holy_AuraOfLight",
  ["Verbesserte Rache"] = "Ability_Warrior_Revenge",
  ["Verbessertes Tier wiederbeleben"] = "Ability_Hunter_BeastSoothe",
  ["Verbesserter Zorn der Gerechtigkeit"] = "Spell_Holy_SealOfFury",
  ["Verbesserte Blutung"] = "Ability_Rogue_Rupture",
  ["Verbessertes Versengen"] = "Spell_Fire_SoulBurn",
  ["Verbesserter Skorpidstich"] = "Ability_Hunter_CriticalShot",
  ["Verbessertes Siegel der Rechtschaffenheit"] = "Ability_ThunderBolt",
  ["Verbessertes Siegel des Kreuzfahrers"] = "Spell_Holy_HolySmite",
  ["Verbesserter Sengender Schmerz"] = "Spell_Fire_SoulBurn",
  ["Verbesserter Schlangenbiss"] = "Ability_Hunter_Quickshot",
  ["Verbesserter Schattenblitz"] = "Spell_Shadow_ShadowBolt",
  ["Verbessertes Schattenwort: Schmerz"] = "Spell_Shadow_ShadowWordPain",
  ["Verbesserter Schildhieb"] = "Ability_Warrior_ShieldBash",
  ["Verbesserter Schildblock"] = "Ability_Defend",
  ["Verbesserter Schildwall"] = "Ability_Warrior_ShieldWall",
  ["Verbesserter Finsterer Stoß"] = "Spell_Shadow_RitualOfSacrifice",
  ["Verbessertes Zerschmettern"] = "Ability_Warrior_DecisiveStrike",
  ["Verbessertes Zerhäckseln"] = "Ability_Rogue_SliceDice",
  ["Verbessertes Sprinten"] = "Ability_Rogue_Sprint",
  ["Verbesserte Stiche und Bisse"] = "Ability_Hunter_Quickshot",
  ["Verbesserter Sukkubus"] = "Spell_Shadow_SummonSuccubus",
  ["Verbessertes Rüstung zerreißen"] = "Ability_Warrior_Sunder",
  ["Verbesserter Spott"] = "Spell_Nature_Reincarnation",
  ["Verbesserter Donnerknall"] = "Ability_ThunderClap",
  ["Verbesserte Gelassenheit"] = "Spell_Nature_Tranquility",
  ["Verbesserte Vampirumarmung"] = "Spell_Shadow_ImprovedVampiricEmbrace",
  ["Verbessertes Verschwinden"] = "Ability_Vanish",
  ["Verbesserter Leerwandler"] = "Spell_Shadow_SummonVoidWalker",
  ["Verbesserte Waffentotems"] = "Spell_Fire_EnchantWeapon",
  ["Verbesserter Wirbelwind"] = "Ability_Whirlwind",
  ["Verbessertes Zurechtstutzen"] = "Ability_Rogue_Trip",
  ["Verbrennen"] = "Spell_Fire_Burnout",
  ["Verbrennung"] = "Spell_Fire_FlameShock",
  ["Inferno"] = "Spell_Shadow_SummonInfernal",
  ["Initiative"] = "Spell_Shadow_Fumble",
  ["Inneres Feuer"] = "Spell_Holy_InnerFire",
  ["Innerer Fokus"] = "Spell_Frost_WindWalkOn",
  ["Anregen"] = "Spell_Nature_Lightning",
  ["Insektenschwarm"] = "Spell_Nature_InsectSwarm",
  ["Heimtückisches Geflüster"] = "Spell_Shadow_ManaFeed",
  ["Bedeutungslosigkeit"] = "Ability_Hibernation",
  ["Inspiration"] = "Spell_Holy_LayOnHands",
  ["Sofort wirkendes Gift"] = "Ability_Poisons",
  ["Sofort wirkendes Gift II"] = "Ability_Poisons",
  ["Sofort wirkendes Gift III"] = "Ability_Poisons",
  ["Sofort wirkendes Gift IV"] = "Ability_Poisons",
  ["Sofort wirkendes Gift V"] = "Ability_Poisons",
  ["Sofort wirkendes Gift VI"] = "Ability_Poisons",
  ["Sofort wirkendes Gift VII"] = "Ability_Poisons",
  ["Intensität"] = "Spell_Fire_LavaSpawn",
  ["Abfangen"] = "Ability_Rogue_Sprint",
  ["Betäubung abfangen"] = "Spell_Frost_Stun",
  ["Einschreiten"] = "Ability_Warrior_VictoryRush",
  ["Drohruf"] = "Ability_GolemThunderClap",
  ["Einschüchterung"] = "Ability_Devour",
  ["Unsichtbarkeit"] = "Ability_Mage_Invisibility",
  ["Eiserner Wille"] = "Spell_Magic_MageArmor",
  ["Juwelenschleifen"] = "INV_Misc_Gem_02",
  ["Jom Gabbar"] = "INV_Misc_EngGizmos_19",
  ["Richturteil"] = "Spell_Holy_RighteousFury",
  ["Richturteil des Blutes"] = "Spell_Shadow_LifeDrain",
  ["Richturteil des Befehls"] = "Ability_Warrior_InnerRage",
  ["Richturteil der Gerechtigkeit"] = "Spell_Holy_SealOfWrath",
  ["Richturteil des Lichts"] = "Spell_Holy_HealingAura",
  ["Richturteil der Abrechnung"] = "Ability_Warrior_InnerRage",
  ["Richturteil der Rechtschaffenheit"] = "Ability_ThunderBolt",
  ["Richturteil der Weisheit"] = "Spell_Holy_RighteousnessAura",
  ["Richturteil des Kreuzfahrers"] = "Spell_Holy_HolySmite",
  ["Tritt"] = "Ability_Kick",
  ["Tritt - zum Schweigen gebracht"] = "Ability_Kick",
  ["Nierenhieb"] = "Ability_Rogue_KidneyShot",
  ["Fass!"] = "Ability_Hunter_KillCommand",
  ["Tötungstrieb"] = "Spell_Holy_BlessingOfStamina",
  ["Kuss der Spinne"] = "INV_Trinket_Naxxramas04",
  ["Kodoreiten"] = "Spell_Nature_Swiftness",
  ["Aufschlitzen"] = "Ability_Druid_Lacerate",
  ["Schmerzenspeitsche"] = "Spell_Shadow_Curse",
  ["Letztes Gefecht"] = "Spell_Holy_AshesToAshes",
  ["Handauflegung"] = "Spell_Holy_LayOnHands",
  ["Rudelführer"] = "Spell_Nature_UnyeildingStamina",
  ["Leder"] = "INV_Chest_Leather_09",
  ["Lederverarbeitung"] = "INV_Misc_ArmorKit_17",
  ["Geringes Heilen"] = "Spell_Holy_LesserHeal",
  ["Geringe Welle der Heilung"] = "Spell_Nature_HealingWaveLesser",
  ["Geringe Unsichtbarkeit"] = "Spell_Magic_LesserInvisibilty",
  ["Tödliche Schüsse"] = "Ability_SearingArrow",
  ["Tödlichkeit"] = "Ability_CriticalStrike",
  ["Levitieren"] = "Spell_Holy_LayOnHands",
  ["Buchband"] = "INV_Misc_Book_11",
  ["Aderlass"] = "Spell_Shadow_BurningSpirit",
  ["Blühendes Leben"] = "INV_Misc_Herb_Felblossom",
  ["Anmut des Lichts"] = "Spell_Holy_LightsGrace",
  ["Blitzschlag"] = "Spell_Nature_Lightning",
  ["Blitzschlagatem"] = "Spell_Nature_Lightning",
  ["Blitzschlagbeherrschung"] = "Spell_Lightning_LightningBolt01",
  ["Blitzartige Reflexe"] = "Spell_Nature_Invisibilty",
  ["Blitzschlagschild"] = "Spell_Nature_LightningShield",
  ["Blitzschnell"] = "Spell_Nature_UnrelentingStorm",
  ["Brunnen des Lichts"] = "Spell_Holy_SummonLightwell",
  ["Erneuerung des Lichtbrunnens"] = "Spell_Holy_SummonLightwell",
  ["Schlossknacken"] = "Spell_Nature_MoonKey",
  ["Lange Benommenheit"] = "Spell_Frost_Stun",
  ["Kampfeslust"] = "Spell_Shadow_DeathPact",
  ["Streitkolben-Spezialisierung"] = "INV_Mace_01",
  ["Streitkolbenbetäubung"] = "Spell_Frost_Stun",
  ["Magische Rüstung"] = "Spell_MageArmor",
  ["Magische Vereinnahmung"] = "Spell_Nature_AstralRecalGroup",
  ["Einklang der Magie"] = "Spell_Nature_AbolishMagic",
  ["Totem des glühenden Magmas"] = "Spell_Fire_SelfDestruct",
  ["Panzer"] = "INV_Chest_Chain_05",
  ["Zerfleddern"] = "Ability_Druid_Mangle",
  ["Erheblicher traumloser Schlaf"] = "Spell_Nature_Sleep",
  ["Verhängnis"] = "Spell_Shadow_CurseOfAchimonde",
  ["Tücke"] = "Ability_Racial_BloodRage",
  ["Manabrand"] = "Spell_Shadow_ManaBurn",
  ["Mananachschub"] = "Spell_Shadow_ManaFeed",
  ["Manaschild"] = "Spell_Shadow_DetectLesserInvisibility",
  ["Manaquelle"] = "Spell_Nature_ManaRegenTotem",
  ["Totem der Manaquelle"] = "Spell_Nature_ManaRegenTotem",
  ["Manaanzapfen"] = "Spell_Arcane_ManaTap",
  ["Manaflut"] = "Spell_Frost_SummonWaterElemental",
  ["Totem der Manaflut"] = "Spell_Frost_SummonWaterElemental",
  ["Fleddern"] = "Ability_Druid_Mangle",
  ["Zerfleischen (Bär)"] = "Ability_Druid_Mangle2",
  ["Zerfleischen (Katze)"] = "Ability_Druid_Mangle2",
  ["Mal der Wildnis"] = "Spell_Nature_Regeneration",
  ["Märtyrertum"] = "Spell_Nature_Tranquility",
  ["Massenbannung"] = "Spell_Arcane_MassDispel",
  ["Meister der Herbeizauberung"] = "INV_Ammo_FireTar",
  ["Meister der Dämonologie"] = "Spell_Shadow_ShadowPact",
  ["Meister der Beschwörung"] = "Spell_Shadow_ImpPhaseShift",
  ["Meister der Taktik"] = "Ability_Hunter_MasterTactitian",
  ["Meister der Täuschung"] = "Spell_Shadow_Charm",
  ["Meister der Elemente"] = "Spell_Fire_MasterOfElements",
  ["Meister der Subtilität"] = "Ability_Rogue_MasterOfSubtlety",
  ["Zermalmen"] = "Ability_Druid_Maul",
  ["Roboschreiter-Lenken"] = "Spell_Nature_Swiftness",
  ["Meditation"] = "Spell_Nature_Sleep",
  ["Nahkampf-Spezialisierung"] = "INV_Axe_02",
  ["Rüstungsschmelze"] = "Spell_Fire_Immolation",
  ["Tier heilen"] = "Ability_Hunter_MendPet",
  ["Mentale Beweglichkeit"] = "Ability_Hibernation",
  ["Mentale Stärke"] = "Spell_Nature_EnchantArmor",
  ["Gedankenschlag"] = "Spell_Shadow_UnholyFrenzy",
  ["Gedankenkontrolle"] = "Spell_Shadow_ShadowWordDominate",
  ["Gedankenschinden"] = "Spell_Shadow_SiphonMana",
  ["Geistesbeherrschung"] = "Spell_Arcane_MindMastery",
  ["Gedankenbesänftigung"] = "Spell_Holy_MindSooth",
  ["Gedankensicht"] = "Spell_Holy_MindVision",
  ["Gedankenbenebelndes Gift"] = "Spell_Nature_NullifyDisease",
  ["Gedankenbenebelndes Gift II"] = "Spell_Nature_NullifyDisease",
  ["Gedankenbenebelndes Gift III"] = "Spell_Nature_NullifyDisease",
  ["Bergbau"] = "Spell_Fire_FlameBlades",
  ["Irreführung"] = "Ability_Hunter_Misdirection",
  ["Elend"] = "Spell_Shadow_Misery",
  ["Spöttischer Schlag"] = "Ability_Warrior_PunishingBlow",
  ["Glühende Rüstung"] = "Ability_Mage_MoltenArmor",
  ["Glühender Zorn"] = "Spell_Fire_MoltenBlood",
  ["Mungobiss"] = "Ability_Hunter_SwiftStrike",
  ["Monstertöten"] = "INV_Misc_Head_Dragon_Black",
  ["Mondfeuer"] = "Spell_Nature_StarFall",
  ["Mondfuror"] = "Spell_Nature_MoonGlow",
  ["Mondschein"] = "Spell_Nature_Sentinal",
  ["Aura des Mondkin"] = "Spell_Nature_MoonGlow",
  ["Mondkingestalt"] = "Spell_Nature_ForceOfNature",
  ["Todbringende Schüsse"] = "Ability_PierceDamage",
  ["Tödlicher Stoß"] = "Ability_Warrior_SavageBlow",
  ["Mehrfachschuss"] = "Ability_UpgradeMoonGlaive",
  ["Mord"] = "Spell_Shadow_DeathScream",
  ["Verstümmeln"] = "Ability_Rogue_ShadowStrikes",
  ["Natürliche Rüstung"] = "Spell_Nature_SpiritArmor",
  ["Vollkommenheit der Natur"] = "Ability_Druid_NaturalPerfection",
  ["Schnellwandlung"] = "Spell_Nature_WispSplode",
  ["Naturalist"] = "Spell_Nature_HealingTouch",
  ["Naturwiderstand"] = "Spell_Nature_ResistNature",
  ["Totem des Naturwiderstands"] = "Spell_Nature_NatureResistanceTotem",
  ["Natur Verwundbarkeit"] = "INV_Misc_QirajiCrystal_03",
  ["Naturfokus"] = "Spell_Nature_HealingWaveGreater",
  ["Anmut der Natur"] = "Spell_Nature_NaturesBlessing",
  ["Griff der Natur"] = "Spell_Nature_NaturesWrath",
  ["Weisheit der Natur"] = "Spell_Frost_Stun",
  ["Reichweite der Natur"] = "Spell_Nature_NatureTouchGrow",
  ["Schnelligkeit der Natur"] = "Spell_Nature_RavenForm",
  ["Negative Ladung"] = "Spell_ChargeNegative",
  ["Netherschutz"] = "Spell_Shadow_NetherProtection",
  ["Nethersturmbanner"] = "INV_Misc_Summerfest_BrazierGreen",
  ["Einbruch der Nacht"] = "Spell_Shadow_Twilight",
  ["Beschützerinstinkt"] = "Ability_Druid_HealingInstincts",
  ["Omen der Klarsicht"] = "Spell_Nature_CrystalBall",
  ["Einhandäxte"] = "INV_Axe_01",
  ["Einhandstreitkolben"] = "INV_Mace_01",
  ["Einhandschwerter"] = "Ability_MeleeDamage",
  ["Einhandwaffen-Spezialisierung"] = "INV_Sword_20",
  ["Öffnen"] = "Temp",
  ["Öffnen - Kein Text"] = "Temp",
  ["Günstige Gelegenheit"] = "Ability_Warrior_WarCry",
  ["Überwältigen"] = "Ability_MeleeDamage",
  ["Schmerzunterdrückung"] = "Spell_Holy_PainSupression",
  ["Panik"] = "INV_Misc_Drum_06",
  ["Paranoia"] = "Spell_Shadow_AuraOfDarkness",
  ["Schädlicher Schattengeist"] = "Spell_Shadow_SoulLeech_3",
  ["Parieren"] = "Ability_Parry",
  ["Orientierung"] = "Ability_Mount_JungleTiger",
  ["Schwäche aufdecken"] = "Spell_Holy_ArcaneIntellect",
  ["Wachsamkeit"] = "Spell_Nature_Sleep",
  ["Dauerfrost"] = "Spell_Frost_Wisp",
  ["Tieraggression"] = "Ability_Druid_Maul",
  ["Tier-Widerstandskraft"] = "Ability_BullRush",
  ["Tiererholung"] = "Ability_Hibernation",
  ["Tier-Widerstand"] = "Spell_Holy_BlessingOfAgility",
  ["Phasenverschiebung"] = "Spell_Shadow_ImpPhaseShift",
  ["Schloss knacken"] = "Spell_Nature_MoonKey",
  ["Taschendiebstahl"] = "INV_Misc_Bag_11",
  ["Durchdringendes Heulen"] = "Spell_Shadow_DeathScream",
  ["Stechendes Eis"] = "Spell_Frost_Frostbolt",
  ["Plattenpanzer"] = "INV_Chest_Plate01",
  ["Spiel mit dem Feuer"] = "Spell_Fire_PlayingWithFire",
  ["Totem der Giftreinigung"] = "Spell_Nature_PoisonCleansingTotem",
  ["Giftspucke"] = "Spell_Nature_CorrosiveBreath",
  ["Gifte"] = "Trade_BrewPoison",
  ["Stangenwaffen"] = "INV_Spear_06",
  ["Stangenwaffen-Spezialisierung"] = "INV_Weapon_Halbard_01",
  ["Verwandlung"] = "Spell_Nature_Polymorph",
  ["Verwandlung: Schwein"] = "Spell_Magic_PolymorphPig",
  ["Verwandlung: Schildkröte"] = "Ability_Hunter_Pet_Turtle",
  ["Portal: Darnassus"] = "Spell_Arcane_PortalDarnassus",
  ["Portal: Exodar"] = "Spell_Arcane_PortalExodar",
  ["Portal: Eisenschmiede"] = "Spell_Arcane_PortalIronForge",
  ["Portal: Orgrimmar"] = "Spell_Arcane_PortalOrgrimmar",
  ["Portal: Shattrath"] = "Spell_Arcane_PortalShattrath",
  ["Portal: Silbermond"] = "Spell_Arcane_PortalSilvermoon",
  ["Portal: Sturmwind"] = "Spell_Arcane_PortalStormWind",
  ["Portal: Donnerfels"] = "Spell_Arcane_PortalThunderBluff",
  ["Portal: Unterstadt"] = "Spell_Arcane_PortalUnderCity",
  ["Positive Ladung"] = "Spell_ChargePositive",
  ["Anspringen"] = "Ability_Druid_SupriseAttack",
  ["Anspringblutung"] = "Ability_Druid_SupriseAttack",
  ["Seele der Macht"] = "Spell_Holy_PowerInfusion",
  ["Machtwort: Seelenstärke"] = "Spell_Holy_WordFortitude",
  ["Machtwort: Schild"] = "Spell_Holy_PowerWordShield",
  ["Gebet der Seelenstärke"] = "Spell_Holy_PrayerOfFortitude",
  ["Gebet der Heilung"] = "Spell_Holy_PrayerOfHealing02",
  ["Gebet der Besserung"] = "Spell_Holy_PrayerOfMendingtga",
  ["Gebet des Schattenschutzes"] = "Spell_Holy_PrayerofShadowProtection",
  ["Gebet der Willenskraft"] = "Spell_Holy_PrayerofSpirit",
  ["Präzision"] = "Ability_Marksmanship",
  ["Instinkt des Raubtiers"] = "Ability_Druid_PredatoryInstincts",
  ["Raubtierschläge"] = "Ability_Hunter_Pet_Cat",
  ["Konzentration"] = "Spell_Shadow_Possession",
  ["Vorbereitung"] = "Spell_Shadow_AntiShadow",
  ["Geistesgegenwart"] = "Spell_Nature_EnchantArmor",
  ["Urfuror"] = "Ability_Racial_Cannibalize",
  ["Prismatischer Mantel"] = "Spell_Arcane_PrismaticCloak",
  ["Sondieren"] = "INV_Misc_Gem_Bloodstone_01",
  ["Schleichen"] = "Ability_Druid_SupriseAttack",
  ["Psychischer Schrei"] = "Spell_Shadow_PsychicScream",
  ["Zuschlagen"] = "INV_Gauntlets_04",
  ["Reinigen"] = "Spell_Nature_Purge",
  ["Läuterung"] = "Spell_Frost_WizardMark",
  ["Läutern"] = "Spell_Holy_Purify",
  ["Reinigende Macht"] = "Spell_Holy_PurifyingPower",
  ["Streben nach Gerechtigkeit"] = "Spell_Holy_PersuitofJustice",
  ["Pyroschlag"] = "Spell_Fire_Fireball02",
  ["Feuerschwall"] = "Spell_Fire_Volcano",
  ["Brandstifter"] = "Spell_Fire_Burnout",
  ["Schnelle Erholung"] = "Ability_Rogue_QuickRecovery",
  ["Schnelle Schüsse"] = "Ability_Warrior_InnerRage",
  ["Schnelligkeit"] = "Ability_Racial_ShadowMeld",
  ["Zorn des Entwirrers"] = "Racial_Orc_BerserkerStrength",
  ["Feuerregen"] = "Spell_Shadow_RainOfFire",
  ["Krallenhieb"] = "Ability_Druid_Disembowel",
  ["Widderreiten"] = "Spell_Nature_Swiftness",
  ["Toben"] = "Ability_Warrior_Rampage",
  ["Distanzwaffen-Spezialisierung"] = "INV_Weapon_Rifle_06",
  ["Schnellfeuer"] = "Ability_Hunter_RunningShot",
  ["Schneller Tod"] = "Ability_Hunter_RapidKilling",
  ["Raptorreiten"] = "Spell_Nature_Swiftness",
  ["Raptorstoß"] = "Ability_MeleeDamage",
  ["Verheeren"] = "Ability_Druid_Ravage",
  ["Bereitschaft"] = "Ability_Hunter_Readiness",
  ["Wiedergeburt"] = "Spell_Nature_Reincarnation",
  ["Rücksichtsloser Sturmangriff"] = "Spell_Nature_AstralRecal",
  ["Tollkühnheit"] = "Ability_CriticalStrike",
  ["Abrechnung"] = "Spell_Holy_BlessingOfStrength",
  ["Erlösung"] = "Spell_Holy_Resurrection",
  ["Verschanzen"] = "Ability_Defend",
  ["Reflexion"] = "Spell_Frost_WindWalkOn",
  ["Regeneration"] = "Spell_Nature_Regenerate",
  ["Nachwachsen"] = "Spell_Nature_ResistNature",
  ["Reinkarnation"] = "Spell_Nature_Reincarnation",
  ["Verstärktes Schild"] = "INV_Shield_06",
  ["Verjüngung"] = "Spell_Nature_Rejuvenation",
  ["Unerbittliche Stöße"] = "Ability_Warrior_DecisiveStrike",
  ["Gnadenlos"] = "Ability_FiegnDead",
  ["Gnadenlose Angriffe"] = "Ability_FiegnDead",
  ["Ferngesteuertes Spielzeug"] = "INV_Misc_Urn_01",
  ["Fluch aufheben"] = "Spell_Holy_RemoveCurse",
  ["Abzeichen entfernen"] = "Temp",
  ["Geringen Fluch aufheben"] = "Spell_Nature_RemoveCurse",
  ["Verwunden"] = "Ability_Gouge",
  ["Erneuerung"] = "Spell_Holy_Renew",
  ["Buße"] = "Spell_Holy_PrayerOfHealing",
  ["Ruhelose Stärke"] = "Ability_CriticalStrike",
  ["Regenerationstotems"] = "Spell_Nature_ManaRegenTotem",
  ["Auferstehung"] = "Spell_Holy_Resurrection",
  ["Gegenschlag"] = "Ability_Warrior_Challange",
  ["Aura der Vergeltung"] = "Spell_Holy_AuraOfLight",
  ["Rache"] = "Ability_Warrior_Revenge",
  ["Rachebetäubung"] = "Ability_Warrior_Revenge",
  ["Nachklingen"] = "Spell_Frost_FrostWard",
  ["Tier wiederbeleben"] = "Ability_Hunter_BeastSoothe",
  ["Rechtschaffene Verteidigung"] = "INV_Shoulder_37",
  ["Zorn der Gerechtigkeit"] = "Spell_Holy_SealOfFury",
  ["Zerfetzen"] = "Ability_GhoulFrenzy",
  ["Riposte"] = "Ability_Warrior_Challange",
  ["Ritual der Verdammnis"] = "Spell_Shadow_AntiMagicShell",
  ["Ritual der Verdammnis'-Effekt"] = "Spell_Arcane_PortalDarnassus",
  ["Tischlein deck dich"] = "Spell_Arcane_MassDispel",
  ["Ritual der Seelen"] = "Spell_Shadow_Shadesofdarkness",
  ["Ritual der Beschwörung"] = "Spell_Shadow_Twilight",
  ["Waffe des Felsbeißers"] = "Spell_Nature_RockBiter",
  ["Rauher Wetzstein"] = "INV_Stone_SharpeningStone_01",
  ["Verderben"] = "Spell_Shadow_ShadowWordPain",
  ["Blutung"] = "Ability_Rogue_Rupture",
  ["Skrupellosigkeit"] = "Ability_Druid_Disembowel",
  ["Opferung"] = "Spell_Shadow_SacrificialShield",
  ["Sicheres Fallen"] = "INV_Feather_01",
  ["Geweihter Kreuzfahrer"] = "Spell_Holy_HolySmite",
  ["Geweihtes Richturteil"] = "Spell_Holy_RighteousFury",
  ["Geweihtes Licht"] = "Spell_Holy_HealingAura",
  ["Aura der Heiligkeit"] = "Spell_Holy_MindVision",
  ["Kopfnuss"] = "Ability_Sap",
  ["Ungezähmte Wut"] = "Ability_Druid_Ravage",
  ["Wilde Schläge"] = "Ability_Racial_BloodRage",
  ["Wildtier ängstigen"] = "Ability_Druid_Cower",
  ["Streuschuss"] = "Ability_GolemStormBolt",
  ["Versengen"] = "Spell_Fire_SoulBurn",
  ["Skorpidgift"] = "Ability_PoisonSting",
  ["Skorpidstich"] = "Ability_Hunter_CriticalShot",
  ["Schrei"] = "Ability_Hunter_Pet_Bat",
  ["Schicksal besiegeln"] = "Spell_Shadow_ChillTouch",
  ["Siegel des Blutes"] = "Spell_Holy_SealOfBlood",
  ["Siegel des Befehls"] = "Ability_Warrior_InnerRage",
  ["Siegel der Gerechtigkeit"] = "Spell_Holy_SealOfWrath",
  ["Siegel des Lichts"] = "Spell_Holy_HealingAura",
  ["Siegel der Rechtschaffenheit"] = "Ability_ThunderBolt",
  ["Siegel der Vergeltung"] = "Spell_Holy_SealOfVengeance",
  ["Siegel der Weisheit"] = "Spell_Holy_RighteousnessAura",
  ["Siegel des Kreuzfahrers"] = "Spell_Holy_HolySmite",
  ["Sengendes Licht"] = "Spell_Holy_SearingLightPriest",
  ["Sengender Schmerz"] = "Spell_Fire_SoulBurn",
  ["Totem der Verbrennung"] = "Spell_Fire_SearingTotem",
  ["Kräfte sammeln"] = "Ability_Hunter_Harass",
  ["Verführung"] = "Spell_Shadow_MindSteal",
  ["Saat der Verderbnis"] = "Spell_Shadow_SeedOfDestruction",
  ["Brodeln"] = "Ability_Druid_ChallangingRoar",
  ["Dämonen spüren"] = "Spell_Shadow_Metamorphosis",
  ["Untote spüren"] = "Spell_Holy_SenseUndead",
  ["Totem des Wachens"] = "Spell_Nature_RemoveCurse",
  ["Schlangenbiss"] = "Ability_Hunter_Quickshot",
  ["Gezahnte Klingen"] = "INV_Sword_17",
  ["Reinlegen"] = "Spell_Nature_MirrorImage",
  ["Untote fesseln"] = "Spell_Nature_Slow",
  ["Schattenaffinität"] = "Spell_Shadow_ShadowWard",
  ["Schattenblitz"] = "Spell_Shadow_ShadowBolt",
  ["Umschlingende Schatten"] = "Spell_Shadow_ShadowEmbrace",
  ["Schattenfokus"] = "Spell_Shadow_BurningSpirit",
  ["Schattenbeherrschung"] = "Spell_Shadow_ShadeTrueSight",
  ["Schattenmacht"] = "Spell_Shadow_ShadowPower",
  ["Schattenschutz"] = "Spell_Shadow_AntiShadow",
  ["Schattenreichweite"] = "Spell_Shadow_ChillTouch",
  ["Schattenwiderstand"] = "Spell_Shadow_AntiShadow",
  ["Aura des Schattenwiderstands"] = "Spell_Shadow_SealOfKings",
  ["Schattentrance"] = "Spell_Shadow_Twilight",
  ["Schattenverwundbarkeit"] = "Spell_Shadow_ShadowBolt",
  ["Schattenzauberschutz"] = "Spell_Shadow_AntiShadow",
  ["Schatten Verwundbarkeit"] = "INV_Misc_QirajiCrystal_05",
  ["Schattenwirken"] = "Spell_Shadow_BlackPlague",
  ["Schattenwort: Tod"] = "Spell_Shadow_DemonicFortitude",
  ["Schattenwort: Schmerz"] = "Spell_Shadow_ShadowWordPain",
  ["Schatten und Flamme"] = "Spell_Shadow_ShadowandFlame",
  ["Schatten des Todes"] = "Spell_Arcane_PrismaticCloak",
  ["Schattenbrand"] = "Spell_Shadow_ScourgeBuild",
  ["Schattengeist"] = "Spell_Shadow_Shadowfiend",
  ["Schattengestalt"] = "Spell_Shadow_Shadowform",
  ["Schattenfurie"] = "Spell_Shadow_Shadowfury",
  ["Schattenschild"] = "Spell_Nature_LightningShield",
  ["Schattenhaftigkeit"] = "Ability_Ambush",
  ["Schattenmimik Passiv"] = "Ability_Ambush",
  ["Schattenschritt"] = "Ability_Rogue_Shadowstep",
  ["Schamanistische Wut"] = "Spell_Nature_ShamanRage",
  ["Geschärfte Klauen"] = "INV_Misc_MonsterClaw_04",
  ["Zertrümmern"] = "Spell_Frost_FrostShock",
  ["Abscheren"] = "Spell_Shadow_FocusedPower",
  ["Panzerschild"] = "Ability_Hunter_Pet_Turtle",
  ["Schild"] = "INV_Shield_04",
  ["Schildhieb"] = "Ability_Warrior_ShieldBash",
  ["Schildhieb - zum Schweigen gebracht"] = "Ability_Warrior_ShieldBash",
  ["Schildblock"] = "Ability_Defend",
  ["Schildschlag"] = "INV_Shield_05",
  ["Schild-Spezialisierung"] = "INV_Shield_06",
  ["Schildwall"] = "Ability_Warrior_ShieldWall",
  ["Tückische Klinge"] = "INV_ThrowingKnife_04",
  ["Schießen"] = "Ability_ShootWand",
  ["Bogenschuss"] = "Ability_Marksmanship",
  ["Schusswaffe abfeuern"] = "Ability_Marksmanship",
  ["Schreddern"] = "Spell_Shadow_VampiricAura",
  ["Schreddernde Angriffe"] = "Spell_Shadow_VampiricAura",
  ["Stille"] = "Spell_Shadow_ImpPhaseShift",
  ["Unterdrückender Schuss"] = "Ability_TheBlackArrow",
  ["Schweigsame Entschlossenheit"] = "Spell_Nature_ManaRegenTotem",
  ["Flagge der Silberschwingen"] = "INV_BannerPVP_02",
  ["Finsterer Stoß"] = "Spell_Shadow_RitualOfSacrifice",
  ["Lebensentzug"] = "Spell_Shadow_Requiem",
  ["Kürschnerei"] = "INV_Misc_Pelt_Wolf_01",
  ["Zerschmettern"] = "Ability_Warrior_DecisiveStrike",
  ["Wappen des Schlächters"] = "INV_Trinket_Naxxramas03",
  ["Schlaf"] = "Spell_Nature_Sleep",
  ["Zerhäckseln"] = "Ability_Rogue_SliceDice",
  ["Verlangsamen"] = "Spell_Nature_Slow",
  ["Langsamer Fall"] = "Spell_Magic_FeatherFall",
  ["Verhüttung"] = "Spell_Fire_FlameBlades",
  ["Göttliche Pein"] = "Spell_Holy_HolySmite",
  ["Schlangenfalle"] = "Ability_Hunter_SnakeTrap",
  ["Robuster Wetzstein"] = "INV_Stone_SharpeningStone_04",
  ["Schallexplosion"] = "Spell_Shadow_Teleport",
  ["Tier besänftigen"] = "Ability_Hunter_BeastSoothe",
  ["Besänftigender Kuss"] = "Spell_Shadow_SoothingKiss",
  ["Seelenfeuer"] = "Spell_Fire_Fireball02",
  ["Seele entziehen"] = "Spell_Shadow_SoulLeech_3",
  ["Seelenverbindung"] = "Spell_Shadow_GatherShadows",
  ["Seelenentzug"] = "Spell_Shadow_LifeDrain02",
  ["Seele brechen"] = "Spell_Arcane_Arcane01",
  ["Seelensteinauferstehung"] = "INV_Misc_Orb_04",
  ["Zaubertempo"] = "Spell_Holy_SearingLight",
  ["Zaubersperre"] = "Spell_Shadow_MindRot",
  ["Zauberkraft"] = "Spell_Arcane_ArcaneTorrent",
  ["Zauberreflexion"] = "Ability_Warrior_ShieldReflection",
  ["Zauber Empfindlichkeit"] = "Spell_Holy_ElunesGrace",
  ["Zauberschutz"] = "Spell_Holy_SpellWarding",
  ["Zauberraub"] = "Spell_Arcane_Arcane02",
  ["Geistbande"] = "Ability_Druid_DemoralizingRoar",
  ["Willensentzug"] = "Spell_Shadow_Requiem",
  ["Geist der Erlösung"] = "INV_Enchant_EssenceEternalLarge",
  ["Einklang des Geistes"] = "Spell_Holy_ReviveChampion",
  ["Spiritueller Fokus"] = "Spell_Arcane_Blink",
  ["Geistige Führung"] = "Spell_Holy_SpiritualGuidence",
  ["Spirituelle Heilung"] = "Spell_Nature_MoonGlow",
  ["Boshafter Furor"] = "Ability_Warrior_Rampage",
  ["Sprinten"] = "Ability_Rogue_Sprint",
  ["Haltung bewahren"] = "Spell_Nature_EnchantArmor",
  ["Sternenfeuer"] = "Spell_Arcane_StarFire",
  ["Sternenfeuerbetäubung"] = "Spell_Arcane_StarFire",
  ["Zorniges Sternenlicht"] = "Spell_Nature_AbolishMagic",
  ["Sternensplitter"] = "Spell_Arcane_StarFire",
  ["Statische Aufladung"] = "Spell_Nature_WispSplode",
  ["Stäbe"] = "INV_Staff_08",
  ["Zuverlässiger Schuss"] = "Ability_Hunter_SteadyShot",
  ["Verstohlenheit"] = "Ability_Stealth",
  ["Steinklauenbetäuben"] = "Spell_Nature_StoneClawTotem",
  ["Totem der Steinklaue"] = "Spell_Nature_StoneClawTotem",
  ["Steingestalt"] = "Spell_Shadow_UnholyStrength",
  ["Steinhaut"] = "Spell_Nature_StoneSkinTotem",
  ["Totem der Steinhaut"] = "Spell_Nature_StoneSkinTotem",
  ["Sturmschlag"] = "Spell_Holy_SealOfMight",
  ["Erdstärke"] = "Spell_Nature_EarthBindTotem",
  ["Totem der Erdstärke"] = "Spell_Nature_EarthBindTotem",
  ["Stärke der Halaani"] = "INV_Trinket_Naxxramas01",
  ["Feststecken"] = "Spell_Shadow_Teleport",
  ["Betäubt"] = "Spell_Frost_Stun",
  ["Feingefühl"] = "Ability_EyeOfTheOwl",
  ["Leiden"] = "Spell_Shadow_BlackPlague",
  ["Streitross beschwören"] = "Ability_Mount_Charger",
  ["Schreckensross herbeirufen"] = "Ability_Mount_Dreadsteed",
  ["Teufelswache beschwören"] = "Spell_Shadow_SummonFelGuard",
  ["Teufelsjäger beschwören"] = "Spell_Shadow_SummonFelHunter",
  ["Teufelsross beschwören"] = "Spell_Nature_Swiftness",
  ["Wichtel beschwören"] = "Spell_Shadow_SummonImp",
  ["Sukkubus beschwören"] = "Spell_Shadow_SummonSuccubus",
  ["Leerwandler beschwören"] = "Spell_Shadow_SummonVoidWalker",
  ["Schlachtross beschwören"] = "Spell_Nature_Swiftness",
  ["Wasserelementar beschwören"] = "Spell_Frost_SummonWaterElemental_2",
  ["Rüstung zerreißen"] = "Ability_Warrior_Sunder",
  ["Unterdrückung"] = "Spell_Shadow_UnsummonBuilding",
  ["Sicherer Stand"] = "Ability_Kick",
  ["Woge des Lichts"] = "Spell_Holy_SurgeOfLight",
  ["Überraschungsangriffe"] = "Ability_Rogue_SurpriseAttack",
  ["Überlebenskünstler"] = "Spell_Shadow_Twilight",
  ["Weitreichende Stöße"] = "Ability_Rogue_SliceDice",
  ["Schnelle Fluggestalt"] = "Ability_Druid_FlightForm",
  ["Rasche Heilung"] = "INV_Relics_IdolofRejuvenation",
  ["Prankenhieb"] = "INV_Misc_MonsterClaw_03",
  ["Schwert-Spezialisierung"] = "INV_Sword_27",
  ["Symbol der Hoffnung"] = "Spell_Holy_SymbolOfHope",
  ["Taktiker"] = "Spell_Nature_EnchantArmor",
  ["Schneiderei"] = "Trade_Tailoring",
  ["Besudeltes Blut"] = "Spell_Shadow_LifeDrain",
  ["Besudelte Gedanken"] = "Spell_Shadow_ShadowPact",
  ["Wildtier zähmen"] = "Ability_Hunter_BeastTaming",
  ["Spott"] = "Spell_Nature_Reincarnation",
  ["Teleportieren: Darnassus"] = "Spell_Arcane_TeleportDarnassus",
  ["Teleportieren: Exodar"] = "Spell_Arcane_TeleportExodar",
  ["Teleportieren: Eisenschmiede"] = "Spell_Arcane_TeleportIronForge",
  ["Teleportieren: Mondlichtung"] = "Spell_Arcane_TeleportMoonglade",
  ["Teleportieren: Orgrimmar"] = "Spell_Arcane_TeleportOrgrimmar",
  ["Teleportieren: Shattrath"] = "Spell_Arcane_TeleportShattrath",
  ["Teleportieren: Silbermond"] = "Spell_Arcane_TeleportSilvermoon",
  ["Teleportieren: Sturmwind"] = "Spell_Arcane_TeleportStormWind",
  ["Teleportieren: Donnerfels"] = "Spell_Arcane_TeleportThunderBluff",
  ["Teleportieren: Unterstadt"] = "Spell_Arcane_TeleportUnderCity",
  ["Temporalriss"] = "Spell_Nature_TimeStop",
  ["Wildes Herz"] = "Ability_Hunter_BeastWithin",
  ["Unbeugsamkeit"] = "INV_Enchant_ShardBrilliantSmall",
  ["Dickes Fell"] = "INV_Misc_Pelt_Bear_03",
  ["Dornen"] = "Spell_Nature_Thorns",
  ["Jagdfieber"] = "Ability_Hunter_ThrilloftheHunt",
  ["Werfen"] = "Ability_Throw",
  ["Wurfwaffen-Spezialisierung"] = "INV_ThrowingAxe_03",
  ["Wurfwaffe"] = "INV_ThrowingKnife_02",
  ["Donnerknall"] = "Spell_Nature_ThunderClap",
  ["Donnerzorn"] = "Spell_Nature_Cyclone",
  ["Donnernde Stöße"] = "Ability_ThunderBolt",
  ["Donnerstampfer"] = "Ability_Hunter_Pet_Gorilla",
  ["Gezeitenfokus"] = "Spell_Frost_ManaRecharge",
  ["Gezeitenbeherrschung"] = "Spell_Nature_Tranquility",
  ["Tigerreiten"] = "Spell_Nature_Swiftness",
  ["Tigerfuror"] = "Ability_Mount_JungleTiger",
  ["Gunst der Zeit"] = "Ability_Rogue_FleetFooted",
  ["Qual"] = "Spell_Shadow_GatherShadows",
  ["Totem"] = "Spell_Nature_StoneClawTotem",
  ["Totem des Ingrimms"] = "Spell_Fire_TotemOfWrath",
  ["Ruf der Totems"] = "Spell_unused",
  ["Totemfokus"] = "Spell_Nature_MoonGlow",
  ["Berührung des Schattens"] = "Spell_Shadow_PsychicScream",
  ["Berührung der Schwäche"] = "Spell_Shadow_DeadofNight",
  ["Zähigkeit"] = "Spell_Holy_Devotion",
  ["Spuren von Silithyst"] = "Spell_Nature_TimeStop",
  ["Wildtiere aufspüren"] = "Ability_Tracking",
  ["Dämonen aufspüren"] = "Spell_Shadow_SummonFelHunter",
  ["Drachkin aufspüren"] = "INV_Misc_Head_Dragon_01",
  ["Elementare aufspüren"] = "Spell_Frost_SummonWaterElemental",
  ["Riesen aufspüren"] = "Ability_Racial_Avatar",
  ["Verborgenes aufspüren"] = "Ability_Stealth",
  ["Humanoide aufspüren"] = "Spell_Holy_PrayerOfHealing",
  ["Untote aufspüren"] = "Spell_Shadow_DarkSummoning",
  ["Beruhigenden Winde"] = "Spell_Nature_Brilliance",
  ["Totem der beruhigenden Winde"] = "Spell_Nature_Brilliance",
  ["Gelassener Geist"] = "Spell_Holy_ElunesGrace",
  ["Gelassenheit"] = "Spell_Nature_Tranquility",
  ["Einlullender Schuss"] = "Spell_Nature_Drowsy",
  ["Fallenbeherrschung"] = "Ability_Ensnare",
  ["Reisegestalt"] = "Ability_Druid_TravelForm",
  ["Baum des Lebens"] = "Ability_Druid_TreeofLife",
  ["Totem des Erdstoßes"] = "Spell_Nature_TremorTotem",
  ["Stammeslederverarbeitung"] = "Spell_Nature_NullWard",
  ["Aura des Volltreffers"] = "Ability_TrueShot",
  ["Untote vertreiben"] = "Spell_Holy_TurnUndead",
  ["Segen der Zwillingsspitze"] = "Spell_Nature_ElementalPrecision_1",
  ["Zweihandäxte"] = "INV_Axe_04",
  ["Zweihandäxte und -Streitkolben"] = "INV_Axe_10",
  ["Zweihandstreitkolben"] = "INV_Mace_04",
  ["Zweihandschwerter"] = "Ability_MeleeDamage",
  ["Zweihandwaffen-Spezialisierung"] = "INV_Axe_09",
  ["Unbewaffnet"] = "Ability_GolemThunderClap",
  ["Unbezwingbarer Wille"] = "Spell_Magic_MageArmor",
  ["Entfesselter Zorn"] = "Spell_Nature_StoneClawTotem",
  ["Untotenreitkunst"] = "Spell_Nature_Swiftness",
  ["Unterwasseratmung"] = "Spell_Shadow_DemonBreath",
  ["Unendlicher Atem"] = "Spell_Shadow_DemonBreath",
  ["Unheilige Macht"] = "Spell_Shadow_ShadowWordDominate",
  ["Entfesselter Zorn"] = "Ability_BullRush",
  ["Entfesselte Wut"] = "Spell_Nature_UnleashedRage",
  ["Instabiles Gebrechen"] = "Spell_Shadow_UnstableAffliction_3",
  ["Instabile Energie"] = "Spell_Lightning_LightningBolt01",
  ["Unumstößlicher Glaube"] = "Spell_Holy_UnyieldingFaith",
  ["Gereizter Magen"] = "Ability_Hunter_Pet_Boar",
  ["Vampirumarmung"] = "Spell_Shadow_UnsummonBuilding",
  ["Vampirberührung"] = "Spell_Holy_Stoicism",
  ["Verschwinden"] = "Ability_Vanish",
  ["Verschwunden"] = "Ability_Vanish",
  ["Rache"] = "Spell_Nature_Purge",
  ["Gifttotem"] = "Spell_Totem_WardOfDraining",
  ["Siegesrausch"] = "Ability_Warrior_Devastate",
  ["Lebenskraft"] = "Spell_Nature_EarthBindTotem",
  ["Heimtückischer Strahl"] = "Spell_Shadow_ShadowBolt",
  ["Üble Gifte"] = "Ability_Rogue_FeignDeath",
  ["Rechtschaffene Schwächung"] = "Spell_Holy_Vindication",
  ["Vipernbiss"] = "Ability_Hunter_AimedShot",
  ["Salve"] = "Ability_Marksmanship",
  ["Zauberstab-Spezialisierung"] = "INV_Wand_01",
  ["Zauberstäbe"] = "Ability_ShootWand",
  ["Kriegsdonner"] = "Ability_WarStomp",
  ["Verzerrt"] = "Spell_Arcane_Arcane04",
  ["Warsong Flagge"] = "INV_BannerPVP_01",
  ["Wasseratmung"] = "Spell_Shadow_DemonBreath",
  ["Wasserschild"] = "Ability_Shaman_WaterShield",
  ["Wassergrab"] = "Spell_Frost_ManaRecharge",
  ["Wasserwandeln"] = "Spell_Frost_WindWalkOn",
  ["Wasserblitz"] = "Spell_Frost_FrostBolt",
  ["Nasses Grab"] = "Spell_Shadow_DemonBreath",
  ["Geschwächte Seele"] = "Spell_Holy_AshesToAshes",
  ["Waffenbeherschung"] = "Ability_Warrior_WeaponMastery",
  ["Waffenschmied"] = "INV_Sword_25",
  ["Wirbelwind"] = "Ability_Whirlwind",
  ["Wille der Verlassenen"] = "Spell_Shadow_RaiseDead",
  ["Windfuror"] = "Spell_Nature_Cyclone",
  ["Angriff des Windzorns"] = "Spell_Nature_Cyclone",
  ["Totem des Windzorns"] = "Spell_Nature_Windfury",
  ["Waffe des Windzorns"] = "Spell_Nature_Cyclone",
  ["Windmauer"] = "Spell_Nature_EarthBind",
  ["Totem der Windmauer"] = "Spell_Nature_EarthBind",
  ["Zurechtstutzen"] = "Ability_Rogue_Trip",
  ["Winterkälte"] = "Spell_Frost_ChillingBlast",
  ["Irrwisch-Geist"] = "Spell_Nature_WispSplode",
  ["Wolfreiten"] = "Spell_Nature_Swiftness",
  ["Wundgift"] = "INV_Misc_Herb_16",
  ["Wundgift II"] = "INV_Misc_Herb_16",
  ["Wundgift III"] = "INV_Misc_Herb_16",
  ["Wundgift IV"] = "INV_Misc_Herb_16",
  ["Wundgift V"] = "INV_Misc_Herb_16",
  ["Zorn"] = "Spell_Nature_AbolishMagic",
  ["Totem des stürmischen Zorns"] = "Spell_Nature_SlowingTotem",
  ["Zorn des Cenarius"] = "Ability_Druid_TwilightsWrath",
  ["Zorn des Astromanten"] = "Spell_Arcane_Arcane02",
  ["Stich des Flügeldrachen"] = "INV_Spear_02",
}
