-- LazyScript Patch: 用户自定义补丁集合
-- 建议所有自定义增强、修正都集中写在本文件，便于升级与维护
-- 放在 LazyScript 目录，主插件加载后自动生效

-- 检测SuperWoW环境的通用函数（放在文件开头）
local function isSuperwowEnvironment()
    return _G.GetPlayerBuffID and _G.CombatLogAdd and _G.SpellInfo
end

-- 自定义条件：ifPFUIBuffDuration（pfUI buff剩余时间判定，支持短buff/药水等）
if lazyScript and lazyScript.masks and lazyScript.bitParsers then
    -- 用法：-ifPFUIBuffDuration<25s=迅捷药水
    local function GetPfUIBuffTimeLeft(buffName)
        if not pfUI or not pfUI.buff or not pfUI.buff.buffs or not pfUI.buff.buffs.buttons then
            
            return 0
        end
        for i = 1, 32 do
            local btn = pfUI.buff.buffs.buttons[i]
            if btn and btn:IsShown() and btn.bid then
                -- 尝试用GameTooltip读取buff名（兼容所有环境）
                local name = nil
                if GetPlayerBuffName then
                    name = GetPlayerBuffName(btn.bid)
                else
                    -- fallback: 用GameTooltip来获取buff名
                    GameTooltip:SetOwner(UIParent, "ANCHOR_NONE")
                    GameTooltip:SetPlayerBuff(btn.bid)
                    name = GameTooltipTextLeft1 and GameTooltipTextLeft1:GetText() or nil
                    GameTooltip:Hide()
                end
                
                if name == buffName then
                    local timeleft = GetPlayerBuffTimeLeft(btn.bid) or 0
                    
                    return timeleft
                end
            else
                if btn then
                    
                end
            end
        end
        
        return 0
    end
    function lazyScript.masks.PfUIBuffDuration(gtLtEq, val, buffName)
        return function()
            local left = GetPfUIBuffTimeLeft(buffName)
            
            local result
            if gtLtEq == "<" then
                result = left < val
            elseif gtLtEq == ">" then
                result = left > val
            else
                result = math.abs(left - val) < 1
            end
            
            return result
        end
    end
    function lazyScript.bitParsers.ifPFUIBuffDuration(bit, actions, masks)
        -- 用法：-ifPFUIBuffDuration<25s=迅捷药水
        if not lazyScript.rebit(bit, "^ifPFUIBuffDuration([<>=])(%d+)s=(.+)$") then
            return false
        end
        local gtLtEq = lazyScript.match1
        local val = tonumber(lazyScript.match2)
        local buffName = lazyScript.match3
        if not buffName or not val then return false end
        table.insert(masks, lazyScript.masks.PfUIBuffDuration(gtLtEq, val, buffName))
        return true
    end
end

-- 自定义条件：ifraiddebuff（Decursive 1.9.x 适配）
if lazyScript and lazyScript.masks and lazyScript.bitParsers then
    -- 不想自动驱散的debuff名称白名单
    local DONT_DISPEL_LIST = {
        ["熔岩镣铐"] = true,  -- 示例：如不想驱散“派对时间！”
        ["冰柱"] = true,
        ["侦测魔法"] = true,
        ["元素诅咒"] = true,
        ["鲁莽诅咒"] = true,
        ["翼龙钉刺"] = true,
        ["毒性光环"] = true,
        ["变异注射"] = true,
        ["雷霆一击"] = true,
        ["脆弱"] = true,
        -- ["另一个debuff名"] = true,
    }
    -- 检查团队/小队/自己是否有人中了可驱散debuff（仅检测，不触发驱散）
    function lazyScript.masks.raiddebuff()
        return function()
            local Dcr_GetUnitDebuffAll = _G["Dcr_GetUnitDebuffAll"]
            if type(Dcr_GetUnitDebuffAll) ~= "function" then
                print("[ifraiddebuff] Dcr_GetUnitDebuffAll not available")
                return false
            end
            -- 收集所有需要检测的单位
            local units = {"player"}
            -- 小队成员
            for i = 1, 4 do
                if UnitExists("party"..i) then
                    table.insert(units, "party"..i)
                end
            end
            -- 团队成员
            local numRaid = GetNumRaidMembers and GetNumRaidMembers() or 0
            for i = 1, numRaid do
                table.insert(units, "raid"..i)
            end
            for _, unit in ipairs(units) do
                -- 只检测30码内的单位
                if CheckInteractDistance(unit, 4) then
                    local debuffs = Dcr_GetUnitDebuffAll(unit)
                    for name, debuff in pairs(debuffs) do
                        -- 跳过白名单debuff
                        if not DONT_DISPEL_LIST[name] then
                            if (debuff.debuff_type == DCR_MAGIC and Dcr_Saved.CureMagic and DCR_CAN_CURE_MAGIC)
                            or (debuff.debuff_type == DCR_CURSE and Dcr_Saved.CureCurse and DCR_CAN_CURE_CURSE)
                            or (debuff.debuff_type == DCR_DISEASE and Dcr_Saved.CureDisease and DCR_CAN_CURE_DISEASE)
                            or (debuff.debuff_type == DCR_POISON and Dcr_Saved.CurePoison and DCR_CAN_CURE_POISON)
                            then
                                return true
                            end
                        end
                    end
                end
            end
            return false
        end
    end
    function lazyScript.bitParsers.ifraiddebuff(bit, actions, masks)
        if bit == "ifraiddebuff" then
            table.insert(masks, lazyScript.masks.raiddebuff())
            return true
        end
        return false
    end
end

-- TWT威胁值条件：ifTWTThreatPercent
if lazyScript and lazyScript.masks and lazyScript.bitParsers then
    function lazyScript.masks.TWTThreatPercent(gtLtEq, val)
        return function()
            local perc = GetTWTThreatPercent and GetTWTThreatPercent() or 0
            if (gtLtEq == ">") then
                return (perc > val)
            elseif (gtLtEq == "=") then
                return (perc == val)
            else
                return (perc < val)
            end
        end
    end

    function lazyScript.bitParsers.ifTWTThreatPercent(bit, actions, masks)
        if (not lazyScript.rebit(bit, "^ifTWTThreatPercent([<=>])(%d+)$")) then
            return false
        end
        local gtLtEq = lazyScript.match1
        local val = tonumber(lazyScript.match2)
        table.insert(masks, lazyScript.masks.TWTThreatPercent(gtLtEq, val))
        return true
    end
end

-- LazyScript Patch: 协助目标持久化与自动恢复（角色专用）

_G.LazyScriptPatchAssistTarget = _G.LazyScriptPatchAssistTarget

-- patch SetAssist，保存协助目标
local function patchSetAssist()
    if LazyScript and lazyScript and lazyScript.SetAssist then
        local orig_SetAssist = lazyScript.SetAssist
        lazyScript.SetAssist = function(a1, a2, a3, a4, a5)
            if a1 and type(a1) == "string" and a1 ~= "" then
                _G.LazyScriptPatchAssistTarget = a1
                _G["LazyScriptPatchAssistTarget"] = _G.LazyScriptPatchAssistTarget
                
            end
            if orig_SetAssist then
                return orig_SetAssist(a1, a2, a3, a4, a5)
            end
        end
        return true
    end
    return false
end

-- 兼容 /lazyscript assist 命令
if SlashCmdList and SlashCmdList["LAZYSCRIPT"] then
    local orig_LazyScriptCmd = SlashCmdList["LAZYSCRIPT"]
    SlashCmdList["LAZYSCRIPT"] = function(msg, ...)
        local lowerMsg = string.lower(msg or "")
        if string.find(lowerMsg, "^assist") then
            local name = string.match(msg, "^assist%s+(%S+)")
            if not name or name == "" then
                name = UnitName("target")
            end
            if name and name ~= "" then
                _G.LazyScriptPatchAssistTarget = name
                _G["LazyScriptPatchAssistTarget"] = _G.LazyScriptPatchAssistTarget
                
            end
        end
        return orig_LazyScriptCmd(msg, unpack(arg))
    end
end

-- 自动恢复协助目标（延迟一帧，确保主插件初始化完毕）
local lsAssistRestoreFrame = CreateFrame("Frame")
lsAssistRestoreFrame:RegisterEvent("PLAYER_ENTERING_WORLD")
lsAssistRestoreFrame:SetScript("OnEvent", function()
    if LazyScript and lazyScript and type(_G.LazyScriptPatchAssistTarget) == "string" and _G.LazyScriptPatchAssistTarget ~= "" then
        lsAssistRestoreFrame:SetScript("OnUpdate", function()
            lazyScript.assistName = _G.LazyScriptPatchAssistTarget
            DEFAULT_CHAT_FRAME:AddMessage("## LazyScript: " .. tostring(_G.LazyScriptPatchAssistTarget) .. " 是当前协助目标。", 1, 0.82, 0)
            if lazyScript.UpdateUI then
                lazyScript.UpdateUI()
            end
            lsAssistRestoreFrame:SetScript("OnUpdate", nil)
        end)
    end
end)

-- Patch Action:Use

-- 防刷屏：协助目标未设置/未找到/未进战斗只提示一次
lazyScript.hasWarnedNoAssistTarget = false
lazyScript.hasWarnedAssistNotFound = false
lazyScript.hasWarnedAssistNotInCombat = false

if lazyScript and lazyScript.pseudoActions and lazyScript.pseudoActions.assist then
    local orig_Assist_IsUsable = lazyScript.pseudoActions.assist.IsUsable
    lazyScript.pseudoActions.assist.IsUsable = function(self, sayNothing)
        -- 未设置协助目标
        if (lazyScript.assistName == nil) then
            if (not lazyScript.hasWarnedNoAssistTarget and not sayNothing) then
                lazyScript.p(ASSIST_TARGET_NOT_SET)
                lazyScript.hasWarnedNoAssistTarget = true
            end
            return false
        end
        -- 未找到协助目标
        if (lazyScript.assistUnitId == nil or string.lower(UnitName(lazyScript.assistUnitId) or "") ~= string.lower(lazyScript.assistName)) then
            lazyScript.assistUnitId = lazyScript.findPlayerUnitId(lazyScript.assistName)
            if lazyScript.assistUnitId == nil then
                if not lazyScript.hasWarnedAssistNotFound and not sayNothing then
                    lazyScript.p(CANNOT_FIND .. lazyScript.assistName .. IN_GROUP_OR_RAID_TO_ASSIST)
                    lazyScript.hasWarnedAssistNotFound = true
                end
                return false
            else
                lazyScript.hasWarnedAssistNotFound = false
            end
        end
        -- 只有协助目标进入战斗后才可用
        if lazyScript.assistUnitId and not UnitAffectingCombat(lazyScript.assistUnitId) then
            if not lazyScript.hasWarnedAssistNotInCombat and not sayNothing then
                lazyScript.p("协助目标未进入战斗。")
                lazyScript.hasWarnedAssistNotInCombat = true
            end
            return false
        end
        -- 目标已进入战斗，重置防刷屏标志
        lazyScript.hasWarnedAssistNotInCombat = false
        return orig_Assist_IsUsable(self, sayNothing)
    end
end

-- 钩住设置协助目标指令，重置所有防刷屏标志
local orig_SetAssist = lazyScript.SetAssist
lazyScript.SetAssist = function(a1, a2, a3, a4, a5)
    lazyScript.hasWarnedNoAssistTarget = false
    lazyScript.hasWarnedAssistNotFound = false
    lazyScript.hasWarnedAssistNotInCombat = false
    if a1 and type(a1) == "string" and a1 ~= "" then
        LazyScriptAssistTarget = a1
        print("[LazyScript Patch] 保存协助目标到 SavedVariables:", LazyScriptAssistTarget)
    end
    if orig_SetAssist then
        return orig_SetAssist(a1, a2, a3, a4, a5)
    end
end

local orig_Action_Use = lazyScript.Action.Use

-- Patch: 支持 targetByName=中文名
if lazyScript and lazyScript.bitParsers and not lazyScript._patchedTargetByName then
    lazyScript._origTargetByNameBitParser = lazyScript.bitParsers.targetByName
    lazyScript.bitParsers.targetByName = function(bit, actions, masks)
        if (not lazyScript.rebit(bit, "^targetByName=(.+)$")) then
            return false
        end
        local name = lazyScript.match1
        local key = "targetByName="..name
        local actionObj = lazyScript.otherActions[key]
        if actionObj == nil then
            actionObj = lazyScript.TargetByNameAction:New(name)
            lazyScript.otherActions[key] = actionObj
        end
        table.insert(actions, actionObj)
        return true
    end
    lazyScript._patchedTargetByName = true
end

-- oRA2 Main Tank 检测功能集成
if not lazyScript then return end

-- 获取主坦列表（oRA2）
lazyScript.GetMainTanks = function()
    -- 兼容 oRA2 Participant/Leader 两种结构
    local tanks = {}
    -- Participant 端
    if oRAPMainTank and oRAPMainTank.core and oRAPMainTank.core.maintanktable then
        for _, name in pairs(oRAPMainTank.core.maintanktable) do
            if name and name ~= "" then
                table.insert(tanks, name)
            end
        end    
    -- Leader 端
    elseif oRALMainTank and oRALMainTank.core and oRALMainTank.core.maintanktable then
        for _, name in pairs(oRALMainTank.core.maintanktable) do
            if name and name ~= "" then
                table.insert(tanks, name)
            end
        end
    end
    return tanks
end

-- 仅支持MT1自动raidN映射和Buff检测（MT1专用补丁，MT2/MT3不处理）

-- 解析 MT1 到实际单位名或单位ID（只支持MT1）
lazyScript.ResolveMTUnitId = function(mtId)
    if mtId ~= "MT1" then return nil end
    local tanks = lazyScript.GetMainTanks()
    local name = tanks[1]
    if name then
        for i = 1, 40 do
            local unit = "raid"..i
            if UnitExists(unit) and UnitName(unit) == name then
                return unit, name
            end
        end
        return nil, name
    end
    -- 没有MT1时，提醒施法者
    if not lazyScript._mt1_warned then
        if DEFAULT_CHAT_FRAME then
            -- DEFAULT_CHAT_FRAME:AddMessage("|cffff0000[LazyScript]|r oRA2中没有设置MT1，请检查团队主坦设置！")
        end
        lazyScript._mt1_warned = true
    end -- 只提醒一次
    return nil, nil
end

-- validateUnitId 仅映射 MT1（其它全部交给主文件处理）
if not lazyScript._origValidateUnitId then
    lazyScript._origValidateUnitId = lazyScript.validateUnitId
end

lazyScript.validateUnitId = function(unitId)
    -- 调试信息：validateUnitId被调用
    DEFAULT_CHAT_FRAME:AddMessage("|cffff00ff[ValidateUnitId]|r 输入: " .. tostring(unitId))

    if unitId == "MT1" then
        local raidId = select(1, lazyScript.ResolveMTUnitId(unitId))
        if raidId and UnitExists(raidId) then
            DEFAULT_CHAT_FRAME:AddMessage("|cffff00ff[ValidateUnitId]|r MT1解析为: " .. tostring(raidId))
            return raidId
        else
            DEFAULT_CHAT_FRAME:AddMessage("|cffff00ff[ValidateUnitId]|r MT1无效，返回占位符")
            return "MT1" -- 永远返回字符串，避免nil导致脚本链断裂
        end
    elseif unitId == "Dead" then
        -- 直接调用死亡检测逻辑
        local deadMembers = {}

        -- 检查小队成员
        for i = 1, GetNumPartyMembers() do
            local unit = "party" .. i
            if UnitExists(unit) and UnitIsDead(unit) then
                local _, class = UnitClass(unit)
                local name = UnitName(unit)
                local priority = ({["PRIEST"] = 1, ["PALADIN"] = 2, ["SHAMAN"] = 3, ["WARRIOR"] = 4})[class] or 99
                table.insert(deadMembers, {unit = unit, name = name, class = class, priority = priority})
            end
        end

        -- 检查团队成员
        local numRaidMembers = GetNumRaidMembers and GetNumRaidMembers() or 0
        if numRaidMembers > 0 then
            for i = 1, numRaidMembers do
                local unit = "raid" .. i
                if UnitExists(unit) and UnitIsDead(unit) then
                    local _, class = UnitClass(unit)
                    local name = UnitName(unit)
                    if name ~= UnitName("player") then
                        local priority = ({["PRIEST"] = 1, ["PALADIN"] = 2, ["SHAMAN"] = 3, ["WARRIOR"] = 4})[class] or 99
                        table.insert(deadMembers, {unit = unit, name = name, class = class, priority = priority})
                    end
                end
            end
        end

        if table.getn(deadMembers) == 0 then
            return "Dead" -- 返回占位符，避免解析阶段报错
        end

        -- 按优先级排序
        table.sort(deadMembers, function(a, b)
            if a.priority == b.priority then
                return a.name < b.name
            end
            return a.priority < b.priority
        end)

        -- 返回第一个死亡队友
        return deadMembers[1].unit
    elseif unitId == "assist" then
        -- 支持 assist 目标
        if lazyScript.assistUnitId and UnitExists(lazyScript.assistUnitId) then
            return lazyScript.assistUnitId
        else
            -- 如果 assistUnitId 不存在或无效，尝试重新查找
            if lazyScript.assistName then
                local unitId = lazyScript.findPlayerUnitId(lazyScript.assistName)
                if unitId then
                    lazyScript.assistUnitId = unitId
                    return unitId
                end
            end
            return "assist" -- 返回占位符，避免解析阶段报错
        end
    end
    local orig = lazyScript._origValidateUnitId(unitId)
    if orig == true then
        return unitId
    elseif orig == false then
        return nil
    else
        return orig
    end
end

-- 只支持MT1 Buff检测自动转为raidN，其它MTx不处理
lazyScript.bitParsers["ifNotMT1HasBuff"] = function(bit, actions, masks)
    if not lazyScript.rebit(bit, "^ifNot(MT1)HasBuff=(.+)$") then
        return false
    end
    local mtId = lazyScript.match1
    local buffName = lazyScript.match2
    table.insert(masks, function()
        local unitId = select(1, lazyScript.ResolveMTUnitId(mtId))
        if not unitId or not UnitExists(unitId) then
            return true -- MT1无效时直接跳过该条件，不报错、不中断
        end

        -- 优先用原始buffName检查
        local buffId = lazyScript.masks.HasBuffOrDebuff(unitId, "buff", nil, buffName, nil, true)
        if buffId then return not buffId end

        -- 再尝试用buffTable里的name（兼容英文/中文）
        if lazyScript.buffTable then
            for k, v in pairs(lazyScript.buffTable) do
                if v.name == buffName or k == buffName then
                    local altBuffId = lazyScript.masks.HasBuffOrDebuff(unitId, "buff", nil, v.name, nil, true)
                    if altBuffId then return not altBuffId end
                end
            end
        end

        -- 同时检查 lazyPriest.buffTable（牧师专用buff）
        if _G.lazyPriest and _G.lazyPriest.buffTable then
            for k, v in pairs(_G.lazyPriest.buffTable) do
                if v.name == buffName or k == buffName then
                    local altBuffId = lazyScript.masks.HasBuffOrDebuff(unitId, "buff", nil, v.name, nil, true)
                    if altBuffId then return not altBuffId end
                end
            end
        end

        return true -- 都没找到，视为没有buff
    end)
    return true
end

-- ================== MT1存在性条件：ifMT1Exists ==================
-- 用法：-ifMT1Exists
-- 作用：判断oRA2主坦MT1是否已设置且raid单位存在，常用于自动Buff主坦时避免死循环或报错
if lazyScript and lazyScript.masks and lazyScript.bitParsers then
    function lazyScript.masks.MT1Exists()
        return function()
            local raidId = nil
            if lazyScript.ResolveMTUnitId then
                raidId = select(1, lazyScript.ResolveMTUnitId("MT1"))
            end
            return raidId and UnitExists(raidId)
        end
    end

    function lazyScript.bitParsers.ifMT1Exists(bit, actions, masks)
        if bit == "ifMT1Exists" then
            table.insert(masks, lazyScript.masks.MT1Exists())
            return true
        end
        return false
    end
end

-- 施法目标自动支持 MT1，其它MTx不处理
if not lazyScript._origCastSpellByRankActionUse then
    lazyScript._origCastSpellByRankActionUse = lazyScript.CastSpellByRankAction.Use
    function lazyScript.CastSpellByRankAction:Use()
        local realTarget = self.target
        if realTarget == "MT1" or realTarget == "MT2" then
            realTarget = select(1, lazyScript.ResolveMTUnitId(realTarget)) or ""
        elseif realTarget == "Dead" then
            realTarget = lazyScript.validateUnitId("Dead") or ""
        end
        local origTarget = self.target
        self.target = realTarget
        local result = lazyScript._origCastSpellByRankActionUse(self)
        self.target = origTarget
        return result
    end
end

-- 为 lazyPriest.Action 也添加 MT1 支持
local function patchLazyPriestAction()
    if not _G.lazyPriest or not _G.lazyPriest.Action then
        return
    end

    if not lazyPriest._origActionUse then
        lazyPriest._origActionUse = lazyPriest.Action.Use
        function lazyPriest.Action:Use()
            -- 检查当前目标是否需要 MT1 转换
            local currentTarget = UnitName("target")
            local needsTargetChange = false
            local originalTarget = nil

            -- 如果当前目标不是 MT1，尝试切换到 MT1
            local mt1UnitId, mt1Name = lazyScript.ResolveMTUnitId("MT1")
            if mt1UnitId and UnitExists(mt1UnitId) then
                local mt1ActualName = UnitName(mt1UnitId)
                if currentTarget ~= mt1ActualName then
                    originalTarget = currentTarget
                    TargetUnit(mt1UnitId)
                    needsTargetChange = true
                end
            end

            -- 执行原始的 Use 方法
            local result = lazyPriest._origActionUse(self)

            -- 如果改变了目标，尝试切换回去
            if needsTargetChange and originalTarget then
                TargetByName(originalTarget, true)
            end

            return result
        end
    end
end

-- 延迟应用补丁，确保 lazyPriest 已加载
local priestPatchFrame = CreateFrame("Frame")
priestPatchFrame:RegisterEvent("PLAYER_ENTERING_WORLD")
priestPatchFrame:SetScript("OnEvent", function()
    C_Timer.After(3, patchLazyPriestAction) -- 延迟3秒，确保 lazyPriest 完全加载
end)

-- 【萨满】一键图腾技能 yjtt 动作注册与实现
if not lazyScript.actions.yjtt then
    lazyScript.actions.yjtt = {
        code = "yjtt",
        name = "一键图腾",
        Use = function(self, target)
            -- 调用 /coe throwset
            if SlashCmdList and SlashCmdList["COE"] then
                SlashCmdList["COE"]("throwset")
            end
            return true
        end,
        GetSlot = function() return nil end,
        FindSpellRanks = function() return nil end,
        IsUsable = function(self, sayNothing) return true end,
    }
end

-- 注册魔法橘子食物buff，用于食物buff检测
if lazyScript and lazyScript.buffTable then
    -- 确保在 LazyScript 主逻辑加载完后再注册
    local function registerMagicOrangeBuff()
        if not lazyScript.buffTable.magicOrangeEat then
            -- 使用正确的英文buff名称注册
            lazyScript.buffTable.magicOrangeEat = lazyScript.Buff:New("magicOrangeEat", "INV_Misc_Food_28", "food")
        end
    end
    
    -- 延迟注册，确保 Lazy Script 完全加载
    local magicOrangeFrame = CreateFrame("Frame")
    magicOrangeFrame:RegisterEvent("PLAYER_ENTERING_WORLD")
    magicOrangeFrame:SetScript("OnEvent", function()
        magicOrangeFrame:SetScript("OnUpdate", function()
            if lazyScript and lazyScript.buffTable and lazyScript.Buff then
                registerMagicOrangeBuff()
                magicOrangeFrame:SetScript("OnUpdate", nil)
            end
        end)
    end)
    
    -- 也可以立即尝试注册（如果已经加载）
    if lazyScript.Buff then
        registerMagicOrangeBuff()
    end
end

-- 【通用】一键驱散技能 Decursive 动作注册与实现
if not lazyScript.actions.Decursive then
    lazyScript.actions.Decursive = {
        code = "Decursive",
        name = "一键驱散",
        Use = function(self, target)
            -- 调用 /Decursive
            if SlashCmdList and SlashCmdList["DECURSIVE"] then
                SlashCmdList["DECURSIVE"]("")
            end
            return true
        end,
        GetSlot = function() return nil end,
        FindSpellRanks = function() return nil end,
        IsUsable = function(self, sayNothing) return true end,
    }
end

-- 【通用】跟随动作 follow 注册与实现
-- 初始化阶段就输出调试信息
DEFAULT_CHAT_FRAME:AddMessage("|cffffff00[Follow Init]|r 正在初始化follow命令...")

if lazyScript and lazyScript.actions then
    DEFAULT_CHAT_FRAME:AddMessage("|cffffff00[Follow Init]|r LazyScript 动作系统已加载")
else
    DEFAULT_CHAT_FRAME:AddMessage("|cffff0000[Follow Init]|r 错误：LazyScript 动作系统未加载！")
end

-- 创建Follow动作类，继承自PseudoAction
lazyScript.FollowAction = {}
function lazyScript.FollowAction:New(target)
    local obj = {}
    setmetatable(obj, { __index = self })
    obj.code = "follow"
    obj.codePattern = "^follow$"
    obj.name = "跟随"
    obj.target = target or ""  -- 设置target属性
    obj.triggersGlobal = false
    obj.requiresSpellStopCasting = false
    obj.everyTimer = 0
    obj.nowAndEveryTimer = 0
    return obj
end

function lazyScript.FollowAction:Use()
    -- LazyScript通过target属性传递目标
    local unitId = self.target
    if unitId == "" then
        unitId = "target"
    end

    -- 验证目标
    if not UnitExists(unitId) then
        return false
    end

    -- 执行跟随
    FollowUnit(unitId)

    -- 记录动作和重置计时器（LazyScript标准）
    lazyScript.recordAction(self.code)
    self.everyTimer = GetTime()
    self.nowAndEveryTimer = self.everyTimer

    return true
end

function lazyScript.FollowAction:GetSlot()
    return nil
end

function lazyScript.FollowAction:FindSpellRanks()
    return nil
end

function lazyScript.FollowAction:IsUsable(sayNothing)
    if not FollowUnit or type(FollowUnit) ~= "function" then
        if not sayNothing then
            lazyScript.p("跟随功能不可用")
        end
        return false
    end
    return true
end

-- 注册基础follow动作（无target）
lazyScript.actions.follow = lazyScript.FollowAction:New()

-- 创建follow动作的缓存表，类似于castSpellByRankActions
if not lazyScript.followActions then
    lazyScript.followActions = {}
end

-- Hook bitParsers.follow来处理follow动作
if not lazyScript.bitParsers.follow then
    lazyScript.bitParsers.follow = function(bit, actions, masks)
        if not lazyScript.rebit(bit, "^follow$") then
            return false
        end
        table.insert(actions, lazyScript.actions.follow)
        return true
    end
end

-- Hook ParseLine函数来处理follow@assist的特殊情况
local originalParseLine = lazyScript.ParseLine
lazyScript.ParseLine = function(line)
    -- 检查是否包含follow@assist语法
    if string.find(line, "follow@assist") then
        -- 检查是否设置了assist目标
        if not lazyScript.assistName then
            lazyScript.p("未设置协助目标，请先使用 /ls assist <玩家名> 设置协助目标")
            return nil
        end

        -- 查找assist目标的unitId
        local assistUnitId = lazyScript.assistUnitId
        if not assistUnitId or not UnitExists(assistUnitId) or
           string.lower(UnitName(assistUnitId) or "") ~= string.lower(lazyScript.assistName) then
            assistUnitId = lazyScript.findPlayerUnitId(lazyScript.assistName)
            if not assistUnitId then
                lazyScript.p("找不到协助目标 " .. lazyScript.assistName .. " 在队伍或团队中")
                return nil
            end
            lazyScript.assistUnitId = assistUnitId
        end

        -- 将follow@assist替换为follow@实际unitId，让LazyScript正常解析
        local modifiedLine = string.gsub(line, "follow@assist", "follow@" .. assistUnitId)
        return originalParseLine(modifiedLine)
    end

    -- 对于其他情况，调用原始函数
    return originalParseLine(line)
end

DEFAULT_CHAT_FRAME:AddMessage("|cffffff00[Follow Init]|r follow动作注册完成，支持 follow, follow@target, follow@assist 语法")

-- ================== 全职业通用 DoT 判定条件 ==================
-- 支持所有职业通过 ifmydot/ifNotmydot 判定目标身上的指定DoT是否为自己施放
-- SuperWoW环境下用Decursive判定，非SuperWoW环境下用DoTimer判定
-- 用法举例：-ifmydot=月火术 -ifmydot=暗言术：痛 -ifmydot=Corruption -ifNotmydot=虫群

local mydot_buffer = {}
local mydot_buffer_size = 2 -- 多帧判定缓冲帧数

function lazyScript.masks.mydot(spell)
    spell = spell or "Corruption"
    mydot_buffer[spell] = mydot_buffer[spell] or {}

    local result = false
    if isSuperwowEnvironment() and Cursive and Cursive.curses then
        local _, targetGuid = UnitExists("target")
        if targetGuid then
            -- 针对三大诅咒特殊处理，不依赖痛苦诅咒
            local specialCurseList = {
                ["鲁莽诅咒"] = true,
                ["暗影诅咒"] = true,
                ["元素诅咒"] = true,
                [Cursive.curses and Cursive.curses.L and Cursive.curses.L["Curse of Recklessness"] or "Curse of Recklessness"] = true,
                [Cursive.curses and Cursive.curses.L and Cursive.curses.L["Curse of Shadow"] or "Curse of Shadow"] = true,
                [Cursive.curses and Cursive.curses.L and Cursive.curses.L["Curse of the Elements"] or "Curse of the Elements"] = true,
            }
            -- 主动刷新目标DoT信息（只在查不到时触发，避免性能问题）
            if not Cursive.curses.guids[targetGuid] or not Cursive.curses.guids[targetGuid][spell] then
                Cursive.curses:ScanGuidForCurse(targetGuid, spell)
            end
            if specialCurseList[spell] then
                -- 只判定本身是否存在，不依赖痛苦诅咒
                local curseData = Cursive.curses.guids[targetGuid] and Cursive.curses.guids[targetGuid][spell]
                if curseData and Cursive.curses:TimeRemaining(curseData) > 0 then
                    result = true
                end
            else
                if Cursive.curses:HasCurse(spell, targetGuid) then
                    result = true
                end
            end
        end
    elseif DoT_OwnSpellOnTarget then
        if DoT_OwnSpellOnTarget(spell) then
            result = true
        end
    end

    -- 更新缓冲区
    table.insert(mydot_buffer[spell], result)
    if table.getn(mydot_buffer[spell]) > mydot_buffer_size then
        table.remove(mydot_buffer[spell], 1)
    end
    -- 多帧判定：只有全部为true才返回true
    local all_true = true
    for i=1, table.getn(mydot_buffer[spell]) do
        if not mydot_buffer[spell][i] then all_true = false break end
    end
    return all_true
end

function lazyScript.bitParsers.ifmydot(bit, actions, masks)
    -- 移除职业限制
    local _, _, spell = string.find(bit, "^ifmydot=?(.*)$")
    if spell == "" then spell = "Corruption" end
    if not spell then return false end
    table.insert(masks, function() return lazyScript.masks.mydot(spell) end)
    return true
end

function lazyScript.bitParsers.ifNotmydot(bit, actions, masks)
    -- 移除职业限制
    local _, _, spell = string.find(bit, "^ifNotmydot=?(.*)$")
    if spell == "" then spell = "Corruption" end
    if not spell then return false end
    table.insert(masks, lazyScript.negWrapper(function() return lazyScript.masks.mydot(spell) end, true))
    return true
end

-- ================== END 全职业通用 DoT 判定条件 ==================

-- ================== END 懒人牧师专用补丁内容 ==================

-- === 自动注册牧师新增法术，无需主文件调用 ===
local function registerLazyPriestSpells()
    if not _G.lazyPriest or not _G.lazyPriest.actions then
        return
    end
    -- 注册动作
    lazyPriest.actions.yssy = lazyPriest.Action:New("yssy", "Spell_Holy_ProclaimChampion_02")
    lazyPriest.actions.yszc = lazyPriest.Action:New("yszc", "Spell_Holy_ChampionsGrace")
    lazyPriest.actions.fnys = lazyPriest.Action:New("fnys", "Spell_Holy_EmpowerChampion")

    -- 同时注册到 lazyScript.actions 中，确保 @target 语法能正常工作
    lazyScript.actions.fnys = lazyPriest.actions.fnys
    lazyScript.actions.yszc = lazyPriest.actions.yszc
    -- 只注册 buff，不注册本地化和 BUFF_TTS，彻底消除报错
    if lazyPriest.buffTable then
        lazyPriest.buffTable.yssy = lazyPriest.Buff:New("神圣勇士", "Spell_Holy_ProclaimChampion_02")
        lazyPriest.buffTable.yszc = lazyPriest.Buff:New("勇士之赐", "Spell_Holy_ChampionsGrace")
        lazyPriest.buffTable.fnys = lazyPriest.Buff:New("赋能勇士", "Spell_Holy_EmpowerChampion")
    end
end

-- 延迟注册，确保主逻辑加载完再注册补丁法术
local frame = CreateFrame("Frame")
frame:RegisterEvent("PLAYER_ENTERING_WORLD")
frame:SetScript("OnEvent", function()
    C_Timer.After(2, registerLazyPriestSpells) -- 延迟2秒注册，确保主逻辑已完成
end)

-- ================== BUFF_TTS 补丁：神圣勇士/勇士之赐/赋能勇士 ==================
do
    local function patchBuffTTS()
        if not lsLocale or not lsLocale.zhCN then return end
        lsLocale.zhCN.BUFF_TTS = lsLocale.zhCN.BUFF_TTS or {}
        lsLocale.zhCN.BUFF_TTS["神圣勇士"] = "神圣勇士"
        lsLocale.zhCN.BUFF_TTS["勇士之赐"] = "勇士之赐"
        lsLocale.zhCN.BUFF_TTS["赋能勇士"] = "赋能勇士"
    end
    if lsLocale and lsLocale.zhCN and lsLocale.zhCN.BUFF_TTS then
        patchBuffTTS()
    else
        local f = CreateFrame and CreateFrame("Frame")
        if f then
            f:RegisterEvent("ADDON_LOADED")
            f:SetScript("OnEvent", function(self, event)
                if event == "ADDON_LOADED" then
                    patchBuffTTS()
                end
            end)
        else
            -- fallback（极早期环境）
            patchBuffTTS()
        end
    end
end
-- ================== END 懒人牧师专用补丁内容 ==================

-- ================== 死亡队友检测与复活目标映射条件 ==================
-- 用法：-ifHasDead 复活术@Dead
-- 功能：检测小队/团队中是否有死亡队友，并将最优先的死亡队友映射到 Dead 单位ID
-- 优先级：牧师 > 圣骑士 > 萨满 > 战士 > 其他职业

if lazyScript and lazyScript.masks and lazyScript.bitParsers then
    -- 职业优先级映射
    local CLASS_PRIORITY = {
        ["PRIEST"] = 1, ["牧师"] = 1,
        ["PALADIN"] = 2, ["圣骑士"] = 2,
        ["SHAMAN"] = 3, ["萨满"] = 3,
        ["WARRIOR"] = 4, ["战士"] = 4,
        ["MAGE"] = 5, ["法师"] = 5,
        ["WARLOCK"] = 6, ["术士"] = 6,
        ["ROGUE"] = 7, ["盗贼"] = 7,
        ["HUNTER"] = 8, ["猎人"] = 8,
        ["DRUID"] = 9, ["德鲁伊"] = 9
    }

    -- 获取所有死亡队友
    local function getDeadPartyMembers()
        local deadMembers = {}

        -- 检查小队成员
        for i = 1, GetNumPartyMembers() do
            local unit = "party" .. i
            if UnitExists(unit) and UnitIsDead(unit) then
                local _, class = UnitClass(unit)
                local name = UnitName(unit)
                table.insert(deadMembers, {
                    unit = unit,
                    name = name,
                    class = class,
                    priority = CLASS_PRIORITY[class] or 99
                })
            end
        end

        -- 检查团队成员
        local numRaidMembers = GetNumRaidMembers and GetNumRaidMembers() or 0
        if numRaidMembers > 0 then
            for i = 1, numRaidMembers do
                local unit = "raid" .. i
                if UnitExists(unit) and UnitIsDead(unit) then
                    local _, class = UnitClass(unit)
                    local name = UnitName(unit)
                    -- 排除自己
                    if name ~= UnitName("player") then
                        table.insert(deadMembers, {
                            unit = unit,
                            name = name,
                            class = class,
                            priority = CLASS_PRIORITY[class] or 99
                        })
                    end
                end
            end
        end

        return deadMembers
    end

    -- 检查是否有人正在复活指定目标
    local function isBeingResurrected(targetName)
        -- 复活术法术名称列表（各职业）
        local resurrectionSpells = {
            "复活术",    -- 牧师
            "救赎",      -- 圣骑士
            "先祖之魂"   -- 萨满
        }

        -- 检查小队成员是否在施法复活术
        for i = 1, GetNumPartyMembers() do
            local unit = "party" .. i
            if UnitExists(unit) and UnitCastingInfo then
                local spellName, _, _, _, _, targetUnit = UnitCastingInfo(unit)
                if spellName and targetUnit and UnitName(targetUnit) == targetName then
                    -- 检查是否是复活类法术
                    for _, resSpell in ipairs(resurrectionSpells) do
                        if spellName == resSpell then
                            return true
                        end
                    end
                end
            end
        end

        -- 检查团队成员是否在施法复活术
        local numRaidMembers = GetNumRaidMembers and GetNumRaidMembers() or 0
        if numRaidMembers > 0 then
            for i = 1, numRaidMembers do
                local unit = "raid" .. i
                if UnitExists(unit) and UnitCastingInfo then
                    local spellName, _, _, _, _, targetUnit = UnitCastingInfo(unit)
                    if spellName and targetUnit and UnitName(targetUnit) == targetName then
                        -- 检查是否是复活类法术
                        for _, resSpell in ipairs(resurrectionSpells) do
                            if spellName == resSpell then
                                return true
                            end
                        end
                    end
                end
            end
        end

        return false
    end

    -- 死亡队友检测mask
    function lazyScript.masks.hasDead()
        return function()
            local deadMembers = getDeadPartyMembers()

            if table.getn(deadMembers) == 0 then
                -- 没有死亡队友，清除Dead映射
                lazyScript._deadTargetUnit = nil
                return false
            end

            -- 按优先级排序
            table.sort(deadMembers, function(a, b)
                if a.priority == b.priority then
                    return a.name < b.name -- 同优先级按名字排序
                end
                return a.priority < b.priority
            end)

            -- 找到第一个没有被复活的死亡队友
            for _, member in ipairs(deadMembers) do
                if not isBeingResurrected(member.name) then
                    -- 映射到Dead单位ID
                    lazyScript._deadTargetUnit = member.unit
                    return true
                end
            end

            -- 所有死亡队友都在被复活中
            lazyScript._deadTargetUnit = nil
            return false
        end
    end

    -- ifHasDead条件解析器
    function lazyScript.bitParsers.ifHasDead(bit, actions, masks)
        if bit == "ifHasDead" then
            table.insert(masks, lazyScript.masks.hasDead())
            return true
        end
        return false
    end
end



-- ================== END 死亡队友检测与复活目标映射条件 ==================

-- ================== PFUI 图腾状态检测条件 ==================
-- 支持检测不显示buff的图腾：战栗图腾、清毒图腾、祛病图腾等
-- 用法：-ifTotemActive=战栗图腾 -ifNotTotemActive=清毒图腾

if lazyScript and lazyScript.masks and lazyScript.bitParsers then

    function lazyScript.masks.totemActive(totemName)
        return function()
            -- 检查 PFUI 是否存在
            if not _G.pfUI then
                return false
            end

            -- 检查 PFUI API 和 libtotem
            if not _G.pfUI.api or not _G.pfUI.api.libtotem then
                return false
            end

            -- 检查GetTotemInfo函数
            if not _G.GetTotemInfo then
                -- 尝试从PFUI环境获取
                local pfUIEnv = _G.pfUI:GetEnvironment()
                if pfUIEnv and pfUIEnv.GetTotemInfo then
                    _G.GetTotemInfo = pfUIEnv.GetTotemInfo
                else
                    return false
                end
            end

            -- 支持多个图腾名称，用逗号分隔
            local totemNames = {}
            for name in string.gfind(totemName, "([^,]+)") do
                local trimmedName = string.gsub(name, "^%s*(.-)%s*$", "%1") -- 去掉前后空格
                table.insert(totemNames, trimmedName)
            end

            -- 检查每个图腾名称
            for _, singleTotemName in ipairs(totemNames) do
                -- 遍历所有图腾槽位（1-4）
                for slot = 1, 4 do
                    local active, name, start, duration, icon = GetTotemInfo(slot)
                    if active and name then
                        -- 检查图腾名称是否匹配（支持模糊匹配）
                        if string.find(name, singleTotemName, 1, true) then
                            -- 检查图腾是否还有剩余时间
                            if start and duration then
                                local remaining = start + duration - GetTime()
                                if remaining > 0 then
                                    return true
                                end
                            end
                        end
                    end
                end
            end

            return false
        end
    end

    function lazyScript.bitParsers.ifTotemActive(bit, actions, masks)
        if not lazyScript.rebit(bit, "^ifTotemActive=(.+)$") then
            return false
        end
        local totemName = lazyScript.match1
        table.insert(masks, lazyScript.masks.totemActive(totemName))
        return true
    end

    function lazyScript.bitParsers.ifNotTotemActive(bit, actions, masks)
        if not lazyScript.rebit(bit, "^ifNotTotemActive=(.+)$") then
            return false
        end
        local totemName = lazyScript.match1
        table.insert(masks, function()
            return not lazyScript.masks.totemActive(totemName)()
        end)
        return true
    end
end

-- ================== END PFUI 图腾状态检测条件 ==================

-- ================== TargetDistance 精确距离判定条件 ==================
-- 支持通过 UnitXP API 获取精确距离进行判定
-- 用法：-ifTargetDistance<8 -ifTargetDistance>30 -ifTargetDistance=5
--      -ifNotTargetDistance<10 -ifNotTargetDistance>25

if lazyScript and lazyScript.masks and lazyScript.bitParsers then
    function lazyScript.masks.targetDistance(operator, distance)
        return function()
            -- 检查是否有目标
            if not UnitExists("target") then
                return false
            end
            
            -- 直接使用 UnitXP API 获取精确距离
            local actualDistance = nil
            if UnitXP then
                actualDistance = UnitXP("distanceBetween", "player", "target")
            end
            
            -- 如果没有获取到精确距离，直接返回false
            if not actualDistance or actualDistance <= 0 then
                return false
            end
            
            -- 根据操作符进行比较
            if operator == "<" then
                return actualDistance < distance
            elseif operator == ">" then
                return actualDistance > distance
            elseif operator == "=" or operator == "==" then
                return math.abs(actualDistance - distance) < 0.5 -- 允许0.5码的误差
            else
                return false
            end
        end
    end

    function lazyScript.bitParsers.ifTargetDistance(bit, actions, masks)
        -- 匹配 ifTargetDistance<数字, ifTargetDistance>数字, ifTargetDistance=数字
        if not lazyScript.rebit(bit, "^ifTargetDistance([<>=])(%d+%.?%d*)$") then
            return false
        end
        local operator = lazyScript.match1
        local distance = tonumber(lazyScript.match2)
        if not distance then
            return false
        end
        
        table.insert(masks, lazyScript.masks.targetDistance(operator, distance))
        return true
    end

    function lazyScript.bitParsers.ifNotTargetDistance(bit, actions, masks)
        -- 匹配 ifNotTargetDistance<数字, ifNotTargetDistance>数字, ifNotTargetDistance=数字
        if not lazyScript.rebit(bit, "^ifNotTargetDistance([<>=])(%d+%.?%d*)$") then
            return false
        end
        local operator = lazyScript.match1
        local distance = tonumber(lazyScript.match2)
        if not distance then
            return false
        end
        
        table.insert(masks, function()
            local result = lazyScript.masks.targetDistance(operator, distance)()
            return not result
        end)
        return true
    end
end
-- ================== END TargetDistance 精确距离判定条件 ==================

-- ================== 小队职业组成判断条件 ==================
-- 根据本小队职业分布判定队伍类型：远程组、法系组、近战组
-- 用法：-ifPartyType=远程组 -ifPartyType=法系组 -ifPartyType=近战组
--      -ifNotPartyType=远程组 -ifNotPartyType=法系组 -ifNotPartyType=近战组

if lazyScript and lazyScript.masks and lazyScript.bitParsers then
    
    -- 获取当前玩家所在小队成员信息
    local function getCurrentPartyMembers()
        local members = {}
        
        -- 如果在团队中，需要找到自己所在的小队
        local numRaidMembers = GetNumRaidMembers and GetNumRaidMembers() or 0
        if numRaidMembers > 0 then
            -- 团队模式：找到自己的subgroup
            local playerSubgroup = nil
            local playerName = UnitName("player")
            for i = 1, numRaidMembers do
                local name, rank, subgroup, level, class = GetRaidRosterInfo(i)
                if name == playerName then
                    playerSubgroup = subgroup
                    break
                end
            end
            
            -- 收集同一subgroup的其他成员（不包含自己）
            if playerSubgroup then
                for i = 1, numRaidMembers do
                    local name, rank, subgroup, level, class = GetRaidRosterInfo(i)
                    if subgroup == playerSubgroup and name ~= playerName then
                        table.insert(members, {
                            name = name,
                            class = class,
                            unit = "raid"..i
                        })
                    end
                end
            end
        else
            -- 小队模式：只添加小队成员，不包含自己
            for i = 1, GetNumPartyMembers() do
                if UnitExists("party"..i) then
                    local _, class = UnitClass("party"..i)
                    table.insert(members, {
                        name = UnitName("party"..i),
                        class = class,
                        unit = "party"..i
                    })
                end
            end
        end
        
        return members
    end
    
    -- 统计小队职业分布并判定队伍类型
    local function getPartyType()
        local members = getCurrentPartyMembers()
        local classCounts = {}
        
        -- 统计各职业数量
        for _, member in ipairs(members) do
            if member.class then
                classCounts[member.class] = (classCounts[member.class] or 0) + 1
            end
        end
        
        -- 重新分类职业
        local hunterCount = (classCounts["HUNTER"] or 0) + (classCounts["猎人"] or 0)  -- 远程组：猎人
        local priestCount = (classCounts["PRIEST"] or 0) + (classCounts["牧师"] or 0)
        local mageCount = (classCounts["MAGE"] or 0) + (classCounts["法师"] or 0)
        local warlockCount = (classCounts["WARLOCK"] or 0) + (classCounts["术士"] or 0)
        local casterCount = priestCount + mageCount + warlockCount  -- 法系组：牧师+法师+术士
        local rogueCount = (classCounts["ROGUE"] or 0) + (classCounts["盗贼"] or 0)
        local warriorCount = (classCounts["WARRIOR"] or 0) + (classCounts["战士"] or 0)
        local meleeCount = rogueCount + warriorCount  -- 近战组：盗贼+战士
        
        -- 找出人数最多的组
        local maxCount = math.max(hunterCount, casterCount, meleeCount)
        local dominantGroup = "混合组"
        
        -- 统计有多少组达到最大人数
        local maxGroups = {}
        if hunterCount == maxCount and maxCount > 0 then
            table.insert(maxGroups, "远程组")
        end
        if casterCount == maxCount and maxCount > 0 then
            table.insert(maxGroups, "法系组")
        end
        if meleeCount == maxCount and maxCount > 0 then
            table.insert(maxGroups, "近战组")
        end
        
        -- 只有一个组达到最大人数时，返回该组；否则返回混合组
        if table.getn(maxGroups) == 1 then
            dominantGroup = maxGroups[1]
        end
        
        return dominantGroup
    end
    
    function lazyScript.masks.partyType(targetTypes)
        return function()
            local currentType = getPartyType()
            
            -- 支持多个类型，用逗号分隔
            local typeList = {}
            for partyType in string.gfind(targetTypes, "([^,]+)") do
                local trimmedType = string.gsub(partyType, "^%s*(.-)%s*$", "%1") -- 去掉前后空格
                table.insert(typeList, trimmedType)
            end
            
            -- 检查当前类型是否在目标类型列表中
            for _, targetType in ipairs(typeList) do
                if currentType == targetType then
                    return true
                end
            end
            
            return false
        end
    end

    function lazyScript.bitParsers.ifPartyType(bit, actions, masks)
        if not lazyScript.rebit(bit, "^ifPartyType=(.+)$") then
            return false
        end
        local partyTypes = lazyScript.match1
        table.insert(masks, lazyScript.masks.partyType(partyTypes))
        return true
    end

    function lazyScript.bitParsers.ifNotPartyType(bit, actions, masks)
        if not lazyScript.rebit(bit, "^ifNotPartyType=(.+)$") then
            return false
        end
        local partyTypes = lazyScript.match1
        table.insert(masks, function()
            return not lazyScript.masks.partyType(partyTypes)()
        end)
        return true
    end
end

-- ================== END 小队职业组成判断条件 ==================

-- ================== SuperWoW API兼容性补丁 ==================
-- 只替换原版API为SuperWoW API，不注册新条件

if isSuperwowEnvironment() and lazyScript then
    if lazyScript.Tooltip then
        lazyScript.Tooltip.GetUnitBuffOrDebuffTextLeftN = function(self, unitId, buffOrDebuff, buffId, lineNum)
            if lineNum == 1 then
                local thisTexture, applications, auraId, arg4, arg5
                if buffOrDebuff == "buff" then
                    thisTexture, applications, auraId, arg4, arg5 = UnitBuff(unitId, buffId)
                else
                    thisTexture, applications, auraId, arg4, arg5 = UnitDebuff(unitId, buffId)
                end
                local spellName = nil
                -- 只用 arg4 作为 spellID 判定（debuff），buff用 auraId
                if buffOrDebuff == "debuff" then
                    if tonumber(arg4) and SpellInfo then
                        local tryName = SpellInfo(tonumber(arg4))
                        if tryName and tryName ~= "" then
                            spellName = tryName
                        end
                    end
                else
                    if tonumber(auraId) and SpellInfo then
                        local tryName = SpellInfo(tonumber(auraId))
                        if tryName and tryName ~= "" then
                            spellName = tryName
                        end
                    end
                end
                -- 兼容极端情况，兜底用图标匹配
                if not spellName and thisTexture and lazyScript.buffTable then
                    for buffKey, buffObj in pairs(lazyScript.buffTable) do
                        local match = false
                        if buffObj.texture == thisTexture then
                            match = true
                        else
                            local thisTextureName = string.match(thisTexture, "([^\\]+)$") or ""
                            local buffTextureName = string.match(buffObj.texture or "", "([^\\]+)$") or ""
                            if thisTextureName ~= "" and thisTextureName == buffTextureName then
                                match = true
                            end
                        end
                        if match then
                            spellName = buffObj.name or buffKey
                            break
                        end
                    end
                end
                return spellName
            end
            return nil
        end
    end

    -- 保持原版的 HasBuffOrDebuff 函数不变，只替换 tooltip 获取
    -- 这样就能利用原版的完整匹配逻辑
end

-- ================== END SuperWoW API兼容性补丁 ==================

-- ================== 附近敌人数量判断条件 ==================
-- 使用ShaguScan API获取10码内敌人数量
-- 用法：-ifNearbyEnemies>3 -ifNearbyEnemies<2

if lazyScript and lazyScript.masks and lazyScript.bitParsers then
    
    -- 获取10码内敌人数量（移到全局作用域）- 添加缓存优化
    local nearbyEnemyCache = {
        lastUpdate = 0,
        count = 0,
        updateInterval = 3.0 -- 每3秒更新一次缓存，进一步减少调用频率
    }

    _G.LazyScript_GetNearbyEnemyCount = function()
        local currentTime = GetTime()

        -- 如果缓存还有效，直接返回缓存结果
        if currentTime - nearbyEnemyCache.lastUpdate < nearbyEnemyCache.updateInterval then
            return nearbyEnemyCache.count
        end

        -- 检查ShaguScan是否可用
        if not _G.ShaguScan or not _G.ShaguScan.core or not _G.ShaguScan.core.guids then
            nearbyEnemyCache.count = 0
            nearbyEnemyCache.lastUpdate = currentTime
            return 0
        end

        local count = 0
        local currentTime = GetTime()

        -- 遍历ShaguScan检测到的所有单位
        for guid, time in pairs(_G.ShaguScan.core.guids) do
            -- 跳过太旧的GUID（超过30秒的），减少无效检查
            if currentTime - time > 30 then
                -- 跳过过期的GUID
            else
                -- 精确检测：敌对的、战斗中的、存活的敌人
                if UnitExists(guid) then
                    -- 1. 必须是敌对单位
                    if UnitCanAttack("player", guid) then
                        -- 2. 必须是存活的
                        if not UnitIsDead(guid) then
                            -- 3. 必须在战斗中
                            if UnitAffectingCombat(guid) then
                                -- 4. 检查距离是否在10码内
                                local distance = nil
                                if UnitXP then
                                    distance = UnitXP("distanceBetween", "player", guid)
                                end

                                -- 只统计10码内的敌人
                                if distance and distance <= 10 then
                                    count = count + 1
                                end
                            end
                        end
                    end
                end
            end
        end

        -- 更新缓存
        nearbyEnemyCache.count = count
        nearbyEnemyCache.lastUpdate = currentTime
        return count
    end
    
    -- 本地引用
    local getNearbyEnemyCount = _G.LazyScript_GetNearbyEnemyCount
    
    -- 附近敌人数量判断mask（只支持 > 和 <）
    function lazyScript.masks.nearbyEnemies(operator, count)
        return function()
            local actualCount = getNearbyEnemyCount()
            
            if operator == ">" then
                return actualCount > count
            elseif operator == "<" then
                return actualCount < count
            else
                return false
            end
        end
    end
    
    -- ifNearbyEnemies条件解析器（只支持 > 和 <）
    function lazyScript.bitParsers.ifNearbyEnemies(bit, actions, masks)
        local operator, count = string.match(bit, "^ifNearbyEnemies([<>])(%d+)$")
        if not operator or not count then
            return false
        end
        
        count = tonumber(count)
        if not count then
            return false
        end
        
        table.insert(masks, lazyScript.masks.nearbyEnemies(operator, count))
        return true
    end
end
-- ================== END附近敌人数量判断条件 ==================

-- ================== 附近Boss战判定条件 ==================
-- 用法：-ifNearbyBoss=克苏恩,奈法利安
if lazyScript and lazyScript.masks and lazyScript.bitParsers then
    -- 判断是否正在与指定Boss战斗（使用BigWigs API或直接选中Boss判断）
    function lazyScript.masks.nearbyBoss(bossNames)
        return function()
            -- 支持多个Boss名称，用逗号分隔
            local bossList = {}
            for name in string.gfind(bossNames, "([^,]+)") do
                local trimmed = string.gsub(name, "^%s*(.-)%s*$", "%1")
                table.insert(bossList, trimmed)
            end

            -- 优先检查：如果直接选中了需要判断的boss，且boss已进入战斗
            if UnitExists("target") then
                local targetName = UnitName("target")
                if targetName then
                    -- 检查目标是否是指定的Boss之一
                    for _, bossName in ipairs(bossList) do
                        if targetName == bossName then
                            -- 检查目标是否已进入战斗
                            if UnitAffectingCombat("target") then
                                return true
                            end
                        end
                    end
                end
            end

            -- 检查BigWigs是否可用，使用pcall保护避免出错
            -- 使用pcall保护整个BigWigs检查过程
            local bigwigsSuccess, bigwigsResult = pcall(function()
                if not BigWigs then return false end
                if not BigWigs.modules then return false end
                if type(BigWigs.modules) ~= "table" then return false end

                -- 遍历BigWigs所有模块
                for moduleName, module in pairs(BigWigs.modules) do
                    if type(module) == "table" and module.core and module.engaged then
                        -- 获取Boss显示名称
                        local bossDisplayName = module.translatedName or moduleName
                        if type(bossDisplayName) == "string" then
                            -- 检查是否匹配指定的Boss名称
                            for _, bossName in ipairs(bossList) do
                                if bossDisplayName == bossName or moduleName == bossName then
                                    return true
                                end
                            end
                        end
                    end
                end
                return false
            end)

            -- 如果BigWigs调用成功且返回true，则返回true
            if bigwigsSuccess and bigwigsResult then
                return true
            end

            return false
        end
    end

    function lazyScript.bitParsers.ifNearbyBoss(bit, actions, masks)
        if not lazyScript.rebit(bit, "^ifNearbyBoss=(.+)$") then
            return false
        end
        local bossNames = lazyScript.match1
        table.insert(masks, lazyScript.masks.nearbyBoss(bossNames))
        return true
    end
end

-- ================== END 附近Boss战判定条件 ==================

-- ================== 图腾距离判断条件 ==================
-- 使用 ShaguScan 扫描附近图腾并获取距离
-- 用法：-ifTotemDistance<20=治疗之泉图腾 -ifTotemDistance>30=火舌图腾
--      -ifNotTotemDistance<15=法力之泉图腾

if lazyScript and lazyScript.masks and lazyScript.bitParsers then
    -- 获取指定图腾的距离 - 简化版本，只检测pet类型
    local totemDistanceCache = {}
    local totemOwnerCache = {} -- 缓存图腾所有者信息

    local function getTotemDistance(totemName)
        local currentTime = GetTime()

        -- 开始检测图腾

        -- 检查缓存是否有效（3秒内）
        if totemDistanceCache[totemName] and
           currentTime - totemDistanceCache[totemName].lastUpdate < 3.0 then
            return totemDistanceCache[totemName].distance
        end

        local playerName = UnitName("player")
        local checkedCount = 0
        local maxChecks = 10 -- 限制最多检查10个友善单位（因为可能有其他玩家的图腾）

        -- 遍历 ShaguScan 扫描到的所有单位 GUID
        for guid, time in pairs(_G.ShaguScan.core.guids) do
            -- 跳过太旧的GUID（超过30秒的）
            if currentTime - time > 30 then
                -- 跳过过期的GUID
            else
                checkedCount = checkedCount + 1
                if checkedCount > maxChecks then
                    break -- 避免检查过多单位
                end

                -- 检查单位是否存在
                if UnitExists(guid) then
                    -- 使用ShaguScan的pet定义：非玩家但被玩家控制的单位
                    local isPlayer = UnitIsPlayer(guid)
                    local isControlled = UnitPlayerControlled(guid)
                    local isPet = not isPlayer and isControlled

                    -- 使用ShaguScan的pet定义检查图腾
                    if isPet then
                        local unitName = UnitName(guid)

                        if unitName then
                            -- 使用模糊匹配：检查单位名称是否包含图腾名称
                            -- 这样可以匹配带等级的图腾（如"法力之泉图腾V"匹配"法力之泉图腾"）
                            local nameMatch = string.find(unitName, totemName, 1, true)


                            if nameMatch then
                        -- 检查图腾的创造者信息
                        local playerName = UnitName("player")



                        -- 先检查缓存的图腾所有者信息
                        local createdBy = nil
                        if totemOwnerCache[guid] and
                           currentTime - totemOwnerCache[guid].lastUpdate < 10.0 then
                            createdBy = totemOwnerCache[guid].owner

                        else


                            -- 方法1: 使用 UnitCreatedBy (如果存在)
                            if UnitCreatedBy then
                                createdBy = UnitCreatedBy(guid)
                            end

                        -- 方法2: 优化的tooltip检查，使用共享tooltip和缓存
                        if not createdBy then
                            -- 使用全局共享的tooltip，避免重复创建
                            if not _G.LazyScript_SharedTooltip then
                                _G.LazyScript_SharedTooltip = CreateFrame("GameTooltip", "LazyScriptSharedTooltip", nil, "GameTooltipTemplate")
                            end

                            local tooltip = _G.LazyScript_SharedTooltip
                            tooltip:SetOwner(WorldFrame, "ANCHOR_NONE")
                            tooltip:ClearLines()
                            tooltip:SetUnit(guid)

                            -- 限制检查的行数，减少性能消耗
                            local maxLines = math.min(tooltip:NumLines(), 3) -- 最多检查3行

                            for i = 1, maxLines do
                                local line = getglobal("LazyScriptSharedTooltipTextLeft" .. i)
                                if line then
                                    local text = line:GetText()

                                    if text then
                                        -- 查找包含"的"字符的行，可能是"某某某的创造物"
                                        if string.find(text, "的") then
                                            -- 尝试提取创造者名称
                                            local ownerName = string.match(text, "(.+)的")
                                            if ownerName then
                                                createdBy = ownerName
                                                break
                                            end
                                        end
                                    end
                                end
                            end

                            tooltip:Hide()
                        end

                        -- 方法3: 如果还是没有，尝试检查图腾的主人单位
                        if not createdBy then
                            -- 尝试检查图腾是否有主人目标
                            local ownerGuid = guid .. "target"
                            if UnitExists(ownerGuid) then
                                local ownerName = UnitName(ownerGuid)
                                if ownerName then
                                    createdBy = ownerName
                                end
                            end
                        end

                        -- 缓存图腾所有者信息（无论是否找到）
                        totemOwnerCache[guid] = {owner = createdBy, lastUpdate = currentTime}
                        end -- 结束else分支

                        -- 检查是否是玩家创造的图腾
                        local isMyTotem = false
                        if createdBy and playerName and tostring(createdBy) == tostring(playerName) then
                            isMyTotem = true
                        end

                        -- 如果确认是自己的图腾，获取距离
                        if isMyTotem and UnitXP then
                            local distance = UnitXP("distanceBetween", "player", guid)

                            if distance and distance > 0 then
                                -- 缓存结果
                                totemDistanceCache[totemName] = {distance = distance, lastUpdate = currentTime}

                                return distance
                            end
                        elseif isMyTotem and not UnitXP then

                        else

                        end

                        -- 如果找到了匹配名称的图腾但不是自己的，继续查找
                        -- （可能有多个同名图腾，需要找到自己的那个）
                            end -- 结束图腾名称匹配的if
                        else

                        end -- 结束unitName检查的if
                    else

                    end -- 结束Pet类型检查的if
                else

                end -- 结束UnitExists检查的if
            end -- 结束GUID时间检查的else
        end -- 结束for循环

        -- 没找到图腾，缓存nil结果

        totemDistanceCache[totemName] = {distance = nil, lastUpdate = currentTime}
        return nil
    end

    function lazyScript.masks.totemDistance(operator, distance, totemNames)
        return function()

            -- 支持多个图腾名，用逗号分隔
            local totemList = {}

            -- 使用更简单的分割方法
            local start = 1
            while true do
                local commaPos = string.find(totemNames, ",", start)
                local totemName

                if commaPos then
                    totemName = string.sub(totemNames, start, commaPos - 1)
                    start = commaPos + 1
                else
                    totemName = string.sub(totemNames, start)
                end

                -- 去除前后空格
                totemName = string.gsub(totemName, "^%s*(.-)%s*$", "%1")
                if totemName ~= "" then
                    table.insert(totemList, totemName)
                end

                if not commaPos then break end
            end

            -- 检查每个图腾的距离
            for i = 1, table.getn(totemList) do
                local totemName = totemList[i]
                local actualDistance = getTotemDistance(totemName)

                if actualDistance then
                    -- 根据操作符进行比较
                    local result = false
                    if operator == "<" then
                        result = actualDistance < distance
                    elseif operator == ">" then
                        result = actualDistance > distance
                    elseif operator == "=" or operator == "==" then
                        result = math.abs(actualDistance - distance) < 1 -- 允许1码的误差
                    end

                    -- 如果任何一个图腾满足条件，返回true
                    if result then
                        return true
                    end
                end
            end

            -- 如果没有找到任何图腾或都不满足条件，返回false
            return false
        end
    end

    function lazyScript.bitParsers.ifTotemDistance(bit, actions, masks)
        -- 匹配 ifTotemDistance<数字=图腾名, ifTotemDistance>数字=图腾名, ifTotemDistance=数字=图腾名
        if not lazyScript.rebit(bit, "^ifTotemDistance([<>=])(%d+%.?%d*)=(.+)$") then
            return false
        end
        local operator = lazyScript.match1
        local distance = tonumber(lazyScript.match2)
        local totemNames = lazyScript.match3
        if not distance or not totemNames then
            return false
        end

        table.insert(masks, lazyScript.masks.totemDistance(operator, distance, totemNames))
        return true
    end

    function lazyScript.bitParsers.ifNotTotemDistance(bit, actions, masks)
        -- 匹配 ifNotTotemDistance<数字=图腾名, ifNotTotemDistance>数字=图腾名, ifNotTotemDistance=数字=图腾名
        if not lazyScript.rebit(bit, "^ifNotTotemDistance([<>=])(%d+%.?%d*)=(.+)$") then
            return false
        end
        local operator = lazyScript.match1
        local distance = tonumber(lazyScript.match2)
        local totemNames = lazyScript.match3
        if not distance or not totemNames then
            return false
        end

        table.insert(masks, function()
            local result = lazyScript.masks.totemDistance(operator, distance, totemNames)()
            return not result
        end)
        return true
    end
end

-- ================== END 图腾距离判断条件 ==================

-- ================== 附近敌人名称战判定条件 ==================
-- 用法：-ifNearbyEnemyName=圣所龙人,圣所龙族
if lazyScript and lazyScript.masks and lazyScript.bitParsers then
    -- 判断附近50码内是否有任意指定名称的敌人且已进战斗 - 添加缓存优化
    local nearbyEnemyNameCache = {}

    function lazyScript.masks.nearbyEnemyName(enemyNames)
        return function()
            local currentTime = GetTime()
            local cacheKey = enemyNames

            -- 检查缓存是否有效（3秒内）- 进一步减少调用频率
            if nearbyEnemyNameCache[cacheKey] and
               currentTime - nearbyEnemyNameCache[cacheKey].lastUpdate < 3.0 then
                return nearbyEnemyNameCache[cacheKey].result
            end

            if not _G.ShaguScan or not _G.ShaguScan.core or not _G.ShaguScan.core.guids then
                nearbyEnemyNameCache[cacheKey] = {result = false, lastUpdate = currentTime}
                return false
            end

            -- 支持多个名称，用逗号分隔
            local nameList = {}
            for name in string.gfind(enemyNames, "([^,]+)") do
                local trimmed = string.gsub(name, "^%s*(.-)%s*$", "%1")
                table.insert(nameList, trimmed)
            end

            -- 优先检查：如果直接选中了需要判断的敌人，且敌人已进入战斗
            if UnitExists("target") then
                local targetName = UnitName("target")
                if targetName then
                    -- 检查目标名称是否匹配指定的敌人名称之一
                    for _, enemyName in ipairs(nameList) do
                        if targetName == enemyName then
                            -- 检查目标是否为敌对、存活且已进入战斗
                            if UnitCanAttack("player", "target") and
                               not UnitIsDead("target") and
                               UnitAffectingCombat("target") then
                                nearbyEnemyNameCache[cacheKey] = {result = true, lastUpdate = currentTime}
                                return true
                            end
                        end
                    end
                end
            end

            for guid, time in pairs(_G.ShaguScan.core.guids) do
                -- 跳过太旧的GUID（超过30秒的），减少无效检查
                if currentTime - time > 30 then
                    -- 跳过过期的GUID
                else
                    -- 精确检测：敌对的、战斗中的、存活的、指定名称的敌人
                    if UnitExists(guid) then
                        -- 1. 必须是敌对单位
                        if UnitCanAttack("player", guid) then
                            -- 2. 必须是存活的
                            if not UnitIsDead(guid) then
                                -- 3. 必须在战斗中
                                if UnitAffectingCombat(guid) then
                                    -- 4. 检查名称是否匹配
                                    local unitName = UnitName(guid)
                                    if unitName then
                                        for _, targetName in ipairs(nameList) do
                                            if unitName == targetName then
                                                -- 5. 检查距离（50码以内）
                                                local distance = nil
                                                if UnitXP then
                                                    distance = UnitXP("distanceBetween", "player", guid)
                                                end
                                                if distance and distance <= 50 then
                                                    nearbyEnemyNameCache[cacheKey] = {result = true, lastUpdate = currentTime}
                                                    return true
                                                end
                                            end
                                        end
                                    end
                                end
                            end
                        end
                    end
                end
            end

            nearbyEnemyNameCache[cacheKey] = {result = false, lastUpdate = currentTime}
            return false
        end
    end

    function lazyScript.bitParsers.ifNearbyEnemyName(bit, actions, masks)
        if not lazyScript.rebit(bit, "^ifNearbyEnemyName=(.+)$") then
            return false
        end
        local enemyNames = lazyScript.match1
        table.insert(masks, lazyScript.masks.nearbyEnemyName(enemyNames))
        return true
    end
end
-- ================== END 附近敌人名称战判定条件 ==================