<Ui xmlns="http://www.blizzard.com/wow/ui/" 
xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" 
xsi:schemaLocation="http://www.blizzard.com/wow/ui/
C:\Projects\WoW\Bin\Interface\FrameXML\UI.xsd">
	
	
	<Frame name="LazyScriptImmunityExceptionCriteriaEditFrame" toplevel="true" enableMouse="true" 
	movable="true" parent="UIParent" hidden="true">
		<Size>
			<AbsDimension x="500" y="430"/>
		</Size>
		<Anchors>
			<Anchor point="CENTER"/>
		</Anchors>
		
		<Backdrop name="$parentBackdrop" bgFile="Interface\TutorialFrame\TutorialFrameBackground"
		edgeFile="Interface\DialogFrame\UI-DialogBox-Border" tile="true">
			<EdgeSize>
				<AbsValue val="32"/>
			</EdgeSize>
			<TileSize>
				<AbsValue val="32"/>
			</TileSize>
			<BackgroundInsets>
				<AbsInset left="5" right="5" top="5" bottom="5"/>
			</BackgroundInsets>
		</Backdrop>
		
		<Layers>
			<Layer level="ARTWORK">
				
				<FontString name="$parentTitle" inherits="GameFontHighlightLarge" justifyH="CENTER" text="Immunities_Criteria_Editor">
					<Size>
						<AbsDimension x="400" y="15"/>
					</Size>
					<Anchors>
						<Anchor point="TOPLEFT" relativeTo="$parent" relativePoint="TOPLEFT">
							<Offset>
								<AbsDimension x="25" y="-20"/>
							</Offset>
						</Anchor>
					</Anchors>
				</FontString>
				
				<FontString name="$parentFormNameInfo" inherits="GameFontHighlight" justifyH="LEFT" text="Shows_all_learned_immunities">
					<Size>
						<AbsDimension x="450" y="18"/>
					</Size>
					<Anchors>
						<Anchor point="TOPLEFT" relativeTo="$parent" relativePoint="TOPLEFT">
							<Offset>
								<AbsDimension x="15" y="-41"/>
							</Offset>
						</Anchor>
					</Anchors>
				</FontString>
				
			</Layer>
		</Layers>
		
		<Frames>
			
			
			<ScrollFrame name="LazyScriptImmunityExceptionCriteriaScrollFrame" inherits="UIPanelScrollFrameTemplate">
				<Size>
					<AbsDimension x="450" y="316"/>
				</Size>
				<Anchors>
					<Anchor point="TOPLEFT">
						<Offset>
							<AbsDimension x="15" y="-70"/>
						</Offset>
					</Anchor>
				</Anchors>
				
				<Backdrop name="$parentBackdrop" bgFile="Interface\TutorialFrame\TutorialFrameBackground"
				edgeFile="Interface\DialogFrame\UI-DialogBox-Border" tile="true" alpha="1.0">
					<EdgeSize>
						<AbsValue val="8"/>
					</EdgeSize>
					<TileSize>
						<AbsValue val="8"/>
					</TileSize>
					<BackgroundInsets>
						<AbsInset left="0" right="0" top="0" bottom="0"/>
					</BackgroundInsets>
				</Backdrop>
				
				
				<ScrollChild>
					<Frame name="$parentScrollChild">
						<Size>
							<AbsDimension x="435" y="305"/>
						</Size>
						<Anchors>
							<Anchor point="TOPLEFT" relativeTo="$parent" relativePoint="TOPLEFT">
								<Offset>
									<AbsDimension x="5" y="-5"/>
								</Offset>
							</Anchor>
						</Anchors>
						<Frames>
							
							
							<EditBox name="LazyScriptImmunityExceptionCriteriaEditFrameForm" letters="7000" enableMouse="true" autoFocus="true" multiLine="true">
								<Size>
									<AbsDimension x="435" y="305"/>
								</Size>
								<Anchors>
									<Anchor point="TOPLEFT" relativeTo="$parent" relativePoint="TOPLEFT">
										<Offset>
											<AbsDimension x="5" y="-5"/>
										</Offset>
									</Anchor>
								</Anchors>
								
								<Scripts>
									<OnEscapePressed>
										HideUIPanel(LazyScriptImmunityExceptionCriteriaEditFrame)
										HideUIPanel(LazyScriptFormHelp)
									</OnEscapePressed>
									<OnEnterPressed>
										HideUIPanel(LazyScriptImmunityExceptionCriteriaEditFrame)
										HideUIPanel(LazyScriptFormHelp)
									</OnEnterPressed>
									<OnClick>
										this:SetFocus()
									</OnClick>
									<OnShow>
										LAZYSCRIPTHELP_SCROLLBAR_HACK3 = false
									</OnShow>
									<OnMouseUp>
										LAZYSCRIPTHELP_SCROLLBAR_HACK3 = true
									</OnMouseUp>
									<OnTextChanged>
										ScrollingEdit_OnTextChanged(LazyScriptImmunityExceptionCriteriaScrollFrame)
									</OnTextChanged>
									<OnCursorChanged>
										ScrollingEdit_OnCursorChanged(arg1, arg2-10, arg3, arg4)
									</OnCursorChanged>
									<OnUpdate>
										if (LAZYSCRIPTHELP_SCROLLBAR_HACK3) then
										ScrollingEdit_OnUpdate(LazyScriptImmunityExceptionCriteriaScrollFrame)
										end
									</OnUpdate>
									
								</Scripts>
								<FontString inherits="GameFontHighlight"/>
								<Color r=".2" g=".2" b=".2" /> 
							</EditBox>
							
						</Frames>
					</Frame>
				</ScrollChild>
				<Scripts>
					<OnClick>
						LazyScriptImmunityExceptionCriteriaEditFrameForm:SetFocus()
					</OnClick>
				</Scripts>
			</ScrollFrame>                
			
			
			<Button name="$parentCloseButton" inherits="UIPanelCloseButton">
				<Anchors>
					<Anchor point="TOPRIGHT">
						<Offset>
							<AbsDimension x="-5" y="-5"/>
						</Offset>
					</Anchor>
				</Anchors>
				<Scripts>
					<OnClick>
						HideUIPanel(LazyScriptImmunityExceptionCriteriaEditFrame)
						HideUIPanel(LazyScriptFormHelp)
					</OnClick>
				</Scripts>
			</Button>
			
			<Button name="$parentHelpButton" inherits="GameMenuButtonTemplate" text="Help">
				<Size>
					<AbsDimension x="100" y="25"/>
				</Size>
				<Anchors>
					<Anchor point="BOTTOMLEFT" relativeTo="$parent" relativePoint="BOTTOMLEFT">
						<Offset>
							<AbsDimension x="10" y="10"/>
						</Offset>
					</Anchor>
				</Anchors>
				<Scripts>
					<OnClick>
						if (LazyScriptFormHelp:IsVisible()) then
						LazyScriptFormHelp:Hide()
						else
						LazyScriptFormHelp:Show()
						end
					</OnClick>
				</Scripts>
			</Button>
			
			<Button name="$parentCancelButton" inherits="GameMenuButtonTemplate" text="Cancel">
				<Size>
					<AbsDimension x="100" y="25"/>
				</Size>
				<Anchors>
					<Anchor point="BOTTOMRIGHT">
						<Offset>
							<AbsDimension x="-10" y="10"/>
						</Offset>
					</Anchor>
				</Anchors>
				<Scripts>
					<OnClick>
						lazyScript.immunityEditBox.cancelEdit = true
						HideUIPanel(LazyScriptImmunityExceptionCriteriaEditFrame)
						HideUIPanel(LazyScriptFormHelp)
					</OnClick>
				</Scripts>
			</Button>
			
			<Button name="$parentOkayButton" inherits="GameMenuButtonTemplate" text="Okay">
				<Size>
					<AbsDimension x="100" y="25"/>
				</Size>
				<Anchors>
					<Anchor point="RIGHT" relativeTo="$parentCancelButton" relativePoint="LEFT">
						<Offset>
							<AbsDimension x="0" y="0"/>
						</Offset>
					</Anchor>
				</Anchors>
				<Scripts>
					<OnClick>
						HideUIPanel(LazyScriptImmunityExceptionCriteriaEditFrame)
						HideUIPanel(LazyScriptFormHelp)
					</OnClick>
				</Scripts>
			</Button>
			
		</Frames>
		
		<Scripts>
			<OnShow>
				lazyScript.immunityEditBox.OnShow()
			</OnShow>
			<OnHide>
				lazyScript.immunityEditBox.OnHide()
			</OnHide>
			<OnLoad>
				lazyScript.metadata:updateRevisionFromKeyword("$Revision: 171 $")
				this:RegisterForDrag("LeftButton")
			</OnLoad>
			<OnDragStart>
				this:StartMoving()
			</OnDragStart>
			<OnDragStop>
				this:StopMovingOrSizing()
			</OnDragStop>
			<OnMouseUp>
				this:StopMovingOrSizing()
			</OnMouseUp>
		</Scripts>
		
	</Frame>
	
</Ui>