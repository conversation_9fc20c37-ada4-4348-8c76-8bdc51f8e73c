pfUI_locale["frFR"] = {}

pfUI_locale["frFR"]["class"] = {
  ["Démoniste"] = "WARLOCK",
  ["Guerrier"] = "WARRIOR",
  ["Chasseur"] = "HUNTER",
  ["Mage"] = "MAGE",
  ["Prêtre"] = "PRIEST",
  ["Druide"] = "DRUID",
  ["Paladin"] = "PALADIN",
  ["Chaman"] = "SHAMAN",
  ["Voleur"] = "ROGUE",
}

pfUI_locale["frFR"]["healduration"] = {
  ["Rejuvenation"] = "Augmente la durée de votre sort Récupération de 3 sec.",
  ["Renew"] = "Augmente la durée de votre sort Rénovation de 3 sec.",
}

pfUI_locale["frFR"]["bagtypes"] = {
  ["Carquois"] = "QUIVER",
  ["Sac d'âme"] = "SOULBAG",
  ["Conteneur"] = "DEFAULT",
}

pfUI_locale["frFR"]["itemtypes"] = {
  ["INVTYPE_WAND"] = "Baguette",
  ["INVTYPE_THROWN"] = "Armes de jet",
  ["INVTYPE_GUN"] = "Arme à feu",
  ["INVTYPE_CROSSBOW"] = "Arbalète",
  ["INVTYPE_PROJECTILE"] = "Projectile",
}

pfUI_locale["frFR"]["hunterpaging"] = {
  ["MELEE"] = "Coupure d'ailes",
  ["RANGED"] = "Tir des arcanes",
}

pfUI_locale["frFR"]["customcast"] = {
  ["AIMEDSHOT"] = "Visée",
  ["MULTISHOT"] = "Flèches multiples",
}

pfUI_locale["frFR"]["critters"] = {
  'Aspic',
  'Hanneton',
  'Chauve-souris de clocher',
  'Crapaud-bile',
  'Rat noir',
  'Chien de prairie brun',
  'Lapin en cage',
  'Mouton en cage',
  'Ecureuil en cage',
  'Crapaud en cage',
  'Chat',
  'Poulet',
  'Cléo',
  'Rat du Magma',
  'Vache',
  'Métamorphosé en vache',
  'Daim soigné',
  'Gazelle soignée',
  'Rat des profondeurs',
  'Daim',
  'Chien',
  'Effsee',
  'Rat des profondeurs ensorcelé',
  'Croc',
  'Faon',
  'Hanneton de feu',
  'Tout-doux',
  'Grenouille',
  'Gazelle',
  'Lièvre',
  'Cheval',
  'Crapaud énorme',
  'Daim infecté',
  'Ecureuil infecté',
  'Crapaud de la jungle',
  'Thermomètre de Krakle',
  'Dame',
  'Crabe de lave',
  'Mocassin d\'eau',
  'Souris',
  'M. Bigglesworth',
  'Grignote',
  'Noarm',
  'Vieille Blanchette',
  'Perroquet',
  'Métamorphosé en cochon',
  'Monstre déclencheur du trésor des pirates',
  'Insecte pestiféré',
  'Asticot pestiféré',
  'Rat pestiféré',
  'Termite des Maleterres',
  'Chien de prairie',
  'Lapin',
  'Bélier',
  'Rat',
  'Bélier',
  'Blatte',
  'Salome',
  'Banc de poisson',
  'Scorpion',
  'Métamorphosé en mouton',
  'Mouton',
  'Feu follet de Shen\'dralar',
  'Daim malade',
  'Gazelle malade',
  'Serpent',
  'Araignée',
  'Pointe',
  'Ecureuil',
  'Pourceau',
  'Cafard corrompu',
  'Rat corrompu',
  'Crapaud',
  'Métamorphosé en tortue',
  'Casse-pieds',
  'Voix d\'Elune',
  'Point de SPAWN (Seuls les MJ peuvent le voir)',
  'Feu follet',
}

pfUI_locale["frFR"]["dyndebuffs"] = {
  ["Rupture"] = "Rupture",
  ["Kidney Shot"] = "Aiguillon perfide",
  ["Rend"] = "Pourfendre",
  ["Shadow Word: Pain"] = "Mot de l'ombre : Douleur",
  ["Demoralizing Shout"] = "Cri démoralisant",
  ["Frostbolt"] = "Eclair de givre",
  ["Gouge"] = "Suriner",
}

pfUI_locale["frFR"]["judgements"] = {
  ["Jugement de justice"] = true,
  ["Jugement de lumière"] = true,
  ["Jugement de sagesse"] = true,
  ["Jugement du Croisé"] = true,
}

pfUI_locale["frFR"]["interrupts"] = {
  ["Coup de bouclier"] = true,
  ["Volée de coups"] = true,
  ["Coup de pied"] = true,
  ["Horion de terre"] = true,
  ["Choc martial"] = true,
  ["Coup traumatisant"] = true,
  ["Charge étourdissante"] = true,
  ["Interception étourdissante"] = true,
  ["Marteau de la justice"] = true,
  ["Coup bas"] = true,
  ["Suriner"] = true,
  ["Aiguillon perfide"] = true,
  ["Silence"] = true,
  ["Contresort"] = true,
  ["Contresort - Silencieux"] = true,
  ["Sonner"] = true,
  ["Peur"] = true,
  ["Hurlement de terreur"] = true,
  ["Cri psychique"] = true,
  ["Cri d’intimidation"] = true,
  ["Feu stellaire étourdissant"] = true,
  ["Etourdissement vengeur"] = true,
  ["Trait de choc amélioré"] = true,
  ["Impact"] = true,
  ["Pyroclasme"] = true,
  ["Aveuglement"] = true,
  ["Etourdir"] = true,
  ["Effet étourdissant de la masse"] = true,
  ["Trembleterre"] = true,
  ["Repentir"] = true,
  ["Flèche de dispersion"] = true,
  ["Cécité"] = true,
  ["Hibernation"] = true,
  ["Piqûre de wyverne"] = true,
  ["Bombe grossière en cuivre"] = true,
  ["Grande bombe en cuivre"] = true,
  ["Petite bombe en bronze"] = true,
  ["Grande bombe en bronze"] = true,
  ["Grande bombe en fer"] = true,
  ["Bombe explosive en mithril"] = true,
  ["Bombe explosive"] = true,
  ["Bombe en sombrefer"] = true,
  ["Grenade en fer"] = true,
  ["Grenade à fragmentation M73"] = true,
  ["Grenade en thorium"] = true,
  ["Mortier des gobelins"] = true,
}

pfUI_locale["frFR"]["resurrections"] = {
  ["Résurrection"] = true,
  ["Renaissance"] = true,
  ["Rédemption"] = true,
  ["Esprit ancestral"] = true,
}

pfUI_locale["frFR"]["spells"] = {
  ['Accomplissement véritable']={t=500,icon='Spell_Shadow_Charm'},
  ['Acide corrosif']={t=1500,icon='Spell_Nature_Acid_01'},
  ['Acide d\'Hakkar']={t=1000,icon='Spell_Nature_Acid_01'},
  ['Activation des défenses']={t=5000,icon='Temp'},
  ['Affaiblir']={t=2000,icon='Spell_Shadow_CurseOfMannoroth'},
  ['Affûtage de lame']={t=3000,icon='Temp'},
  ['Affûtage de lame II']={t=3000,icon='Temp'},
  ['Affûtage de lame III']={t=3000,icon='Temp'},
  ['Affûtage de lame IV']={t=3000,icon='Temp'},
  ['Affûtage de lame V']={t=3000,icon='Temp'},
  ['Agilité diminuée']={t=2000,icon='Spell_Holy_HarmUndeadAura'},
  ['Agrandir']={t=2000,icon='Spell_Nature_Strength'},
  ['Agro du Boss Uldaman']={t=5000,icon='Spell_Nature_EarthBindTotem'},
  ['Alimenter le feu de joie']={t=500,icon='Temp'},
  ['Amollir']={t=1500,icon='Ability_Creature_Poison_03'},
  ['Amplification des dégâts']={t=2000,icon='Spell_Nature_AbolishMagic'},
  ['Amplifier flammes']={t=1000,icon='Spell_Fire_Fireball'},
  ['Apaiser les animaux']={t=1500,icon='Ability_Hunter_BeastSoothe'},
  ['Appel d\'un worg pâle']={t=1300,icon='Spell_Shadow_ChillTouch'},
  ['Appel d\'un worg écumant']={t=1300,icon='Spell_Shadow_ChillTouch'},
  ['Appel d\'une Horreur Lupine']={t=1300,icon='Spell_Shadow_ChillTouch'},
  ['Appel de Thund']={t=1500,icon='Spell_Frost_Wisp'},
  ['Appel de barrière prismatique']={t=10000,icon='Temp'},
  ['Appel de la tombe']={t=2000,icon='Spell_Shadow_ChillTouch'},
  ['Appel de visions']={t=30000,icon='Spell_Shadow_LifeDrain'},
  ['Appel des Anciens']={t=7000,icon='Temp'},
  ['Appel des glyphes de sauvegarde']={t=3000,icon='Temp'},
  ['Appel du Vide']={t=3000,icon='Spell_Shadow_DeathCoil'},
  ['Appel du néant']={t=10000,icon='Temp'},
  ['Appeler Ivus']={t=10000,icon='Temp'},
  ['Appliquer la Glande de séduction']={t=2500,icon='INV_Misc_Bowl_01'},
  ['Appliquer le Baume']={t=1300,icon='Temp'},
  ['Appliquer le leurre']={t=4000,icon='Temp'},
  ['Apprendre Barque du destin']={t=1000,icon='Temp'},
  ['Appât de Miblon']={t=2000,icon='INV_Misc_Food_50'},
  ['Arme aiguisée - critique']={t=3000,icon='Temp'},
  ['Arme consacrée']={t=3000,icon='Temp'},
  ['Armure des éléments']={t=1000,icon='Spell_Frost_Frost'},
  ['Arrêt du temps']={t=3000,icon='Temp'},
  ['Aspect de Neptulon']={t=1000,icon='Temp'},
  ['Asservir démon']={t=3000,icon='Spell_Shadow_EnslaveDemon'},
  ['Asticots']={t=5000,icon='Trade_Fishing'},
  ['Attaque Nefarius 001']={t=1000,icon='Temp'},
  ['Attaque bombe étourdissante']={t=2000,icon='Spell_Fire_SelfDestruct'},
  ['Attaque du Tireur de précision']={t=2000,icon='Ability_Marksmanship'},
  ['Attaque mentale']={t=1500,icon='Spell_Shadow_UnholyFrenzy'},
  ['Attracteur de poissons aquadynamique']={t=5000,icon='INV_Misc_Orb_03'},
  ['Atténuateur en phase']={t=2000,icon='Spell_Holy_SearingLight'},
  ['Augmenter la réputation']={t=1000,icon='Temp'},
  ['Auto-explosion']={t=7000,icon='Spell_Fire_SelfDestruct'},
  ['Autodestruction']={t=7000,icon='Spell_Fire_SelfDestruct'},
  ['Balayage de ver']={t=1000,icon='INV_Misc_MonsterScales_05'},
  ['Balayer']={t=1500,icon='Spell_Nature_Thorns'},
  ['Balise de la faille']={t=2000,icon='Spell_Nature_AbolishMagic'},
  ['Balle explosive']={t=1000,icon='Spell_Fire_Fireball02'},
  ['Bannir']={t=1500,icon='Spell_Shadow_Cripple'},
  ['Bannir un Exilé ardent']={t=1000,icon='Spell_Shadow_LifeDrain'},
  ['Bannir un Exilé foudroyant']={t=1000,icon='Spell_Shadow_LifeDrain'},
  ['Bannir un Exilé grimpeur']={t=1000,icon='Spell_Shadow_LifeDrain'},
  ['Barrage tourbillonnant']={t=1500,icon='INV_Spear_05'},
  ['Barrière de lumière']={t=2000,icon='Temp'},
  ['Bave toxique']={t=500,icon='Spell_Nature_CorrosiveBreath'},
  ['Berceuse enchantée']={t=1000,icon='Spell_Shadow_SoothingKiss'},
  ['Bière-massue de Kreeg']={t=1000,icon='INV_Drink_05'},
  ['Blizzard']={t=2000,icon='Spell_Frost_IceStorm'},
  ['Bloqué']={t=10000,icon='Spell_Shadow_Teleport'},
  ['Boire la potion']={t=3000,icon='Spell_Holy_Heal'},
  ['Boire une potion mineure']={t=3000,icon='Spell_Holy_Heal'},
  ['Boisson psychoénergétique de Mebok']={t=3000,icon='Temp'},
  ['Bombardement']={t=3000,icon='Ability_GolemStormBolt'},
  ['Bombardement II']={t=3000,icon='Ability_GolemStormBolt'},
  ['Bombardement de gelée']={t=1000,icon='Temp'},
  ['Bombe']={t=2000,icon='Spell_Fire_SelfDestruct'},
  ['Bombe de gaz']={t=1000,icon='INV_Misc_Ammo_Bullet_01'},
  ['Bombe des arcanes']={t=1500,icon='Spell_Holy_Silence'},
  ['Bombe du Syndicat']={t=3000,icon='Spell_Shadow_MindBomb'},
  ['Bombe en sombrefer']={t=1000,icon='Spell_Fire_SelfDestruct'},
  ['Bombe explosive']={t=1000,icon='Spell_Fire_SelfDestruct'},
  ['Bombe explosive en mithril']={t=1000,icon='Spell_Fire_SelfDestruct'},
  ['Bombe grossière en cuivre']={t=1000,icon='Spell_Fire_SelfDestruct'},
  ['Bombe étourdissante']={t=2000,icon='Spell_Fire_SelfDestruct'},
  ['Bonhomme de neige']={t=1500,icon='INV_Ammo_Snowball'},
  ['Boomerang de Linken']={t=500,icon='INV_Weapon_ShortBlade_10'},
  ['Bouclier anti-magie']={t=2000,icon='Spell_Shadow_AntiMagicShell'},
  ['Bouclier de feu']={t=1000,icon='Spell_Fire_Immolation'},
  ['Bouclier de feu II']={t=1000,icon='Spell_Fire_Immolation'},
  ['Bouclier de feu III']={t=1000,icon='Spell_Fire_Immolation'},
  ['Bouclier de feu IV']={t=1000,icon='Spell_Fire_Immolation'},
  ['Bouclier réflecteur']={t=1000,icon='Spell_Shadow_Teleport'},
  ['Boue projetée']={t=1000,icon='Spell_Nature_Sleep'},
  ['Bouffée de maladie']={t=1500,icon='Spell_Nature_EarthBind'},
  ['Boule de feu']={t=3500,icon='Spell_Fire_FlameBolt'},
  ['Bourbier']={t=1000,icon='Spell_Nature_StrangleVines'},
  ['Bourrasque']={t=2000,icon='Spell_Nature_EarthBind'},
  ['Brise-âme']={t=2000,icon='Spell_Shadow_Haunting'},
  ['Brûlure']={t=1500,icon='Spell_Fire_SoulBurn'},
  ['Brûlure d\'âmes']={t=4000,icon='Ability_Racial_Cannibalize'},
  ['Brûlure de givre']={t=2000,icon='Spell_Frost_ChillingBlast'},
  ['Brûlure de mana']={t=3000,icon='Spell_Shadow_ManaBurn'},
  ['Brûlure de zone']={t=3000,icon='Spell_Fire_SelfDestruct'},
  ['Bulle d’eau']={t=1000,icon='Spell_Frost_Wisp'},
  ['Bâton clé de Uldaman']={t=5000,icon='Temp'},
  ['Bâton de commandement de Sniffetarin']={t=1500,icon='Spell_Shadow_LifeDrain'},
  ['Bélier blanc']={t=3000,icon='Ability_Mount_MountainRam'},
  ['Bélier blanc rapide']={t=3000,icon='Ability_Mount_MountainRam'},
  ['Bélier bleu']={t=3000,icon='Ability_Mount_MountainRam'},
  ['Bélier brun']={t=3000,icon='Ability_Mount_MountainRam'},
  ['Bélier brun rapide']={t=3000,icon='Ability_Mount_MountainRam'},
  ['Bélier de givre']={t=3000,icon='Ability_Mount_MountainRam'},
  ['Bélier de guerre noir']={t=3000,icon='Ability_Mount_MountainRam'},
  ['Bélier gris']={t=3000,icon='Ability_Mount_MountainRam'},
  ['Bélier gris rapide']={t=3000,icon='Ability_Mount_MountainRam'},
  ['Bélier noir']={t=3000,icon='Ability_Mount_MountainRam'},
  ['Bénédiction de Julie']={t=2000,icon='Spell_Holy_Renew'},
  ['Bénédiction de Shahram']={t=1000,icon='Spell_Holy_LayOnHands'},
  ['Canon à flammes']={t=1500,icon='Spell_Fire_FlameBolt'},
  ['Capture des termites']={t=5000,icon='Temp'},
  ['Capturer Grark']={t=3000,icon='Temp'},
  ['Capturer un jeune worg']={t=2500,icon='Temp'},
  ['Carapace de l\'ombre']={t=1000,icon='Spell_Shadow_AntiShadow'},
  ['Carapace infernale']={t=3000,icon='Spell_Fire_SelfDestruct'},
  ['Cascade de roses']={t=500,icon='INV_Misc_Dust_04'},
  ['Casser le Grand Truc']={t=2000,icon='Spell_Shadow_CurseOfAchimonde'},
  ['Cauchemar de Naralex']={t=2000,icon='Spell_Nature_Sleep'},
  ['Cercle de guérison']={t=3000,icon='Spell_Holy_BlessingOfProtection'},
  ['Champignon de Mirkfallon']={t=3000,icon='Spell_Holy_HarmUndeadAura'},
  ['Chandelle d\'Elune']={t=500,icon='Temp'},
  ['Changement de polarité']={t=3000,icon='Spell_Nature_Lightning'},
  ['Chants de manifestation']={t=2000,icon='Spell_Magic_LesserInvisibilty'},
  ['Charge']={t=5000,icon='Spell_Shadow_EvilEye'},
  ['Chaîne d\'éclairs']={t=2500,icon='Spell_Nature_ChainLightning'},
  ['Chaînes de Brûlure']={t=3000,icon='Spell_Shadow_ManaBurn'},
  ['Chaînes de glace']={t=1300,icon='Spell_Frost_ChainsOfIce'},
  ['Cheval (pinto)']={t=3000,icon='Ability_Mount_RidingHorse'},
  ['Cheval bai']={t=3000,icon='Ability_Mount_RidingHorse'},
  ['Cheval de guerre squelette rouge']={t=3000,icon='Ability_Mount_Undeadhorse'},
  ['Cheval de guerre squelette vert']={t=3000,icon='Ability_Mount_Undeadhorse'},
  ['Cheval de guerre squelette violet']={t=3000,icon='Ability_Mount_Undeadhorse'},
  ['Cheval squelette']={t=3000,icon='Ability_Mount_Undeadhorse'},
  ['Cheval squelette bai']={t=3000,icon='Ability_Mount_Undeadhorse'},
  ['Cheval squelette bleu']={t=3000,icon='Ability_Mount_Undeadhorse'},
  ['Cheval squelette rouge']={t=3000,icon='Ability_Mount_Undeadhorse'},
  ['Choc colossal']={t=5000,icon='Temp'},
  ['Choc de flammes']={t=3000,icon='Spell_Fire_SelfDestruct'},
  ['Choc martial']={t=500,icon='Ability_WarStomp'},
  ['Chope de fête vide']={t=2000,icon='Temp'},
  ['Châtiment']={t=2500,icon='Spell_Holy_HolySmite'},
  ['Châtiment sacré']={t=2500,icon='Spell_Holy_HolySmite'},
  ['Cloche de Shay']={t=4500,icon='Temp'},
  ['Clone']={t=2500,icon='Spell_Shadow_BlackPlague'},
  ['Clé de la cage de la panthère']={t=5000,icon='Temp'},
  ['Collecte de déchets']={t=4000,icon='Temp'},
  ['Collecte de vie']={t=1000,icon='Spell_Shadow_Requiem'},
  ['Collecter l\'Oeuf de la colonie']={t=500,icon='Temp'},
  ['Collecter les oeufs de silithide']={t=5000,icon='Temp'},
  ['Colère']={t=2000,icon='Spell_Nature_AbolishMagic'},
  ['Colère divine']={t=2000,icon='Spell_Holy_Excorcism'},
  ['Combinaison de torche']={t=5000,icon='Temp'},
  ['Complainte de la Banshee']={t=2000,icon='Spell_Shadow_Possession'},
  ['Compétence des Réprouvés']={t=2500,icon='Spell_Shadow_AntiShadow'},
  ['Connexion de caméra de Goblin']={t=3000,icon='Temp'},
  ['Connexion à la caméra de gnome']={t=3000,icon='Temp'},
  ['Contresort du Dieu maléfique']={t=300000,icon='Temp'},
  ['Contrôle mental']={t=3000,icon='Spell_Shadow_ShadowWordDominate'},
  ['Corruption']={t=2000,icon='Spell_Shadow_AbominationExplosion'},
  ['Coup factice']={t=2000,icon='Ability_Marksmanship'},
  ['Crachat acide']={t=3000,icon='Spell_Nature_Acid_01'},
  ['Crachat d\'Acide corrosif']={t=3000,icon='Spell_Nature_Acid_01'},
  ['Crachat d\'abomination']={t=2500,icon='Spell_Nature_CorrosiveBreath'},
  ['Crachat de flammes']={t=3000,icon='Spell_Fire_FlameBolt'},
  ['Crachat de venin corrosif']={t=2500,icon='Spell_Nature_CorrosiveBreath'},
  ['Crachat empoisonné']={t=2000,icon='Spell_Nature_CorrosiveBreath'},
  ['Crachat infectieux']={t=3000,icon='Spell_Shadow_CreepingPlague'},
  ['Crachat toxique']={t=2500,icon='Spell_Nature_CorrosiveBreath'},
  ['Crachat venimeux']={t=2500,icon='Spell_Nature_CorrosiveBreath'},
  ['Craquement de flammes']={t=2500,icon='Spell_Fire_Fire'},
  ['Creuse pour trouver du Cobalt']={t=1500,icon='Temp'},
  ['Crinière acérée']={t=1000,icon='Spell_Nature_Thorns'},
  ['Cristal de résonite enchanté']={t=5000,icon='Temp'},
  ['Crochetage']={t=5000,icon='Spell_Nature_MoonKey'},
  ['Création d\'un lanceur de chapelet de comètes']={t=2000,icon='INV_Misc_EngGizmos_03'},
  ['Création d\'un lanceur de fusées de feu d\'artifice']={t=2000,icon='INV_Musket_04'},
  ['Création d\'une coupe de divination']={t=2500,icon='INV_Misc_Bowl_01'},
  ['Création de Pierre de soins']={t=3000,icon='INV_Stone_04'},
  ['Création de Pierre de soins (inférieure)']={t=3000,icon='INV_Stone_04'},
  ['Création de Pierre de soins (majeure)']={t=3000,icon='INV_Stone_04'},
  ['Création de Pierre de soins (mineure)']={t=3000,icon='INV_Stone_04'},
  ['Création de Pierre de soins (supérieure)']={t=3000,icon='INV_Stone_04'},
  ['Création de l\'Eau des Prophètes']={t=5000,icon='Spell_Shadow_LifeDrain'},
  ['Création d’une faille']={t=3000,icon='Temp'},
  ['CréatureSpécial']={t=2000,icon='Temp'},
  ['Crée une Liasse de reliques']={t=1000,icon='Temp'},
  ['Créer Totems de Witherbark']={t=2000,icon='Spell_Lightning_LightningBolt01'},
  ['Créer l\'Enigmatron-PX83']={t=2000,icon='INV_Misc_Bowl_01'},
  ['Créer un Coffre']={t=500,icon='Temp'},
  ['Créer un Coffre rempli']={t=2000,icon='Temp'},
  ['Créer un Déchiqueteur']={t=1000,icon='INV_Misc_Gear_01'},
  ['Créer un Parchemin']={t=5000,icon='INV_Scroll_05'},
  ['Créer un Totem de purification']={t=5000,icon='Spell_Shadow_LifeDrain'},
  ['Créer un sapta']={t=3000,icon='INV_Misc_Food_09'},
  ['Créer une Robe de mage']={t=4000,icon='Temp'},
  ['Créer une Sphère de mage']={t=4000,icon='Temp'},
  ['Cueillette']={t=5000,icon='Spell_Nature_NatureTouchGrow'},
  ['Cuisine de Macaron']={t=2000,icon='Spell_Holy_Heal'},
  ['Cure de Tharnariun 1']={t=750,icon='Spell_Nature_RemoveDisease'},
  ['Cœur d\'Hakkar - Molthor brise le cœur']={t=2000,icon='Temp'},
  ['Dard empoisonné']={t=2000,icon='INV_Misc_MonsterTail_03'},
  ['Dards de flammes']={t=3000,icon='Spell_Fire_SelfDestruct'},
  ['Delete Me']={t=4000,icon='INV_Scroll_02'},
  ['Dessiner les glyphes anciens']={t=10000,icon='Temp'},
  ['Destrier de bataille Stormpike']={t=3000,icon='Ability_Mount_MountainRam'},
  ['Destrier de la mort']={t=3000,icon='Ability_Mount_Undeadhorse'},
  ['Dissipation']={t=1000,icon='Spell_Holy_DispelMagic'},
  ['Dissipation de masse']={t=1000,icon='Spell_Shadow_Teleport'},
  ['Dissipation du poison']={t=2000,icon='Spell_Holy_Purify'},
  ['Dissipation supérieure']={t=4000,icon='Spell_Arcane_StarFire'},
  ['Domination']={t=1000,icon='Spell_Shadow_ShadowWordDominate'},
  ['Don du Xavian']={t=5000,icon='Spell_Holy_FlashHeal'},
  ['Don d’Arugal']={t=2500,icon='Spell_Shadow_ChillTouch'},
  ['Double vue']={t=2000,icon='Spell_Nature_FarSight'},
  ['Double vue (PT)']={t=2000,icon='Temp'},
  ['Douce surprise']={t=1000,icon='INV_ValentinesChocolate03'},
  ['Douleur brûlante']={t=1500,icon='Spell_Fire_SoulBurn'},
  ['Douleur intense']={t=1000,icon='Spell_Shadow_ShadowWordPain'},
  ['Douleur paralysante']={t=1500,icon='Spell_Nature_CorrosiveBreath'},
  ['Draco-Incarcinatrix 900']={t=2000,icon='Temp'},
  ['Drain d’esclave']={t=1000,icon='Spell_Shadow_ChillTouch'},
  ['Drain d’âme']={t=2000,icon='Spell_Shadow_LifeDrain02'},
  ['Drapeau de Kadrak']={t=2000,icon='INV_Banner_03'},
  ['Dresser un pilier']={t=5000,icon='Temp'},
  ['Dynamite']={t=1000,icon='Spell_Fire_SelfDestruct'},
  ['Dynamite Ev-Lan']={t=1000,icon='Spell_Fire_SelfDestruct'},
  ['Dynamite basique']={t=1000,icon='Spell_Fire_SelfDestruct'},
  ['Dynamite dense']={t=1000,icon='Spell_Fire_SelfDestruct'},
  ['Dynamite des Mortemines']={t=2000,icon='Spell_Fire_SelfDestruct'},
  ['Dynamite grossière']={t=1000,icon='Spell_Fire_SelfDestruct'},
  ['Dynamite puissante']={t=1000,icon='Spell_Fire_SelfDestruct'},
  ['Dynamite solide']={t=1000,icon='Spell_Fire_SelfDestruct'},
  ['Début Prophète ardent']={t=5000,icon='Spell_Nature_EarthBindTotem'},
  ['Déclencher le Défi d\'Urok']={t=2000,icon='Temp'},
  ['Déclencheur rotation']={t=3000,icon='Temp'},
  ['Décoction instable']={t=3000,icon='Spell_Fire_Incinerate'},
  ['Décrépitude spirituelle']={t=2000,icon='Spell_Holy_HarmUndeadAura'},
  ['Défense de Proudmoore']={t=3000,icon='Spell_Holy_BlessingOfProtection'},
  ['Défibriller']={t=4000,icon='Spell_Nature_Purge'},
  ['Déguisement de Défias']={t=3000,icon='Ability_Rogue_Disguise'},
  ['Déguisement de Paysan']={t=3000,icon='Ability_Rogue_Disguise'},
  ['Déguisement de Péon']={t=3000,icon='Ability_Rogue_Disguise'},
  ['Déguisement de membre du Syndicat']={t=3000,icon='Ability_Rogue_Disguise'},
  ['Déguisement de nain Sombrefer']={t=3000,icon='Ability_Rogue_Disguise'},
  ['Déguisement de pirate des mers du Sud']={t=3000,icon='Ability_Rogue_Disguise'},
  ['Déguisement de sorcier de Dalaran']={t=3000,icon='Ability_Rogue_Disguise'},
  ['Déguisment de trogg Brisepierre']={t=3000,icon='Ability_Rogue_Disguise'},
  ['Dégénérescence mentale']={t=2000,icon='Spell_Shadow_MindRot'},
  ['Délice au lait']={t=1000,icon='INV_ValentinesChocolate01'},
  ['Dépeçage']={t=2000,icon='INV_Misc_Pelt_Wolf_01'},
  ['Déplace une faille  temporelle']={t=5000,icon='Temp'},
  ['Déranger l\'Oeuf de la colonie']={t=1000,icon='Temp'},
  ['Désarmement de piège']={t=2000,icon='Spell_Shadow_GrimWard'},
  ['Désenchanter']={t=3000,icon='Spell_Holy_RemoveCurse'},
  ['Désir noir']={t=1000,icon='INV_ValentinesChocolate04'},
  ['Détonation']={t=5000,icon='Spell_Fire_SelfDestruct'},
  ['Détruire l\'Aimant fantôme']={t=10000,icon='Temp'},
  ['Détruire l\'Oeuf']={t=3000,icon='INV_Misc_MonsterClaw_02'},
  ['Détruire la tente']={t=2500,icon='Temp'},
  ['Détérioration']={t=2000,icon='Spell_Shadow_CurseOfAchimonde'},
  ['Déverrouillage']={t=5000,icon='Temp'},
  ['Déverrouillage de Khadgar']={t=10000,icon='INV_Misc_Key_14'},
  ['Déverrouillage d’Ashcrombe']={t=4000,icon='Spell_Nature_MoonKey'},
  ['Déverrouiller le Pied de Maury']={t=5000,icon='Temp'},
  ['Déviation de sort (NYI)']={t=1000,icon='Temp'},
  ['Eau purifiée pétillante']={t=6000,icon='Temp'},
  ['Echec d’Equilibre de la nature']={t=10000,icon='Temp'},
  ['Eclair']={t=3000,icon='Spell_Nature_Lightning'},
  ['Eclair de Vide']={t=3000,icon='Spell_Shadow_ShadowBolt'},
  ['Eclair de cristal']={t=2000,icon='Spell_Shadow_Teleport'},
  ['Eclair de feu']={t=2000,icon='Spell_Fire_FireBolt'},
  ['Eclair de feu II']={t=3000,icon='Spell_Fire_FireBolt02'},
  ['Eclair de feu III']={t=3000,icon='Spell_Fire_FireBolt02'},
  ['Eclair de feu IV']={t=3000,icon='Spell_Fire_FireBolt02'},
  ['Eclair de givre']={t=3000,icon='Spell_Frost_FrostBolt02'},
  ['Eclair de glace']={t=2000,icon='Spell_Frost_FrostBolt02'},
  ['Eclair de lave']={t=2000,icon='Spell_Fire_Fire'},
  ['Eclair de poison']={t=2500,icon='Spell_Nature_CorrosiveBreath'},
  ['Eclair de radiation']={t=3000,icon='Spell_Shadow_CorpseExplode'},
  ['Eclair de tempête']={t=1000,icon='INV_Hammer_01'},
  ['Eclair des arcanes']={t=1000,icon='Spell_Arcane_StarFire'},
  ['Eclair des arcanes chargé']={t=7000,icon='Spell_Arcane_StarFire'},
  ['Eclair lumineux']={t=1500,icon='Spell_Holy_FlashHeal'},
  ['Eclair toxique']={t=2500,icon='Spell_Nature_CorrosiveBreath'},
  ['Eclairs bondissants']={t=3000,icon='Spell_Nature_Lightning'},
  ['Eclairs en série']={t=1800,icon='Spell_Nature_ChainLightning'},
  ['Eclat flamboyant']={t=3000,icon='Spell_Fire_SelfDestruct'},
  ['Eclats d\'os']={t=500,icon='Spell_Shadow_ScourgeBuild'},
  ['Ecu de vindicte']={t=1000,icon='INV_Shield_19'},
  ['Ecureuil mécanique']={t=1000,icon='Spell_Shadow_Metamorphosis'},
  ['Editor Test Spell']={t=250,icon='Temp'},
  ['Effet Rituel d\'invocation']={t=5000,icon='Temp'},
  ['Effet Rituel de malédiction']={t=10000,icon='Spell_Arcane_PortalDarnassus'},
  ['Effet d\'invocation']={t=5000,icon='Spell_Shadow_AnimateDead'},
  ['Effet de Segra Darkthorn']={t=3000,icon='Temp'},
  ['Effet de la poudre de renne']={t=8000,icon='Temp'},
  ['Effet visuel d\'invocation du Trésor de la Horde']={t=1500,icon='Temp'},
  ['Effet visuel de l\'autel atal\'ai (DND)']={t=1000,icon='Temp'},
  ['Effrayer une bête']={t=1500,icon='Ability_Druid_Cower'},
  ['Elémentaire de feu']={t=500,icon='Spell_Fire_Fireball02'},
  ['Elémentaire de terre']={t=3000,icon='Ability_Temp'},
  ['Elémentaires de Sarilus']={t=3000,icon='Spell_Shadow_RaiseDead'},
  ['Empire des âmes']={t=3000,icon='Spell_Shadow_ShadowWordDominate'},
  ['Emprise']={t=2000,icon='Spell_Shadow_ShadowWordDominate'},
  ['Emprise de Crispechardon']={t=2000,icon='Spell_Shadow_Haunting'},
  ['Enchantement de Mor\'rogal']={t=1300,icon='Temp'},
  ['Enchanter les Annales de Darrowshire']={t=3500,icon='Temp'},
  ['Enchaînement']={t=2500,icon='Ability_Warrior_Cleave'},
  ['Endommager le véhicule']={t=2000,icon='Spell_Fire_Fire'},
  ['Enflammer la chair']={t=2000,icon='Spell_Fire_Fire'},
  ['Enlever l\'insigne']={t=1000,icon='Temp'},
  ['Enterrer le Cadavre de Samuel']={t=2000,icon='Temp'},
  ['Entraves des morts-vivants']={t=1500,icon='Spell_Nature_Slow'},
  ['Epreuve de la Connaissance']={t=2000,icon='Spell_Nature_EarthBind'},
  ['Equilibre de la nature']={t=3000,icon='Temp'},
  ['Equipage ivre']={t=2000,icon='Temp'},
  ['Esprit ancestral']={t=10000,icon='Spell_Nature_Regenerate'},
  ['Esprit des arcanes II']={t=1000,icon='Spell_Holy_MagicalSentry'},
  ['Esprit des arcanes III']={t=1000,icon='Spell_Holy_MagicalSentry'},
  ['Esprit des arcanes IV']={t=1000,icon='Spell_Holy_MagicalSentry'},
  ['Esprit des arcanes V']={t=1000,icon='Spell_Holy_MagicalSentry'},
  ['Esprit farouche']={t=3000,icon='Spell_Nature_SpiritWolf'},
  ['Esprit farouche II']={t=3000,icon='Spell_Nature_SpiritWolf'},
  ['Esprit pestiféré']={t=4000,icon='Spell_Shadow_CallofBone'},
  ['Esprits hanteurs']={t=2000,icon='Spell_Shadow_BlackPlague'},
  ['Essaim des moissons']={t=3000,icon='Spell_Holy_Dizzy'},
  ['Etalon blanc']={t=3000,icon='Ability_Mount_RidingHorse'},
  ['Etalon noir']={t=3000,icon='Ability_Mount_NightmareHorse'},
  ['Etalon palomino']={t=3000,icon='Ability_Mount_RidingHorse'},
  ['Eteindre la Flamme éternelle']={t=1000,icon='Temp'},
  ['Etincelle']={t=2000,icon='Spell_Nature_Lightning'},
  ['Etreinte de la veuve']={t=500,icon='Spell_Arcane_Blink'},
  ['Exorcisme d\'Atiesh']={t=20000,icon='Temp'},
  ['Exorcisme des esprits']={t=4000,icon='Temp'},
  ['Explosion']={t=2000,icon='Temp'},
  ['Explosion de flammes']={t=7000,icon='Spell_Fire_SelfDestruct'},
  ['Explosion de sable']={t=2000,icon='Spell_Nature_Cyclone'},
  ['Explosion des arcanes']={t=1500,icon='Spell_Nature_WispSplode'},
  ['Explosion féerique']={t=1500,icon='Spell_Fire_FireBolt'},
  ['Explosion pyrotechnique']={t=6000,icon='Spell_Fire_Fireball02'},
  ['Explosion télécommandée']={t=1000,icon='INV_Misc_StoneTablet_04'},
  ['Extase mélodieuse']={t=1000,icon='Temp'},
  ['Extinction']={t=2000,icon='Temp'},
  ['Faction Progéniture de Nozdormu +1000']={t=1000,icon='Temp'},
  ['Faible Eclair de givre']={t=2200,icon='Spell_Frost_FrostBolt02'},
  ['Faiblesse']={t=3000,icon='Spell_Shadow_Cripple'},
  ['Familier renforcé']={t=500,icon='Spell_Shadow_DeathPact'},
  ['Fantômes hanteurs']={t=2000,icon='Spell_Shadow_BlackPlague'},
  ['Fatigue fièvreuse']={t=3000,icon='Spell_Nature_NullifyDisease'},
  ['Fermeture']={t=1000,icon='Temp'},
  ['Feu d\'artifice']={t=500,icon='Temp'},
  ['Feu de camp basique']={t=10000,icon='Spell_Fire_Fire'},
  ['Feu de camp éclatant']={t=10000,icon='Spell_Fire_Fire'},
  ['Feu de l\'âme']={t=6000,icon='Spell_Fire_Fireball02'},
  ['Feu stellaire']={t=3500,icon='Spell_Arcane_StarFire'},
  ['Filet lesté']={t=500,icon='Ability_Ensnare'},
  ['Filet électrifié']={t=500,icon='Ability_Ensnare'},
  ['Fièvre collective']={t=1000,icon='Spell_Shadow_DarkSummoning'},
  ['Flamme d\'ombre']={t=2000,icon='Spell_Fire_Incinerate'},
  ['Flamme vertueuse activée']={t=4000,icon='Spell_Holy_InnerFire'},
  ['Flammes de Shahram']={t=1000,icon='Spell_Fire_SelfDestruct'},
  ['Flammes de châtiment']={t=3000,icon='Temp'},
  ['Flammes du chaos']={t=1000,icon='Spell_Fire_WindsofWoe'},
  ['Flammes incendiaires']={t=2000,icon='Spell_Fire_Immolation'},
  ['Flammes infernales']={t=3000,icon='Spell_Fire_SelfDestruct'},
  ['Flammes infernales II']={t=3000,icon='Spell_Fire_SelfDestruct'},
  ['Flammes infernales III']={t=3000,icon='Spell_Fire_SelfDestruct'},
  ['Flammes sacrées']={t=3500,icon='Spell_Holy_SearingLight'},
  ['Flèche d\'Aynasha']={t=500,icon='Temp'},
  ['Flèche d\'infection']={t=2000,icon='Spell_Shadow_CallofBone'},
  ['Flèche de Cupidon']={t=1000,icon='Temp'},
  ['Flèche explosive']={t=1000,icon='Spell_Fire_Fireball02'},
  ['Flèche noire']={t=2000,icon='Ability_TheBlackArrow'},
  ['Fléau du venin']={t=2000,icon='Temp'},
  ['Flétrissement']={t=1500,icon='Spell_Nature_NullifyDisease'},
  ['Focalisation']={t=5000,icon='Temp'},
  ['Fondant aux myrtilles']={t=1000,icon='INV_ValentinesChocolate02'},
  ['Fonte de l\'armure du Déchiqueteur']={t=2000,icon='Spell_Fire_Incinerate'},
  ['Fonte de minerai']={t=1500,icon='Spell_Fire_SelfDestruct'},
  ['Force d\'Arko\'narin']={t=4000,icon='Spell_Nature_AstralRecal'},
  ['Force de Frostmane']={t=1000,icon='Spell_Nature_UndyingStrength'},
  ['Force des âges']={t=2000,icon='Spell_Shadow_Requiem'},
  ['Force diminuée']={t=2000,icon='Spell_Holy_HarmUndeadAura'},
  ['Forger Poing de Verigan']={t=600,icon='Spell_Holy_RighteousFury'},
  ['Forme de Hibou']={t=5000,icon='Spell_Nature_RavenForm'},
  ['Forme de chat de Vipore DND']={t=1000,icon='Temp'},
  ['Forme de furbolg']={t=2000,icon='INV_Misc_MonsterClaw_04'},
  ['Forme de traquelune (pas invisible)']={t=1000,icon='Ability_Hibernation'},
  ['Foudre en boule']={t=1000,icon='Spell_Lightning_LightningBolt01'},
  ['Fouet de flammes']={t=1000,icon='Spell_Fire_Fireball'},
  ['Fouir']={t=1000,icon='Ability_Vanish'},
  ['Fourche d\'éclairs']={t=2000,icon='Spell_Nature_ChainLightning'},
  ['Fracas de vagues']={t=2000,icon='Spell_Frost_FrostNova'},
  ['Frappe']={t=2000,icon='Temp'},
  ['Frappe de la Gargouille']={t=1500,icon='Spell_Shadow_ShadowBolt'},
  ['Frappe des ailes']={t=1000,icon='INV_Misc_MonsterScales_14'},
  ['Fureur élémentaire']={t=1000,icon='Spell_Fire_FireArmor'},
  ['Furie sanguinaire rapide']={t=2000,icon='Spell_Nature_BloodLust'},
  ['Gambit de l’aube']={t=1500,icon='Temp'},
  ['Gardien de flammes rapide']={t=1500,icon='Spell_Fire_SealOfFire'},
  ['Gardien de givre rapide']={t=1500,icon='Spell_Fire_SealOfFire'},
  ['Gardien guérisseur']={t=2000,icon='Spell_Holy_LayOnHands'},
  ['Gardien guérisseur excellent']={t=2000,icon='Spell_Holy_LayOnHands'},
  ['Gardien marcheur du Vide']={t=3000,icon='Spell_Shadow_SummonVoidWalker'},
  ['Gardiens d’Ilkrud']={t=5000,icon='Spell_Shadow_SummonVoidWalker'},
  ['Gel']={t=4000,icon='Spell_Frost_Glacier'},
  ['Geler l\'Oeuf de la colonie']={t=500,icon='Temp'},
  ['Geler l\'Oeuf de la colonie (prototype)']={t=500,icon='Temp'},
  ['Geler sur place']={t=2500,icon='Spell_Frost_Glacier'},
  ['Gelée putride']={t=2000,icon='Spell_Shadow_CreepingPlague'},
  ['Gemme du néant']={t=3000,icon='Temp'},
  ['Gemme du serpent']={t=5000,icon='Temp'},
  ['Geyser massif']={t=1500,icon='Spell_Frost_SummonWaterElemental'},
  ['Glacial']={t=1500,icon='Spell_Frost_FrostBolt02'},
  ['Gong']={t=500,icon='Temp'},
  ['Gong de Zul\'Farrak']={t=500,icon='Temp'},
  ['Graine de Gaea enchantée']={t=5000,icon='Temp'},
  ['Graines de plante']={t=5000,icon='Temp'},
  ['Grand balayage']={t=4000,icon='Ability_Whirlwind'},
  ['Grand kodo blanc']={t=3000,icon='Ability_Mount_Kodo_01'},
  ['Grand kodo brun']={t=3000,icon='Ability_Mount_Kodo_03'},
  ['Grand kodo gris']={t=3000,icon='Ability_Mount_Kodo_01'},
  ['Grand loup des bois']={t=3000,icon='Ability_Mount_BlackDireWolf'},
  ['Grande bombe en bronze']={t=1000,icon='Spell_Fire_SelfDestruct'},
  ['Grande bombe en cuivre']={t=1000,icon='Spell_Fire_SelfDestruct'},
  ['Grande bombe en fer']={t=1000,icon='Spell_Fire_SelfDestruct'},
  ['Grande charge d\'hydroglycérine']={t=5000,icon='Temp'},
  ['Grenade en fer']={t=1000,icon='Spell_Fire_SelfDestruct'},
  ['Grenade en thorium']={t=1000,icon='Spell_Fire_SelfDestruct'},
  ['Griffes bestiales']={t=1000,icon='Spell_Nature_Regeneration'},
  ['Griffes bestiales II']={t=1000,icon='Spell_Nature_Regeneration'},
  ['Griffes bestiales III']={t=1000,icon='Spell_Nature_Regeneration'},
  ['Grog vert gordok']={t=1000,icon='INV_Drink_03'},
  ['Grognement d\'intimidation']={t=1000,icon='Ability_Racial_Cannibalize'},
  ['Grondement d’intimidation']={t=1000,icon='Ability_Racial_Cannibalize'},
  ['Grondement menaçant']={t=1000,icon='Ability_Racial_Cannibalize'},
  ['Grondement redoutable']={t=1000,icon='Ability_Racial_Cannibalize'},
  ['Gui']={t=1000,icon='INV_Misc_Branch_01'},
  ['Guérison complète']={t=1000,icon='Temp'},
  ['Guérison des maladies']={t=2500,icon='Spell_Holy_NullifyDisease'},
  ['Guérison ténébreuse']={t=3500,icon='Spell_Shadow_ChillTouch'},
  ['Harpon empoisonné']={t=2000,icon='Ability_Poisons'},
  ['Heurtoir']={t=1500,icon='Ability_Warrior_DecisiveStrike'},
  ['Hibernation']={t=1500,icon='Spell_Nature_Sleep'},
  ['Horion']={t=1000,icon='Temp'},
  ['Huile de mana brillante']={t=3000,icon='Temp'},
  ['Huile de mana inférieure']={t=3000,icon='Temp'},
  ['Huile de mana mineure']={t=3000,icon='Temp'},
  ['Huile de sorcier']={t=3000,icon='Temp'},
  ['Huile de sorcier brillante']={t=3000,icon='Temp'},
  ['Huile de sorcier bénie']={t=3000,icon='Temp'},
  ['Huile de sorcier inférieure']={t=3000,icon='Temp'},
  ['Huile de sorcier mineure']={t=3000,icon='Temp'},
  ['Huile des ténèbres']={t=3000,icon='Temp'},
  ['Huile glaciale']={t=3000,icon='Temp'},
  ['Humus rampant']={t=3000,icon='Spell_Shadow_CreepingPlague'},
  ['Hurlement de terreur']={t=2000,icon='Spell_Shadow_DeathScream'},
  ['Hurlement sanguinaire']={t=1000,icon='Spell_Shadow_LifeDrain'},
  ['Hurleur Frostwolf']={t=3000,icon='Ability_Mount_WhiteDireWolf'},
  ['Identifier la progéniture']={t=10000,icon='Spell_Lightning_LightningBolt01'},
  ['Ignition']={t=3000,icon='Temp'},
  ['Illumination']={t=2000,icon='Spell_Shadow_Fumble'},
  ['Illusion permanente : prêtresse Tyriona']={t=3000,icon='Ability_Rogue_Disguise'},
  ['Illusion permanente Tyrion']={t=3000,icon='Ability_Rogue_Disguise'},
  ['Immolation']={t=2000,icon='Spell_Fire_Immolation'},
  ['Implosion']={t=10000,icon='Spell_Fire_SelfDestruct'},
  ['Imprégnation d\'une arme – Tueur de bêtes']={t=5000,icon='Spell_Holy_GreaterHeal'},
  ['Imprégnation de cape – Protection inférieure']={t=5000,icon='Spell_Holy_GreaterHeal'},
  ['Imprégnation de cape – Résistance mineure']={t=5000,icon='Spell_Holy_GreaterHeal'},
  ['Imprégnation de plastron - Esprit mineur']={t=5000,icon='Spell_Holy_GreaterHeal'},
  ['Imprégnation de plastron – Absorption inférieure']={t=5000,icon='Spell_Holy_GreaterHeal'},
  ['Imprégnation de plastron – Esprit inférieur']={t=5000,icon='Spell_Holy_GreaterHeal'},
  ['Imprégner un plastron - Absorber']={t=5000,icon='Spell_Holy_GreaterHeal'},
  ['Incinération']={t=5000,icon='Temp'},
  ['Incinérer']={t=2000,icon='Spell_Shadow_ChillTouch'},
  ['Infection volatile']={t=2000,icon='Spell_Holy_HarmUndeadAura'},
  ['Inferno']={t=2000,icon='Spell_Shadow_SummonInfernal'},
  ['Inondation']={t=5000,icon='Temp'},
  ['Insuffler l\'esprit du seigneur Valthalak DND']={t=5000,icon='Temp'},
  ['Invisibilité']={t=3000,icon='Spell_Nature_Invisibilty'},
  ['Invisibilité inférieure']={t=3000,icon='Spell_Magic_LesserInvisibilty'},
  ['Invisibilité supérieure']={t=3000,icon='Spell_Nature_Invisibilty'},
  ['Invitation lunaire']={t=5000,icon='Spell_Arcane_TeleportMoonglade'},
  ['Invocation']={t=1000,icon='Spell_Arcane_Blink'},
  ['Invocation d\'Aquementas']={t=2000,icon='Temp'},
  ['Invocation d\'Ar\'lia']={t=2500,icon='Spell_Nature_GroundingTotem'},
  ['Invocation d\'Echeyakee']={t=500,icon='Spell_Shadow_LifeDrain'},
  ['Invocation d\'Edana Serrehaine']={t=4000,icon='Temp'},
  ['Invocation d\'Ishamuhale']={t=2000,icon='Spell_Shadow_LifeDrain'},
  ['Invocation d\'un Palefroi corrompu Furis TEST DND']={t=5000,icon='Temp'},
  ['Invocation d\'un ancona']={t=1000,icon='Ability_Seal'},
  ['Invocation d\'un bébé requin']={t=1000,icon='Ability_Seal'},
  ['Invocation d\'un bébé tigre blanc']={t=1000,icon='Ability_Seal'},
  ['Invocation d\'un cacatoès']={t=1000,icon='Ability_Seal'},
  ['Invocation d\'un cafard']={t=1000,icon='Ability_Seal'},
  ['Invocation d\'un char d\'assaut qiraji bleu']={t=3000,icon='INV_Misc_QirajiCrystal_04'},
  ['Invocation d\'un char d\'assaut qiraji jaune']={t=3000,icon='INV_Misc_QirajiCrystal_01'},
  ['Invocation d\'un char d\'assaut qiraji noir']={t=3000,icon='INV_Misc_QirajiCrystal_05'},
  ['Invocation d\'un char d\'assaut qiraji rouge']={t=3000,icon='INV_Misc_QirajiCrystal_02'},
  ['Invocation d\'un char d\'assaut qiraji vert']={t=3000,icon='INV_Misc_QirajiCrystal_03'},
  ['Invocation d\'un chasseur corrompu']={t=10000,icon='Spell_Shadow_SummonFelHunter'},
  ['Invocation d\'un chasseur corrompu Witherbark']={t=3000,icon='Spell_Shadow_SummonFelHunter'},
  ['Invocation d\'un chat (Bombay)']={t=1000,icon='Ability_Seal'},
  ['Invocation d\'un chat (Cornouailles)']={t=1000,icon='Ability_Seal'},
  ['Invocation d\'un chat (Maine Coon)']={t=1000,icon='Ability_Seal'},
  ['Invocation d\'un chat (siamois)']={t=1000,icon='Ability_Seal'},
  ['Invocation d\'un chat (tigré argent)']={t=1000,icon='Ability_Seal'},
  ['Invocation d\'un chat (tigré orange)']={t=1000,icon='Ability_Seal'},
  ['Invocation d\'un chaton blanc']={t=1000,icon='Ability_Seal'},
  ['Invocation d\'un chaton commun']={t=2000,icon='INV_Box_PetCarrier_01'},
  ['Invocation d\'un chaton corrompu']={t=1000,icon='Ability_Seal'},
  ['Invocation d\'un cheval de guerre']={t=3000,icon='Spell_Nature_Swiftness'},
  ['Invocation d\'un chien de prairie']={t=1000,icon='Ability_Seal'},
  ['Invocation d\'un cobra noir']={t=1000,icon='Ability_Seal'},
  ['Invocation d\'un cockatiel']={t=1000,icon='Ability_Seal'},
  ['Invocation d\'un coureur bleu']={t=1000,icon='Ability_Seal'},
  ['Invocation d\'un crapaud mécanique']={t=1000,icon='Ability_Seal'},
  ['Invocation d\'un cyclonien']={t=10000,icon='Spell_Nature_EarthBind'},
  ['Invocation d\'un destrier de l\'effroi']={t=3000,icon='Ability_Mount_Dreadsteed'},
  ['Invocation d\'un destrier de l\'effroi xorothien']={t=5000,icon='Temp'},
  ['Invocation d\'un destrier infernal illusoire']={t=2000,icon='Spell_Fire_SealOfFire'},
  ['Invocation d\'un diablotin']={t=10000,icon='Spell_Shadow_SummonImp'},
  ['Invocation d\'un dragonnet d\'Onyxia']={t=2000,icon='Temp'},
  ['Invocation d\'un dragonnet de la colonie']={t=2000,icon='Temp'},
  ['Invocation d\'un défenseur']={t=5000,icon='Spell_Nature_Purge'},
  ['Invocation d\'un démon de l\'Orbe']={t=5000,icon='Temp'},
  ['Invocation d\'un esprit d\'antan']={t=4000,icon='Spell_Nature_Purge'},
  ['Invocation d\'un esprit de hurleur']={t=2000,icon='Temp'},
  ['Invocation d\'un esprit du marais']={t=1500,icon='Spell_Nature_AbolishMagic'},
  ['Invocation d\'un exhalombre']={t=5000,icon='Spell_Shadow_RaiseDead'},
  ['Invocation d\'un faeling']={t=1000,icon='Ability_Seal'},
  ['Invocation d\'un feu follet elfique']={t=1000,icon='Ability_Seal'},
  ['Invocation d\'un garde des sorts']={t=5000,icon='Spell_Nature_Purge'},
  ['Invocation d\'un golem télécommandé']={t=3000,icon='Ability_Repair'},
  ['Invocation d\'un grand-duc']={t=1000,icon='Ability_Seal'},
  ['Invocation d\'un gryphon']={t=3000,icon='Ability_BullRush'},
  ['Invocation d\'un jeune dragonnet cramoisi']={t=1000,icon='Ability_Seal'},
  ['Invocation d\'un jeune dragonnet d\'azur']={t=1000,icon='Ability_Seal'},
  ['Invocation d\'un jeune dragonnet d\'émeraude']={t=1000,icon='Ability_Seal'},
  ['Invocation d\'un jeune dragonnet de bronze']={t=1000,icon='Ability_Seal'},
  ['Invocation d\'un jeune dragonnet sombre']={t=1000,icon='Ability_Seal'},
  ['Invocation d\'un jeune hippogryphe']={t=1000,icon='Ability_Seal'},
  ['Invocation d\'un jeune serpent']={t=1000,icon='Ability_Seal'},
  ['Invocation d\'un jeune worg']={t=1000,icon='Ability_Seal'},
  ['Invocation d\'un jubelin']={t=1000,icon='Ability_Seal'},
  ['Invocation d\'un lapin']={t=1000,icon='Ability_Seal'},
  ['Invocation d\'un lapin patte-blanche']={t=1000,icon='Ability_Seal'},
  ['Invocation d\'un lapin tacheté']={t=1000,icon='Ability_Seal'},
  ['Invocation d\'un laquais rebelle']={t=2000,icon='Spell_Shadow_RaiseDead'},
  ['Invocation d\'un limon des marais']={t=2500,icon='Spell_Shadow_BlackPlague'},
  ['Invocation d\'un macaw aile verte']={t=1000,icon='Ability_Seal'},
  ['Invocation d\'un macaw jacinthe']={t=1000,icon='Ability_Seal'},
  ['Invocation d\'un marcheur du Vide']={t=10000,icon='Spell_Shadow_SummonVoidWalker'},
  ['Invocation d\'un minuscule dragon rouge']={t=1000,icon='Ability_Seal'},
  ['Invocation d\'un minuscule dragon vert']={t=1000,icon='Ability_Seal'},
  ['Invocation d\'un mocassin d\'eau']={t=1000,icon='Ability_Seal'},
  ['Invocation d\'un orphelin']={t=1000,icon='Ability_Seal'},
  ['Invocation d\'un palefroi corrompu']={t=3000,icon='Spell_Nature_Swiftness'},
  ['Invocation d\'un panda']={t=1000,icon='Ability_Seal'},
  ['Invocation d\'un perroquet sanglant']={t=1000,icon='Ability_Seal'},
  ['Invocation d\'un poulet des prairies']={t=1000,icon='Ability_Seal'},
  ['Invocation d\'un poulet fermier']={t=1000,icon='Ability_Seal'},
  ['Invocation d\'un poulet mécanique']={t=1000,icon='Ability_Seal'},
  ['Invocation d\'un ravageur magram']={t=4000,icon='Spell_Shadow_RaiseDead'},
  ['Invocation d\'un rejeton de Bael\'Gar']={t=4000,icon='Spell_Fire_LavaSpawn'},
  ['Invocation d\'un robot']={t=1000,icon='Ability_Seal'},
  ['Invocation d\'un robot d\'alarme']={t=250,icon='INV_Gizmo_08'},
  ['Invocation d\'un roc de Plymouth blanc']={t=1000,icon='Ability_Seal'},
  ['Invocation d\'un scarabée putride']={t=2000,icon='Spell_Shadow_CarrionSwarm'},
  ['Invocation d\'un serpent albinos']={t=1000,icon='Ability_Seal'},
  ['Invocation d\'un serpent brun']={t=1000,icon='Ability_Seal'},
  ['Invocation d\'un serpent cramoisi']={t=1000,icon='Ability_Seal'},
  ['Invocation d\'un serpent écarlate']={t=1000,icon='Ability_Seal'},
  ['Invocation d\'un serviteur']={t=500,icon='Temp'},
  ['Invocation d\'un serviteur Infernal']={t=2000,icon='Spell_Shadow_SummonInfernal'},
  ['Invocation d\'un serviteur de Tervosh']={t=4000,icon='Spell_Frost_Wisp'},
  ['Invocation d\'un spectre du syndicat']={t=1000,icon='Spell_Shadow_Twilight'},
  ['Invocation d\'un squelette']={t=2000,icon='Spell_Shadow_RaiseDead'},
  ['Invocation d\'un squelette atal\'ai']={t=1000,icon='Spell_Shadow_RaiseDead'},
  ['Invocation d\'un squelette fragile']={t=10000,icon='Spell_Shadow_RaiseDead'},
  ['Invocation d\'un suinteux dégoûtant']={t=1000,icon='Ability_Seal'},
  ['Invocation d\'un sylvain']={t=3000,icon='Spell_Nature_ProtectionformNature'},
  ['Invocation d\'un sénégal']={t=1000,icon='Ability_Seal'},
  ['Invocation d\'un théurge']={t=5000,icon='Spell_Nature_Purge'},
  ['Invocation d\'un tisseur d\'effroi Blackhand']={t=5000,icon='Spell_Nature_Purge'},
  ['Invocation d\'un vétéran Blackhand']={t=5000,icon='Spell_Nature_Purge'},
  ['Invocation d\'un yéti mécanique paisible']={t=1000,icon='Ability_Seal'},
  ['Invocation d\'un zergling']={t=1000,icon='Ability_Seal'},
  ['Invocation d\'un zombie']={t=2000,icon='Spell_Shadow_RaiseDead'},
  ['Invocation d\'un écureuil Sniffetarin']={t=2000,icon='Spell_Nature_ProtectionformNature'},
  ['Invocation d\'un élémentaire d\'eau']={t=2000,icon='Spell_Shadow_SealOfKings'},
  ['Invocation d\'une bombe']={t=1000,icon='Ability_Seal'},
  ['Invocation d\'une chouette blanche']={t=1000,icon='Ability_Seal'},
  ['Invocation d\'une chouette-faucon']={t=1000,icon='Ability_Seal'},
  ['Invocation d\'une couveuse']={t=1000,icon='Ability_Seal'},
  ['Invocation d\'une faille du Rêve']={t=10000,icon='Temp'},
  ['Invocation d\'une flamme vive']={t=2000,icon='Spell_Fire_Fire'},
  ['Invocation d\'une gargouille Aile-de-roc']={t=10000,icon='Spell_Shadow_UnsummonBuilding'},
  ['Invocation d\'une grande chouette cornue']={t=1000,icon='Ability_Seal'},
  ['Invocation d\'une grenouille cracheuse']={t=1000,icon='Ability_Seal'},
  ['Invocation d\'une grenouille forestière']={t=1000,icon='Ability_Seal'},
  ['Invocation d\'une grenouille insulaire']={t=1000,icon='Ability_Seal'},
  ['Invocation d\'une grenouille sylvestre']={t=1000,icon='Ability_Seal'},
  ['Invocation d\'une gueule d\'acier albinos']={t=1000,icon='Ability_Seal'},
  ['Invocation d\'une gueule d\'acier caouanne']={t=1000,icon='Ability_Seal'},
  ['Invocation d\'une gueule d\'acier imbriqué']={t=1000,icon='Ability_Seal'},
  ['Invocation d\'une gueule d\'acier luth']={t=1000,icon='Ability_Seal'},
  ['Invocation d\'une gueule d\'acier olivâtre']={t=1000,icon='Ability_Seal'},
  ['Invocation d\'une illusion impalpable']={t=2000,icon='Spell_Fire_SealOfFire'},
  ['Invocation d\'une illusion lupine']={t=1000,icon='Spell_Shadow_Teleport'},
  ['Invocation d\'une succube']={t=10000,icon='Spell_Shadow_SummonSuccubus'},
  ['Invocation d\'une vipère']={t=1500,icon='Spell_Nature_ResistMagic'},
  ['Invocation d\'une âme en peine illusoire']={t=1000,icon='Spell_Shadow_Teleport'},
  ['Invocation de Bip-bip']={t=1000,icon='Ability_Seal'},
  ['Invocation de Bourbie']={t=1000,icon='Ability_Seal'},
  ['Invocation de Dagun']={t=5000,icon='Temp'},
  ['Invocation de Diablo']={t=1000,icon='Ability_Seal'},
  ['Invocation de Gloubie']={t=1000,icon='Ability_Seal'},
  ['Invocation de Grince-mâchoires']={t=3000,icon='Temp'},
  ['Invocation de Lunegriffe']={t=3000,icon='Temp'},
  ['Invocation de M. Trémousse']={t=1000,icon='Ability_Seal'},
  ['Invocation de Moustaches']={t=1000,icon='Ability_Seal'},
  ['Invocation de Murki']={t=1000,icon='Ability_Seal'},
  ['Invocation de Myzrael']={t=10000,icon='Spell_Shadow_LifeDrain'},
  ['Invocation de Polet']={t=1000,icon='Ability_Seal'},
  ['Invocation de Ragnaros']={t=10000,icon='Spell_Fire_LavaSpawn'},
  ['Invocation de Razelikh']={t=10000,icon='Temp'},
  ['Invocation de Shy-Rotam']={t=2500,icon='Temp'},
  ['Invocation de Terkie']={t=1000,icon='Ability_Seal'},
  ['Invocation de Thelrin DND']={t=1000,icon='Temp'},
  ['Invocation de Tonitruante']={t=10000,icon='Temp'},
  ['Invocation de bombe des gobelins']={t=250,icon='Ability_Repair'},
  ['Invocation de bâton magique']={t=2000,icon='INV_Staff_26'},
  ['Invocation de destrier']={t=3000,icon='Ability_Mount_Charger'},
  ['Invocation de frappe-ténèbres']={t=10000,icon='Temp'},
  ['Invocation de jeune fée fléchetteuse']={t=1000,icon='Ability_Seal'},
  ['Invocation de l\'Autel des invocations']={t=10000,icon='Temp'},
  ['Invocation de l\'esprit d\'un ancien']={t=2000,icon='Temp'},
  ['Invocation de l\'esprit du sanglier']={t=1500,icon='Spell_Magic_PolymorphPig'},
  ['Invocation de la bannière de Karang']={t=500,icon='Temp'},
  ['Invocation de marcheur du néant']={t=4000,icon='Spell_Shadow_GatherShadows'},
  ['Invocation de minuscules parasites Pétale-de-sang']={t=2000,icon='Spell_Shadow_DarkSummoning'},
  ['Invocation de tréants alliés']={t=1500,icon='Spell_Nature_ForceOfNature'},
  ['Invocation des braises']={t=2000,icon='Spell_Fire_Fire'},
  ['Invocation des gardiens des rêves illusoires']={t=1000,icon='Spell_Shadow_Teleport'},
  ['Invocation des marionnettes d\'Helcular']={t=3000,icon='Spell_Shadow_Haunting'},
  ['Invocation du Cercle des appels']={t=10000,icon='Temp'},
  ['Invocation du dieu araignée']={t=10000,icon='Spell_Shadow_LifeDrain'},
  ['Invocation du trésor de la Horde']={t=1500,icon='Temp'},
  ['Invocation du visage de Gunther']={t=2000,icon='Temp'},
  ['Invocation d’un faux PNJ']={t=20000,icon='Spell_Shadow_UnsummonBuilding'},
  ['J\'eevee invoque un objet']={t=2000,icon='Temp'},
  ['Jet d\'encre']={t=1000,icon='Spell_Nature_Sleep'},
  ['Jet de torche']={t=1000,icon='Spell_Fire_Flare'},
  ['Jet d’acide']={t=1000,icon='INV_Drink_06'},
  ['Jument alezane']={t=3000,icon='Ability_Mount_RidingHorse'},
  ['Kalaran invoque une torche']={t=1000,icon='Temp'},
  ['Kev']={t=3000,icon='Spell_Fire_FireBolt'},
  ['Kit d\'entretien mécanique']={t=2000,icon='INV_Gizmo_03'},
  ['Kodo bleu']={t=3000,icon='Ability_Mount_Kodo_02'},
  ['Kodo brun']={t=3000,icon='Ability_Mount_Kodo_03'},
  ['Kodo de guerre noir']={t=3000,icon='Ability_Mount_Kodo_01'},
  ['Kodo de monte']={t=3000,icon='INV_Misc_Head_Tauren_02'},
  ['Kodo gris']={t=3000,icon='Ability_Mount_Kodo_01'},
  ['Kodo vert']={t=3000,icon='Ability_Mount_Kodo_02'},
  ['Kombobulateur kodo']={t=5000,icon='Trade_Fishing'},
  ['La fuite du Bly\'s Band']={t=10000,icon='INV_Misc_Rune_01'},
  ['La princesse invoque le portail']={t=10000,icon='Spell_Arcane_PortalIronForge'},
  ['La ruse du raptor']={t=3000,icon='INV_Misc_MonsterClaw_02'},
  ['Lame fanatique']={t=1000,icon='Spell_Fire_Immolation'},
  ['Lancement de boule puante']={t=2000,icon='INV_Misc_Bowl_01'},
  ['Lancement de torche']={t=3000,icon='Spell_Fire_Fireball02'},
  ['Lancer de Hache']={t=1000,icon='INV_Axe_08'},
  ['Lancer de dynamite']={t=2000,icon='Spell_Fire_SelfDestruct'},
  ['Lancer de potion']={t=2000,icon='Spell_Misc_Drink'},
  ['Lancer de rocher']={t=3000,icon='Ability_GolemStormBolt'},
  ['Lancer de rocher II']={t=3000,icon='Ability_GolemStormBolt'},
  ['Lancer l\'objet noyé dans les cauchemars']={t=2000,icon='Spell_Fire_SelfDestruct'},
  ['Lancer la bière sombrefer']={t=500,icon='Temp'},
  ['Lancer une flèche de Cupidon']={t=1000,icon='Temp'},
  ['Langue guérisseuse']={t=1000,icon='Spell_Holy_Heal'},
  ['Langue guérisseuse II']={t=1000,icon='Spell_Holy_Heal'},
  ['Le Brave reprend sa forme de poulet']={t=1000,icon='Ability_Racial_BearForm'},
  ['Le Mastoc']={t=3000,icon='Spell_Fire_SelfDestruct'},
  ['Les sbires d\'Urok disparaissent']={t=2000,icon='Temp'},
  ['Lever le sceau']={t=2000,icon='Temp'},
  ['Lianes avides']={t=1000,icon='Spell_Nature_Earthquake'},
  ['Libération de Yenniku']={t=4000,icon='Spell_Shadow_LifeDrain'},
  ['Libération de diablotin']={t=2000,icon='Temp'},
  ['Libérer Grifferage']={t=10000,icon='Temp'},
  ['Libérer J\'eevee']={t=2000,icon='Temp'},
  ['Libérer le chaton de Winna']={t=1000,icon='Ability_Seal'},
  ['Libérer le yéti d\'Umi']={t=2000,icon='Temp'},
  ['Liens partagés']={t=1500,icon='Spell_Shadow_UnsummonBuilding'},
  ['Limace des ténèbres']={t=5000,icon='Spell_Shadow_CreepingPlague'},
  ['Limace noire']={t=3000,icon='Spell_Shadow_CallofBone'},
  ['Linceul de vents']={t=2000,icon='Spell_Nature_Cyclone'},
  ['Lit de mort']={t=2000,icon='Spell_Shadow_Twilight'},
  ['Longue-vue']={t=1000,icon='Ability_TownWatch'},
  ['Loup arctique']={t=3000,icon='Ability_Mount_WhiteDireWolf'},
  ['Loup blanc']={t=3000,icon='Ability_Mount_WhiteDireWolf'},
  ['Loup brun']={t=3000,icon='Ability_Mount_BlackDireWolf'},
  ['Loup brun rapide']={t=3000,icon='Ability_Mount_BlackDireWolf'},
  ['Loup de guerre noir']={t=3000,icon='Ability_Mount_BlackDireWolf'},
  ['Loup des bois rapide']={t=3000,icon='Ability_Mount_WhiteDireWolf'},
  ['Loup fantôme']={t=3000,icon='Spell_Nature_SpiritWolf'},
  ['Loup gris']={t=3000,icon='Ability_Mount_WhiteDireWolf'},
  ['Loup gris rapide']={t=3000,icon='Ability_Mount_WhiteDireWolf'},
  ['Loup noir']={t=3000,icon='Ability_Mount_BlackDireWolf'},
  ['Loup redoutable']={t=3000,icon='Ability_Mount_WhiteDireWolf'},
  ['Loup rouge']={t=3000,icon='Ability_Mount_BlackDireWolf'},
  ['Lucioles']={t=2000,icon='Spell_Nature_FaerieFire'},
  ['Lumière sacrée']={t=2500,icon='Spell_Holy_HolyBolt'},
  ['Lâcher les Chiens']={t=2000,icon='Temp'},
  ['Léopard']={t=3000,icon='Ability_Mount_JungleTiger'},
  ['Léopard primal']={t=3000,icon='Ability_Mount_JungleTiger'},
  ['Léthargie cristalline']={t=2000,icon='Spell_Nature_Sleep'},
  ['Léthargie druidique']={t=2500,icon='Spell_Nature_Sleep'},
  ['Léthargie profonde']={t=1000,icon='Spell_Shadow_Cripple'},
  ['Main d\'Iruxos']={t=5000,icon='Temp'},
  ['Mains violettes']={t=4000,icon='Spell_Shadow_SiphonMana'},
  ['Maladresse']={t=1000,icon='Spell_Shadow_Fumble'},
  ['Maladresse II']={t=1000,icon='Spell_Shadow_Fumble'},
  ['Maladresse III']={t=1000,icon='Spell_Shadow_Fumble'},
  ['Malédiction d\'Hakkar']={t=2500,icon='Spell_Shadow_GatherShadows'},
  ['Malédiction de Brandefeu']={t=2000,icon='Ability_Creature_Cursed_03'},
  ['Malédiction de Shahram']={t=1000,icon='Spell_Magic_LesserInvisibilty'},
  ['Malédiction de Stalvan']={t=1000,icon='Spell_Shadow_ShadowPact'},
  ['Malédiction de Thule']={t=2000,icon='Spell_Shadow_ShadowPact'},
  ['Malédiction de Timmy']={t=1000,icon='Spell_Shadow_ShadowPact'},
  ['Malédiction de faiblesse']={t=1000,icon='Spell_Shadow_CurseOfMannoroth'},
  ['Malédiction de guérison']={t=1000,icon='Spell_Shadow_AntiShadow'},
  ['Malédiction de la Banshee']={t=2000,icon='Spell_Nature_Drowsy'},
  ['Malédiction des Magram défunts']={t=2000,icon='Spell_Shadow_UnholyFrenzy'},
  ['Malédiction des Mort-bois']={t=2000,icon='Spell_Shadow_GatherShadows'},
  ['Malédiction des Tribus']={t=2000,icon='Spell_Shadow_CurseOfMannoroth'},
  ['Malédiction des épines']={t=2000,icon='Spell_Shadow_AntiShadow'},
  ['Malédiction du Rat pestiféré']={t=1500,icon='Spell_Shadow_UnholyFrenzy'},
  ['Malédiction du Sang']={t=2000,icon='Spell_Shadow_RitualOfSacrifice'},
  ['Malédiction du Sombre maître']={t=2000,icon='Spell_Shadow_AntiShadow'},
  ['Malédiction gangreneuse']={t=4000,icon='Temp'},
  ['Malédiction impie']={t=1000,icon='Spell_Shadow_CurseOfMannoroth'},
  ['Maléfice']={t=2000,icon='Spell_Nature_Polymorph'},
  ['Maléfice de Ravenclaw']={t=2000,icon='Spell_Shadow_Charm'},
  ['Maléfice vaudou']={t=1000,icon='Spell_Shadow_CurseOfMannoroth'},
  ['Manifestation des esprits']={t=5000,icon='Spell_Totem_WardOfDraining'},
  ['Marque des flammes']={t=1000,icon='Spell_Fire_Fireball'},
  ['Marteau de courroux']={t=1000,icon='Ability_ThunderClap'},
  ['Masse suintante']={t=1000,icon='Spell_Nature_CorrosiveBreath'},
  ['Mauvais oeil']={t=1500,icon='Spell_Shadow_Charm'},
  ['Maître de l\'évasion']={t=500,icon='Ability_Rogue_Trip'},
  ['Mille lames']={t=250,icon='INV-Sword_53'},
  ['Minage']={t=3200,icon='Trade_Mining'},
  ['Mine gobeline']={t=1000,icon='Spell_Shadow_Metamorphosis'},
  ['Mine terrestre sombrefer']={t=1000,icon='Spell_Shadow_Metamorphosis'},
  ['Mineur squelettique Explosion']={t=5000,icon='Spell_Fire_SelfDestruct'},
  ['Mini-canon']={t=100,icon='INV_Musket_04'},
  ['Mise en cage']={t=2000,icon='Spell_Shadow_Teleport'},
  ['Mitrailleuse']={t=500,icon='Ability_Marksmanship'},
  ['Modifier le comportement']={t=2000,icon='INV_Gizmo_01'},
  ['Monture chromatique']={t=3000,icon='INV_Misc_Head_Dragon_Black'},
  ['Morsure d\'âme']={t=2000,icon='Spell_Shadow_SiphonMana'},
  ['Mort & décomposition']={t=2000,icon='Spell_Shadow_DeathAndDecay'},
  ['Mort de Taelan']={t=2000,icon='Temp'},
  ['Mort de Windsor DND']={t=1500,icon='Temp'},
  ['Mortier d\'épice']={t=500,icon='Spell_Fire_Fireball02'},
  ['Mortier des gobelins']={t=1000,icon='Spell_Fire_SelfDestruct'},
  ['Mortier massif']={t=3000,icon='Temp'},
  ['Mot de dégel']={t=5000,icon='Temp'},
  ['Mouton explosif']={t=2000,icon='Ability_Repair'},
  ['Murmures sombres']={t=3000,icon='Spell_Shadow_Haunting'},
  ['Mât du solstice d\'été']={t=10000,icon='Spell_Shadow_Twilight'},
  ['Mécanotrotteur acier']={t=3000,icon='Ability_Mount_MechaStrider'},
  ['Mécanotrotteur blanc']={t=3000,icon='Ability_Mount_MechaStrider'},
  ['Mécanotrotteur blanc rapide']={t=3000,icon='Ability_Mount_MechaStrider'},
  ['Mécanotrotteur bleu']={t=3000,icon='Ability_Mount_MechaStrider'},
  ['Mécanotrotteur bleu clair']={t=3000,icon='Ability_Mount_MechaStrider'},
  ['Mécanotrotteur brut']={t=3000,icon='Ability_Mount_MechaStrider'},
  ['Mécanotrotteur jaune rapide']={t=3000,icon='Ability_Mount_MechaStrider'},
  ['Mécanotrotteur rouge']={t=3000,icon='Ability_Mount_MechaStrider'},
  ['Mécanotrotteur rouge & bleu']={t=3000,icon='Ability_Mount_MechaStrider'},
  ['Mécanotrotteur vert']={t=3000,icon='Ability_Mount_MechaStrider'},
  ['Mécanotrotteur vert fluorescent']={t=3000,icon='Ability_Mount_MechaStrider'},
  ['Mécanotrotteur vert rapide']={t=3000,icon='Ability_Mount_MechaStrider'},
  ['Mécanotrotteur violet']={t=3000,icon='Ability_Mount_MechaStrider'},
  ['Mégavolt']={t=2000,icon='Spell_Nature_ChainLightning'},
  ['Mélange les Limons']={t=500,icon='INV_Potion_12'},
  ['Métal en fusion']={t=2000,icon='Spell_Fire_Fireball'},
  ['Métamorphose']={t=1500,icon='Spell_Nature_Polymorph'},
  ['Métamorphose : cochon']={t=1500,icon='Spell_Magic_PolymorphPig'},
  ['Métamorphose : tortue']={t=1500,icon='Ability_Hunter_Pet_Turtle'},
  ['Métamorphose : vache']={t=1500,icon='Spell_Nature_Polymorph_Cow'},
  ['Métamorphose de Ruul Snowhoof (DND)']={t=1000,icon='Temp'},
  ['Naissance']={t=2000,icon='Temp'},
  ['Narain !']={t=3000,icon='INV_Misc_Head_Gnome_01'},
  ['Nettoyer la boule puante']={t=5000,icon='Temp'},
  ['Nostalgie']={t=4000,icon='Spell_Shadow_LifeDrain'},
  ['Nouveau projectile magique (TEST)']={t=2000,icon='Temp'},
  ['Nova de l\'ombre II']={t=3000,icon='Spell_Shadow_ShadeTrueSight'},
  ['Nuage d\'éclairs']={t=3000,icon='Spell_Nature_CallStorm'},
  ['Nuage de poussière']={t=1500,icon='Ability_Hibernation'},
  ['Nuage empoisonné']={t=1000,icon='Spell_Nature_Regenerate'},
  ['Nuée de peste']={t=2000,icon='Spell_Shadow_CallofBone'},
  ['Nuée de sauterelles']={t=3000,icon='Spell_Nature_InsectSwarm'},
  ['Oeil de Kilrogg']={t=5000,icon='Spell_Shadow_EvilEye'},
  ['Oeil de Ryson']={t=1000,icon='Ability_Hunter_EagleEye'},
  ['Oeil de Yesmur (PT)']={t=2000,icon='Temp'},
  ['Oeil de la bête']={t=2000,icon='Ability_EyeOfTheOwl'},
  ['Oeil de poisson aquadynamique']={t=5000,icon='INV_Misc_Spyglass_01'},
  ['Oeil-qui-voit-tout de Ryson']={t=1000,icon='Ability_Ambush'},
  ['Offrande à Grom']={t=2000,icon='Temp'},
  ['Offrande à Uther']={t=2000,icon='Temp'},
  ['Ombre perforante']={t=2000,icon='Spell_Shadow_ChillTouch'},
  ['Ombres enveloppantes']={t=1500,icon='Spell_Shadow_LifeDrain02'},
  ['Onde de choc']={t=2000,icon='Ability_Whirlwind'},
  ['Ouverture']={t=5000,icon='Temp'},
  ['Ouverture - pas de texte']={t=5000,icon='Temp'},
  ['Ouverture de la Boîte à lettres de Stratholme']={t=5000,icon='Temp'},
  ['Ouverture de la Cage']={t=5000,icon='Temp'},
  ['Ouverture de la porte barrée']={t=5000,icon='Temp'},
  ['Ouverture du Coffre de scarabées']={t=5000,icon='Temp'},
  ['Ouverture du Coffre de scarabées supérieur']={t=5000,icon='Temp'},
  ['Ouverture du Coffre secret']={t=5000,icon='Temp'},
  ['Ouverture du Tonneau de termites']={t=5000,icon='Temp'},
  ['Ouverture du coffre']={t=5000,icon='Temp'},
  ['Ouverture du coffre blindé']={t=5000,icon='Temp'},
  ['Ouverture du coffre de Benedict']={t=5000,icon='Temp'},
  ['Ouverture du coffre sombre']={t=5000,icon='Temp'},
  ['Ouverture du coffre à butin']={t=5000,icon='Temp'},
  ['Ouverture du coffret à reliques']={t=5000,icon='Temp'},
  ['Ouvre le coffre']={t=5000,icon='Temp'},
  ['Ouvrerture du coffre']={t=5000,icon='Temp'},
  ['Ozzie explose']={t=2000,icon='Temp'},
  ['Palefroi bai rapide']={t=3000,icon='Ability_Mount_RidingHorse'},
  ['Palefroi blanc rapide']={t=3000,icon='Ability_Mount_RidingHorse'},
  ['Palefroi de guerre noir']={t=3000,icon='Ability_Mount_NightmareHorse'},
  ['Palefroi squelettique']={t=3000,icon='Ability_Mount_Undeadhorse'},
  ['Palomino rapide']={t=3000,icon='Ability_Mount_RidingHorse'},
  ['Panthère']={t=3000,icon='Ability_Mount_BlackPanther'},
  ['Panthère tachetée']={t=3000,icon='Ability_Mount_BlackPanther'},
  ['Parasite']={t=2000,icon='Ability_Poisons'},
  ['Pardon']={t=1000,icon='Temp'},
  ['Parler avec les têtes']={t=5000,icon='Spell_Shadow_LifeDrain'},
  ['Passe-partout en arcanite']={t=5000,icon='Temp'},
  ['Passe-partout en argent']={t=5000,icon='Temp'},
  ['Passe-partout en or']={t=5000,icon='Temp'},
  ['Passe-partout en vrai-argent']={t=5000,icon='Temp'},
  ['Peau de pierre']={t=6000,icon='Spell_Nature_EnchantArmor'},
  ['Perfectionnement d\'arme contondante']={t=3000,icon='Temp'},
  ['Perfectionnement d\'arme contondante II']={t=3000,icon='Temp'},
  ['Perfectionnement d\'arme contondante III']={t=3000,icon='Temp'},
  ['Perfectionnement d\'arme contondante IV']={t=3000,icon='Temp'},
  ['Perfectionnement d\'arme contondante V']={t=3000,icon='Temp'},
  ['Perturbation']={t=3000,icon='Temp'},
  ['Peste fièvreuse']={t=4500,icon='Spell_Nature_NullifyDisease'},
  ['Peste galopante']={t=2000,icon='Spell_Shadow_CallofBone'},
  ['Peste émétique']={t=2000,icon='Spell_Shadow_CallofBone'},
  ['Petit pain grillé']={t=1000,icon='INV_Misc_Food_11'},
  ['Petite bombe en bronze']={t=1000,icon='Spell_Fire_SelfDestruct'},
  ['Petite charge d\'hydroglycérine']={t=5000,icon='Temp'},
  ['Peur']={t=1500,icon='Spell_Shadow_Possession'},
  ['Peur (NYI)']={t=3000,icon='Spell_Shadow_Possession'},
  ['Philtre d\'amour de Nagmara']={t=1000,icon='Temp'},
  ['Pierre de foyer']={t=10000,icon='INV_Misc_Rune_01'},
  ['Pioche démoniaque']={t=5000,icon='Temp'},
  ['Piège à ours invisible placé']={t=2000,icon='Temp'},
  ['Place la Balise d\'Ichman']={t=5000,icon='Temp'},
  ['Place la Balise de Guse']={t=5000,icon='Temp'},
  ['Place la Balise de Jeztor']={t=5000,icon='Temp'},
  ['Place la Balise de Mulverick']={t=5000,icon='Temp'},
  ['Place la Balise de Ryson']={t=5000,icon='Temp'},
  ['Place la Balise de Slidore']={t=5000,icon='Temp'},
  ['Place la Balise de Vipore']={t=5000,icon='Temp'},
  ['Place la Torche-balise']={t=2300,icon='Temp'},
  ['Place un piège à ours']={t=2000,icon='Temp'},
  ['Placement des Explosifs de Smokey']={t=3000,icon='Temp'},
  ['Placer Clairvoyant']={t=3000,icon='Temp'},
  ['Placer l\'aimant fantôme']={t=1500,icon='Temp'},
  ['Placer la bouée en arcanite']={t=2000,icon='Temp'},
  ['Placer la carcasse de battrodon']={t=2500,icon='INV_Misc_Bowl_01'},
  ['Placer la lame brute']={t=1000,icon='Temp'},
  ['Placer le Sceau brut']={t=500,icon='Temp'},
  ['Placer le brouillard toxique']={t=3000,icon='INV_Cask_01'},
  ['Placer un pendentif']={t=5000,icon='Temp'},
  ['Placer une Charge NG-5 (Rouge)']={t=5000,icon='INV_Misc_Bomb_05'},
  ['Placer une carcasse de lion']={t=2500,icon='INV_Misc_Bowl_01'},
  ['Placer une charge NG-5 (Bleue)']={t=5000,icon='INV_Misc_Bomb_05'},
  ['Plant de Tammra']={t=1300,icon='Temp'},
  ['Plante une bannière']={t=2300,icon='Temp'},
  ['Planter des Haricots magiques']={t=2000,icon='INV_Misc_Food_Wheat_02'},
  ['Planter la Pique de Nimboya préparée']={t=2000,icon='Temp'},
  ['Planter la Tête de Gor\'tesh']={t=5000,icon='Temp'},
  ['Pluie d\'acide']={t=2000,icon='Spell_Nature_Acid_01'},
  ['Pluie de feu']={t=3000,icon='Spell_Shadow_RainOfFire'},
  ['Pluie de flammes']={t=1700,icon='Spell_Fire_Fire'},
  ['Pluie de lave']={t=2000,icon='Temp'},
  ['Plume de raptor']={t=5000,icon='Temp'},
  ['Poing de Shahram']={t=1000,icon='Ability_Whirlwind'},
  ['Poing de force']={t=1000,icon='INV_Gauntlets_31'},
  ['Point de disparition des Esprits']={t=500,icon='Temp'},
  ['Pointe acérée']={t=500,icon='Ability_ImpalingBolt'},
  ['Points d\'honneur +138']={t=1000,icon='Temp'},
  ['Points d\'honneur +228']={t=1000,icon='Temp'},
  ['Points d\'honneur +2388']={t=1000,icon='INV_BannerPVP_02'},
  ['Points d\'honneur +378']={t=1000,icon='Temp'},
  ['Points d\'honneur +398']={t=1000,icon='Temp'},
  ['Points d\'honneur +50']={t=1000,icon='Temp'},
  ['Points d\'honneur +82']={t=1000,icon='Temp'},
  ['Poison affaiblissant']={t=3000,icon='Ability_PoisonSting'},
  ['Poison corrosif']={t=1500,icon='Spell_Nature_CorrosiveBreath'},
  ['Poison de distraction mentale']={t=3000,icon='Spell_Nature_NullifyDisease'},
  ['Poison de distraction mentale II']={t=3000,icon='Spell_Nature_NullifyDisease'},
  ['Poison de distraction mentale III']={t=3000,icon='Spell_Nature_NullifyDisease'},
  ['Poison douloureux']={t=3000,icon='INV_Misc_Herb_16'},
  ['Poison engourdissant']={t=1000,icon='Spell_Nature_SlowPoison'},
  ['Poison instantané']={t=3000,icon='Ability_Poisons'},
  ['Poison instantané II']={t=3000,icon='Ability_Poisons'},
  ['Poison instantané III']={t=3000,icon='Ability_Poisons'},
  ['Poison instantané IV']={t=3000,icon='Ability_Poisons'},
  ['Poison instantané V']={t=3000,icon='Ability_Poisons'},
  ['Poison instantané VI']={t=3000,icon='Ability_Poisons'},
  ['Poison lent']={t=1000,icon='Spell_Nature_SlowPoison'},
  ['Poison lent II']={t=1000,icon='Spell_Nature_SlowPoison'},
  ['Poison mortel']={t=3000,icon='Ability_Rogue_DualWeild'},
  ['Poison mortel II']={t=3000,icon='Ability_Rogue_DualWeild'},
  ['Poison mortel III']={t=3000,icon='Ability_Rogue_DualWeild'},
  ['Poison mortel IV']={t=3000,icon='Ability_Rogue_DualWeild'},
  ['Poison mortel V']={t=3000,icon='Ability_Rogue_DualWeild'},
  ['Port de l\'ombre']={t=250,icon='Spell_Shadow_AntiShadow'},
  ['Portail : Darnassus']={t=10000,icon='Spell_Arcane_PortalDarnassus'},
  ['Portail : Ironforge']={t=10000,icon='Spell_Arcane_PortalIronForge'},
  ['Portail : Karazhan']={t=10000,icon='Spell_Arcane_PortalUnderCity'},
  ['Portail : Orgrimmar']={t=10000,icon='Spell_Arcane_PortalOrgrimmar'},
  ['Portail : Stormwind']={t=10000,icon='Spell_Arcane_PortalStormWind'},
  ['Portail : Thunder Bluff']={t=10000,icon='Spell_Arcane_PortalThunderBluff'},
  ['Portail : Undercity']={t=10000,icon='Spell_Arcane_PortalUnderCity'},
  ['Portail de Goldshire']={t=5000,icon='Temp'},
  ['Portail de l\'ombre']={t=1000,icon='Spell_Shadow_SealOfKings'},
  ['Portail dimensionnel']={t=2000,icon='Temp'},
  ['Portail du Bouclier balafré']={t=1500,icon='Spell_Arcane_TeleportOrgrimmar'},
  ['Portail démoniaque']={t=500,icon='Spell_Arcane_TeleportOrgrimmar'},
  ['Poudre d\'Incendia de Magatha']={t=1300,icon='Temp'},
  ['Poudre d\'incendia']={t=5000,icon='Temp'},
  ['Poussière projetée']={t=1000,icon='Spell_Nature_Sleep'},
  ['Premiers soins']={t=3000,icon='Spell_Holy_GreaterHeal'},
  ['Prière d\'Elune']={t=1000,icon='Spell_Holy_Resurrection'},
  ['Prière de soins']={t=3000,icon='Spell_Holy_PrayerOfHealing02'},
  ['Prononcer l\'Anathème']={t=1000,icon='Temp'},
  ['Prononcer la Bénédiction']={t=1000,icon='Temp'},
  ['Présence de la mort']={t=1000,icon='Spell_Shadow_ShadeTrueSight'},
  ['Psychométrie']={t=5000,icon='Spell_Holy_Restoration'},
  ['Puissance de Ragnaros']={t=500,icon='Spell_Fire_SelfDestruct'},
  ['Puissance de Shahram']={t=1000,icon='Spell_Nature_WispSplode'},
  ['Puissante charge d\'hydroglycérine']={t=5000,icon='Temp'},
  ['Puissants sels odorants']={t=2000,icon='INV_Misc_Ammo_Gunpowder_01'},
  ['Puits de lumière']={t=1500,icon='Spell_Holy_SummonLightwell'},
  ['Purification de la Manifestation']={t=4000,icon='Temp'},
  ['Purification de serpentine']={t=30000,icon='Spell_Shadow_LifeDrain'},
  ['Purifier et placer la nourriture']={t=5000,icon='INV_Misc_Bowl_01'},
  ['Purifier la vigne de Vylevrille']={t=500,icon='Temp'},
  ['Purifier le puits Thunderhorn']={t=10000,icon='Temp'},
  ['Purifier le puits Wildmane']={t=10000,icon='Temp'},
  ['Purifier le puits Winterhoof']={t=10000,icon='Temp'},
  ['Quintessence aquatique : Eteindre la Rune du Cœur du Magma']={t=1000,icon='Temp'},
  ['Quête - Invocation d\'un Tréant']={t=3000,icon='Spell_Nature_NatureTouchGrow'},
  ['Quête - Sort de Segra Darkthorn']={t=3000,icon='Temp'},
  ['Quête - Visuel d\'invocation du héros troll']={t=30000,icon='Temp'},
  ['Quête - fin de téléportation']={t=1000,icon='Temp'},
  ['Raconter une blague']={t=2000,icon='Spell_Shadow_LifeDrain'},
  ['Rafale de flammes']={t=2200,icon='Spell_Fire_Fireball'},
  ['Rage de Thule']={t=1500,icon='Spell_Shadow_UnholyFrenzy'},
  ['Rage hurlante']={t=5000,icon='Ability_BullRush'},
  ['Ragnaros (émergé)']={t=2900,icon='Spell_Fire_LavaSpawn'},
  ['Ranimer Ringo']={t=2500,icon='Temp'},
  ['Ranimer le Rat fouisseur']={t=3000,icon='Spell_Holy_Resurrection'},
  ['Ranimer un Scarabée mort-vivant']={t=1000,icon='Spell_Shadow_Contagion'},
  ['Rapetisser']={t=3000,icon='Spell_Shadow_AntiShadow'},
  ['Rapidité enchantée']={t=3000,icon='Spell_Nature_Invisibilty'},
  ['Rappel']={t=10000,icon='Temp'},
  ['Rappel astral']={t=10000,icon='Spell_Nature_AstralRecal'},
  ['Raptor bleu rapide']={t=3000,icon='Ability_Mount_Raptor'},
  ['Raptor chamaré rouge']={t=3000,icon='Ability_Mount_Raptor'},
  ['Raptor de guerre noir']={t=3000,icon='Ability_Mount_Raptor'},
  ['Raptor ivoire']={t=3000,icon='Ability_Mount_Raptor'},
  ['Raptor obsidienne']={t=3000,icon='Ability_Mount_Raptor'},
  ['Raptor orange rapide']={t=3000,icon='Ability_Mount_Raptor'},
  ['Raptor razzashi rapide']={t=3000,icon='Ability_Mount_Raptor'},
  ['Raptor turquoise']={t=3000,icon='Ability_Mount_Raptor'},
  ['Raptor vert olive rapide']={t=3000,icon='Ability_Mount_Raptor'},
  ['Raptor violet']={t=3000,icon='Ability_Mount_Raptor'},
  ['Raptor émeraude']={t=3000,icon='Ability_Mount_Raptor'},
  ['Raviver Kroshius']={t=3000,icon='Temp'},
  ['Rayon de l\'Oeil']={t=2000,icon='Spell_Nature_CallStorm'},
  ['Reconstruction']={t=3000,icon='Temp'},
  ['Redpath corrompu']={t=2000,icon='Temp'},
  ['Regard de cristal']={t=2000,icon='Ability_GolemThunderClap'},
  ['Regard furieux']={t=500,icon='Temp'},
  ['Regard menaçant']={t=2000,icon='Spell_Shadow_Charm'},
  ['Relevé de température']={t=2000,icon='Temp'},
  ['Relâche le Limon corrompu']={t=5000,icon='INV_Potion_19'},
  ['Remplir fiole']={t=5000,icon='Temp'},
  ['Remplissage']={t=3000,icon='Temp'},
  ['Remède contre la lèpre !']={t=2000,icon='Spell_Holy_FlashHeal'},
  ['Renaissance']={t=2000,icon='Spell_Nature_Reincarnation'},
  ['Renvoi des morts-vivants']={t=1500,icon='Spell_Holy_TurnUndead'},
  ['Renvoyer le familier']={t=5000,icon='Spell_Nature_SpiritWolf'},
  ['Respiration']={t=5000,icon='Spell_Fire_Fire'},
  ['Ressusciter le familier']={t=10000,icon='Ability_Hunter_BeastSoothe'},
  ['Restauration des ténèbres']={t=2000,icon='Ability_Hunter_MendPet'},
  ['Restauration spirituelle']={t=3000,icon='Spell_Nature_MoonGlow'},
  ['Restauration spirituelle II']={t=3000,icon='Spell_Nature_MoonGlow'},
  ['Rets enveloppants']={t=2000,icon='Spell_Nature_EarthBind'},
  ['Revers']={t=1000,icon='Spell_Shadow_LifeDrain'},
  ['Rhum brun de Rumsey']={t=1000,icon='INV_Drink_04'},
  ['Rhum de Rumsey']={t=1000,icon='INV_Drink_03'},
  ['Rhum de Rumsey label noir']={t=1000,icon='INV_Drink_04'},
  ['Rhum doux de Rumsey']={t=1000,icon='INV_Drink_08'},
  ['Rimblat fait pousser fleur DND']={t=2000,icon='Temp'},
  ['Rituel d\'invocation']={t=5000,icon='Spell_Shadow_Twilight'},
  ['Rituel de malédiction']={t=10000,icon='Spell_Shadow_AntiMagicShell'},
  ['Rocher']={t=2000,icon='Ability_Throw'},
  ['Roi des Gordok']={t=1000,icon='INV_Crown_02'},
  ['Rugissement glacial']={t=1000,icon='Spell_Frost_FrostNova'},
  ['Rugissement puissant']={t=1500,icon='Spell_Fire_Fire'},
  ['Rune d\'ouverture']={t=5000,icon='Temp'},
  ['Réanimation morbide']={t=1000,icon='Spell_Shadow_RaiseDead'},
  ['Réapprovisionnement']={t=2000,icon='Spell_Misc_Drink'},
  ['Récite les mots de Cerebras']={t=3000,icon='Temp'},
  ['Rédemption']={t=10000,icon='Spell_Holy_Resurrection'},
  ['Régler']={t=7000,icon='INV_Gizmo_02'},
  ['Régénération sauvage']={t=3000,icon='Spell_Nature_Rejuvenation'},
  ['Rénovation']={t=2000,icon='Spell_Holy_Renew'},
  ['Réparer Chandelle du rituel (DND)']={t=3000,icon='Temp'},
  ['Réparer Cloche du rituel (DND)']={t=3000,icon='Temp'},
  ['Réparer le Collier de Talvash']={t=4500,icon='Spell_Holy_Restoration'},
  ['Réparer le Nœud d\'énergie']={t=3000,icon='Temp'},
  ['Réputation - Baie-du-Butin +500']={t=1000,icon='Temp'},
  ['Réputation - Boss Temple Ahn\'Qiraj']={t=1000,icon='Temp'},
  ['Réputation - Gadgetzan +500']={t=1000,icon='Temp'},
  ['Réputation - Long-Guet +500']={t=1000,icon='Temp'},
  ['Réputation - Ratchet +500']={t=1000,icon='Temp'},
  ['Résistance au Givre']={t=1000,icon='Spell_Frost_WizardMark'},
  ['Résistance à l\'Ombre']={t=1000,icon='Spell_Frost_WizardMark'},
  ['Résurrection']={t=10000,icon='Spell_Holy_Resurrection'},
  ['Résurrection de Gangretrombe']={t=3000,icon='Spell_Totem_WardOfDraining'},
  ['Résurrection de Pierre d\'âme']={t=3000,icon='Spell_Shadow_SoulGem'},
  ['Résurrection personnelle']={t=5000,icon='Temp'},
  ['Résurrection écarlate']={t=2000,icon='Spell_Holy_Resurrection'},
  ['Rétablissement']={t=2000,icon='Spell_Nature_ResistNature'},
  ['Réveil de Merithra']={t=2000,icon='Temp'},
  ['Réveille l\'Ecorcheur d\'âmes']={t=5000,icon='Temp'},
  ['Réveiller Kerlonian']={t=4500,icon='Temp'},
  ['Réveiller le Garde de la Banque']={t=5000,icon='Spell_Nature_Earthquake'},
  ['SORT DE QUETE DE CHU']={t=4000,icon='Spell_Shadow_LifeDrain'},
  ['S\'morc grillé']={t=1000,icon='INV_SummerFest_Smorc'},
  ['Sabre-de-brume rapide']={t=3000,icon='Ability_Mount_BlackPanther'},
  ['Sabre-de-givre']={t=3000,icon='Ability_Mount_WhiteTiger'},
  ['Sabre-de-givre de Berceau-de-l\'Hiver']={t=3000,icon='Ability_Mount_PinkTiger'},
  ['Sabre-de-givre rapide']={t=3000,icon='Ability_Mount_WhiteTiger'},
  ['Sabre-de-givre rayé']={t=3000,icon='Ability_Mount_WhiteTiger'},
  ['Sabre-de-givre tacheté']={t=3000,icon='Ability_Mount_WhiteTiger'},
  ['Sabre-de-l\'aube rapide']={t=3000,icon='Ability_Mount_JungleTiger'},
  ['Sabre-de-nuit']={t=3000,icon='Ability_Mount_BlackPanther'},
  ['Sabre-de-nuit rayé']={t=3000,icon='Ability_Mount_BlackPanther'},
  ['Sabre-tempête rapide']={t=3000,icon='Ability_Mount_BlackPanther'},
  ['Sacrifice']={t=1000,icon='Spell_Holy_DivineIntervention'},
  ['Sacrifier la filière']={t=10000,icon='Temp'},
  ['Sagesse des Winterax']={t=1000,icon='Ability_Ambush'},
  ['Salve']={t=3000,icon='Ability_TheBlackArrow'},
  ['Salve II']={t=3000,icon='Ability_TheBlackArrow'},
  ['Salve d\'éclairs de givre']={t=2000,icon='Spell_Frost_FrostBolt02'},
  ['Salve de Traits de l\'ombre']={t=3000,icon='Spell_Shadow_ShadowBolt'},
  ['Salve de guérison']={t=2500,icon='Spell_Nature_HealingWaveGreater'},
  ['Salve de pointes']={t=500,icon='Ability_ImpalingBolt'},
  ['Saoul - Effet Visuel d\'Incantation canalisée']={t=3000,icon='Spell_Nature_Cyclone'},
  ['Sapeur explosion']={t=5000,icon='Spell_Fire_SelfDestruct'},
  ['Sapphiron DND']={t=20000,icon='Temp'},
  ['Sarments']={t=1500,icon='Spell_Nature_StrangleVines'},
  ['Saucisse du solstice d\'été']={t=1000,icon='INV_Misc_Food_53'},
  ['Scarabées des cryptes']={t=1500,icon='Spell_Shadow_CarrionSwarm'},
  ['Sens amplifiés']={t=1000,icon='Temp'},
  ['Sensibilité au Feu']={t=5000,icon='INV_Misc_QirajiCrystal_02'},
  ['Sensibilité au Givre']={t=5000,icon='INV_Misc_QirajiCrystal_04'},
  ['Sensibilité aux Arcanes']={t=5000,icon='INV_Misc_QirajiCrystal_01'},
  ['Sensibilité à l\'Ombre']={t=5000,icon='INV_Misc_QirajiCrystal_05'},
  ['Sensibilité à la Nature']={t=5000,icon='INV_Misc_QirajiCrystal_03'},
  ['Serre d\'effroi']={t=1000,icon='Spell_Shadow_ShadowPact'},
  ['Servants de Malathrom']={t=1000,icon='Spell_Shadow_CorpseExplode'},
  ['Serviteur de Morganth']={t=2500,icon='Spell_Totem_WardOfDraining'},
  ['Silence']={t=1500,icon='Spell_Holy_Silence'},
  ['Siphon d\'énergie']={t=1500,icon='Spell_Shadow_Cripple'},
  ['Soin de Tharnariun']={t=500,icon='Spell_Nature_MagicImmunity'},
  ['Soins']={t=3000,icon='Spell_Holy_Heal02'},
  ['Soins de masse']={t=1000,icon='Spell_Holy_GreaterHeal'},
  ['Soins exceptionnels']={t=4000,icon='Spell_Holy_Heal'},
  ['Soins inférieurs']={t=2500,icon='Spell_Holy_LesserHeal'},
  ['Soins rapides']={t=1500,icon='Spell_Holy_FlashHeal'},
  ['Soins supérieurs']={t=3000,icon='Spell_Holy_GreaterHeal'},
  ['Sol H']={t=3000,icon='Spell_Frost_ManaRecharge'},
  ['Sol L']={t=3000,icon='Spell_Frost_ManaRecharge'},
  ['Sol M']={t=3000,icon='Spell_Frost_ManaRecharge'},
  ['Sol U']={t=3000,icon='Spell_Frost_ManaRecharge'},
  ['Sommeil']={t=1500,icon='Spell_Nature_Sleep'},
  ['Sort d\'apparition d\'Arugal']={t=2000,icon='Temp'},
  ['Sort d\'apparition du dragonnet de la colonie']={t=500,icon='Temp'},
  ['Sort offensif (leurre)']={t=2000,icon='Temp'},
  ['Souffle de Sargeras']={t=2000,icon='Spell_Shadow_Metamorphosis'},
  ['Souffle de flammes']={t=1700,icon='Spell_Fire_Fire'},
  ['Souffle de foudre']={t=2000,icon='Spell_Nature_Lightning'},
  ['Souffle de givre']={t=250,icon='Spell_Frost_FrostNova'},
  ['Souffle de sable']={t=2000,icon='Spell_Fire_WindsofWoe'},
  ['Souffle foudroyant']={t=3200,icon='Spell_Nature_Lightning'},
  ['Souffle glacial']={t=1000,icon='Spell_Frost_Wisp'},
  ['Stupidité']={t=1000,icon='Spell_Shadow_MindSteal'},
  ['Stupidité II']={t=1000,icon='Spell_Shadow_MindSteal'},
  ['Super cristal']={t=6000,icon='Temp'},
  ['Symbole de vie']={t=10000,icon='Spell_Holy_Resurrection'},
  ['Séduction']={t=1500,icon='Spell_Shadow_MindSteal'},
  ['Tablettes de lecture de Windsor DND']={t=10000,icon='Temp'},
  ['Tarte au sureau']={t=1000,icon='INV_Misc_Food_10'},
  ['Tempête de feu']={t=2000,icon='Spell_Fire_SelfDestruct'},
  ['Tempête de mana']={t=2000,icon='Spell_Frost_IceStorm'},
  ['Test Affûtage de lame']={t=3000,icon='Temp'},
  ['Tibia courbé']={t=1500,icon='Temp'},
  ['Tigre']={t=3000,icon='Ability_Mount_JungleTiger'},
  ['Tigre de guerre noir']={t=3000,icon='Ability_Mount_BlackPanther'},
  ['Tigre zulien rapide']={t=3000,icon='Ability_Mount_JungleTiger'},
  ['Tigre à dents de sabre doré']={t=3000,icon='Ability_Mount_JungleTiger'},
  ['Tigre à dents de sabre fauve']={t=3000,icon='Ability_Mount_JungleTiger'},
  ['Tir avec une arme à feu']={t=1000,icon='Ability_Marksmanship'},
  ['Tir de canon']={t=1000,icon='Spell_Fire_FireBolt02'},
  ['Tir distant II']={t=4000,icon='Ability_Marksmanship'},
  ['Tir distant III']={t=4000,icon='Ability_Marksmanship'},
  ['Tir du Canon des mers du sud']={t=5000,icon='Spell_Fire_FireBolt02'},
  ['Tir du canon']={t=2000,icon='Temp'},
  ['Tir empoisonné']={t=2000,icon='Ability_Poisons'},
  ['Tir à l\'arc']={t=1000,icon='Ability_Marksmanship'},
  ['Tir à l’arbalète']={t=1000,icon='Ability_Marksmanship'},
  ['Tirer un missile']={t=3000,icon='INV_Ammo_Bullet_03'},
  ['Tirer une roquette']={t=3000,icon='INV_Ammo_Bullet_03'},
  ['Tombeau de glace']={t=1500,icon='Spell_Frost_Glacier'},
  ['Torche d\'invocation de démon']={t=3000,icon='Temp'},
  ['Tortue de monte']={t=3000,icon='Ability_Hunter_Pet_Turtle'},
  ['Totem de Poignée de terre']={t=500,icon='Spell_Nature_NatureTouchDecay'},
  ['Totem de foudre']={t=500,icon='Spell_Nature_Lightning'},
  ['Toucher de Ravenclaw']={t=1500,icon='Spell_Shadow_Requiem'},
  ['Toucher de flétrissement']={t=2000,icon='Spell_Nature_Drowsy'},
  ['Toucher glacial de Trelane']={t=3000,icon='Spell_Shadow_UnsummonBuilding'},
  ['Toucher guérisseur']={t=3500,icon='Spell_Nature_HealingTouch'},
  ['Toucher mortel']={t=3000,icon='Spell_Shadow_UnsummonBuilding'},
  ['Toxine de Limace']={t=2000,icon='Spell_Nature_Regenerate'},
  ['Toxine instantanée']={t=3000,icon='INV_Potion_19'},
  ['Toxine mortelle']={t=3000,icon='Spell_Nature_CorrosiveBreath'},
  ['Traduction de Jarkal']={t=4500,icon='Spell_Holy_Restoration'},
  ['Trait de gelée']={t=2500,icon='Spell_Nature_CorrosiveBreath'},
  ['Trait de l\'ombre']={t=3000,icon='Spell_Shadow_ShadowBolt'},
  ['Trait de l\'ombre (échec)']={t=2000,icon='Spell_Shadow_ShadowBolt'},
  ['Trait de lézard']={t=2000,icon='Spell_Nature_Lightning'},
  ['Trait de magma']={t=1000,icon='Spell_Fire_FlameShock'},
  ['Transe divinatoire']={t=5000,icon='Temp'},
  ['Transfert d\'ombre']={t=500,icon='Spell_Shadow_DetectLesserInvisibility'},
  ['Transformation de Warosh']={t=1000,icon='Temp'},
  ['Transformation des Etudiants de la Chambre des visions - Effet']={t=1000,icon='Temp'},
  ['Transformation du dragon bleu DND']={t=1000,icon='Temp'},
  ['Transformation du dragon rouge DND']={t=1000,icon='Temp'},
  ['Transformation du dragon vert DND']={t=1000,icon='Temp'},
  ['Transi(e) d\'amour']={t=1000,icon='INV_Ammo_Arrow_02'},
  ['Transporteur (Long-guet)']={t=10000,icon='Spell_Fire_SelfDestruct'},
  ['Transporteur gnome']={t=10000,icon='Temp'},
  ['Traqueur du Syndicat (MURP) DND']={t=1000,icon='Spell_Arcane_Blink'},
  ['Tremblement psychique']={t=2000,icon='Spell_Nature_Earthquake'},
  ['Triage']={t=7000,icon='Temp'},
  ['Trotteur de bataille noir']={t=3000,icon='Ability_Mount_MechaStrider'},
  ['Trou de temps']={t=2000,icon='Spell_Arcane_PortalOrgrimmar'},
  ['Trousse de dessin']={t=7000,icon='Temp'},
  ['Trouver le Fragment de la relique']={t=5000,icon='Temp'},
  ['Téléportation']={t=1000,icon='Spell_Magic_LesserInvisibilty'},
  ['Téléportation : Darnassus']={t=10000,icon='Spell_Arcane_TeleportDarnassus'},
  ['Téléportation : Ironforge']={t=10000,icon='Spell_Arcane_TeleportIronForge'},
  ['Téléportation : Orgrimmar']={t=10000,icon='Spell_Arcane_TeleportOrgrimmar'},
  ['Téléportation : Reflet-de-Lune']={t=10000,icon='Spell_Arcane_TeleportMoonglade'},
  ['Téléportation : Stormwind']={t=10000,icon='Spell_Arcane_TeleportStormWind'},
  ['Téléportation : Thunder Bluff']={t=10000,icon='Spell_Arcane_TeleportThunderBluff'},
  ['Téléportation : Undercity']={t=10000,icon='Spell_Arcane_TeleportUnderCity'},
  ['Téléportation Autel des Marées']={t=2000,icon='Temp'},
  ['Téléportation au Monastère']={t=2000,icon='Temp'},
  ['Téléportation au bois de la Pénombre']={t=2000,icon='Temp'},
  ['Téléportation au cimetière']={t=2000,icon='Temp'},
  ['Téléportation au phare']={t=2000,icon='Temp'},
  ['Téléportation au tréant']={t=2000,icon='Temp'},
  ['Téléportation à Anvilmar']={t=2000,icon='Temp'},
  ['Téléportation à Ashcrombe']={t=2000,icon='Spell_Fire_SelfDestruct'},
  ['Téléportation à Darkshire']={t=2000,icon='Temp'},
  ['Téléportation à Darnassus - Evénément']={t=1000,icon='Temp'},
  ['Téléportation à Elwynn']={t=2000,icon='Temp'},
  ['Téléportation à Goldshire']={t=2000,icon='Temp'},
  ['Téléportation à Ruisselune']={t=2000,icon='Temp'},
  ['Téléportation à l\'abbaye de Northshire']={t=2000,icon='Temp'},
  ['Téléportation à la caserne']={t=2000,icon='Temp'},
  ['Téléportation à la marche de l\'Ouest']={t=2000,icon='Temp'},
  ['Téléportation à partir de la Tour d\'Azshara']={t=1000,icon='Spell_Nature_EarthBind'},
  ['Téléportation simple']={t=1000,icon='Spell_Magic_LesserInvisibilty'},
  ['Téléportation simple (autre)']={t=1000,icon='Spell_Magic_LesserInvisibilty'},
  ['Téléportation simple (groupe)']={t=2000,icon='Spell_Magic_LesserInvisibilty'},
  ['Téléporte à la Tour d\'Azshara']={t=1000,icon='Spell_Nature_AstralRecalGroup'},
  ['Uldaman Sub-Boss Agro']={t=5000,icon='Spell_Nature_EarthBindTotem'},
  ['Urok invoqué']={t=1000,icon='Temp'},
  ['Utiliser le Colifichet']={t=3000,icon='Temp'},
  ['Vague de soins']={t=3000,icon='Spell_Nature_MagicImmunity'},
  ['Vague de soins d\'Antu\'sul']={t=1000,icon='Spell_Holy_Heal02'},
  ['Vague de soins inférieurs']={t=1500,icon='Spell_Nature_HealingWaveLesser'},
  ['Varicelle de silithide']={t=2000,icon='Spell_Nature_NullifyDisease'},
  ['Vaudou']={t=1000,icon='Spell_Shadow_AntiShadow'},
  ['Vengeance d\'Arygos']={t=2000,icon='Temp'},
  ['Venin de rampant']={t=2000,icon='Spell_Nature_NullifyPoison'},
  ['Vents brûlants']={t=1000,icon='Spell_Nature_Cyclone'},
  ['Ver mangeur de chair']={t=5000,icon='INV_Misc_Orb_03'},
  ['Verroterie']={t=5000,icon='INV_Misc_Orb_03'},
  ['Verroterie brillante']={t=5000,icon='INV_Misc_Orb_03'},
  ['Victime de la transformation']={t=2000,icon='Spell_Magic_LesserInvisibilty'},
  ['Vision assombrie']={t=2000,icon='Spell_Shadow_Fumble'},
  ['Vision de mage']={t=3000,icon='Temp'},
  ['Visuel Soins (DND)']={t=3500,icon='Spell_Holy_Heal'},
  ['Visuel d\'arrêt du temps DND']={t=3000,icon='Temp'},
  ['Visuel d\'éveil d\'Archaedas (DND)']={t=1500,icon='Spell_Nature_Earthquake'},
  ['Visuel de Submerger']={t=1500,icon='Spell_Fire_Volcano'},
  ['Visuel de Submerger Ouro']={t=1500,icon='Spell_Fire_Volcano'},
  ['Visuel de création d\'objet (DND)']={t=5000,icon='Spell_Shadow_SoulGem'},
  ['Visuel de réveil du nain de pierre']={t=1500,icon='Spell_Nature_Earthquake'},
  ['Visuel de téléportation d\'Ivus DND']={t=1000,icon='Temp'},
  ['Visuel de téléportation du Majordome']={t=1000,icon='Spell_Arcane_Blink'},
  ['Visée']={t=3000,icon='INV_Spear_07'},
  ['Voile de l\'ombre']={t=1500,icon='Spell_Shadow_GatherShadows'},
  ['Vol d\'esprit']={t=2000,icon='Spell_Shadow_Possession'},
  ['Vol de vie']={t=1500,icon='Spell_Shadow_LifeDrain02'},
  ['Vol à la tire (PT)']={t=5000,icon='Temp'},
  ['Volonté de Shahram']={t=1000,icon='Spell_Holy_MindVision'},
  ['Volée de Boules de feu']={t=3000,icon='Spell_Fire_FlameBolt'},
  ['Vortex de mana']={t=2000,icon='Spell_Shadow_DarkRitual'},
  ['[PH] Activateur de l\'arc-boutant']={t=5000,icon='Temp'},
  ['[PH] Bazooka à Cristal']={t=1000,icon='Temp'},
  ['[PH] Téléportation au port de Menethil']={t=2000,icon='Temp'},
  ['[PH] Téléportation à Auberdine']={t=2000,icon='Temp'},
  ['[PH] Téléportation à Baie-du-Butin']={t=2000,icon='Temp'},
  ['[PH] Téléportation à Balthule']={t=2000,icon='Temp'},
  ['[PH] Téléportation à Gangrebois']={t=2000,icon='Temp'},
  ['[PH] Téléportation à Grom\'gol']={t=2000,icon='Temp'},
  ['[PH] Téléportation à Orgrimmar']={t=2000,icon='Temp'},
  ['[PH] Téléportation à Ratchet']={t=2000,icon='Temp'},
  ['[PH] Téléportation à Theramore']={t=2000,icon='Temp'},
  ['[PH] Téléportation à Undercity']={t=2000,icon='Temp'},
  ['Œil d\'Immol\'thar']={t=2000,icon='Spell_Shadow_AntiMagicShell'},
}

pfUI_locale["frFR"]["debuffs"] = {
  ['AE Charme']={[0]=300.0,},
  ['Accomplissement véritable']={[0]=20.0,},
  ['Acide corrosif']={[0]=300.0,},
  ['Acide d\'Hakkar']={[0]=60.0,},
  ['Acide de Fouisseur']={[0]=30.0,},
  ['Acide de tunnelier']={[0]=30.0,},
  ['Affaiblir']={[0]=120.0,},
  ['Affliction de l\'espèce : bleu']={[0]=600.0,},
  ['Affliction de l\'espèce : bronze']={[0]=600.0,},
  ['Affliction de l\'espèce : noir']={[0]=600.0,},
  ['Affliction de l\'espèce : rouge']={[0]=600.0,},
  ['Affliction de l\'espèce : vert']={[0]=600.0,},
  ['Agilité VIII']={[0]=3600.0,},
  ['Agilité altérée']={[0]=4.0,},
  ['Agilité diminuée']={[0]=300.0,},
  ['Agressivité nulle']={[0]=1.7,},
  ['Aiguillon de diablosaure']={[0]=10.0,},
  ['Aiguillon perfide']={[1]=0,[2]=1.0,},
  ['Ailes du désespoir']={[0]=6.0,},
  ['Alambic ectoplasmique']={[0]=3.0,},
  ['Amollir']={[0]=10.0,},
  ['Amplification des dégâts']={[0]=10.0,},
  ['Amplifier flammes']={[0]=30.0,},
  ['Annuler']={[0]=8.0,},
  ['Anti-triche chasseur Epique DND']={[0]=60.0,},
  ['Apaisement']={[0]=15.0,},
  ['Apaiser les animaux']={[0]=15.0,},
  ['Aperçu de la folie']={[0]=3.0,},
  ['Appel de la tombe']={[0]=60.0,},
  ['Apprivoise un Clampant de l\'écume']={[0]=900.0,},
  ['Apprivoise un Grand sanglier des rochers']={[0]=20.0,},
  ['Apprivoise un Hurleur Strigid']={[0]=900.0,},
  ['Apprivoise un Ours Griffeglace']={[0]=20.0,},
  ['Apprivoise un Rôdeur sabre-de-nuit']={[0]=900.0,},
  ['Apprivoise un Sanglier tacheté redoutable']={[0]=900.0,},
  ['Apprivoise un Traqueur des prairies']={[0]=900.0,},
  ['Apprivoise un léopard des neiges']={[0]=900.0,},
  ['Apprivoise un rapace']={[0]=900.0,},
  ['Apprivoise un scorpide blindé']={[0]=900.0,},
  ['Apprivoise un trotteur des plaines adulte']={[0]=900.0,},
  ['Apprivoise une Rôdeuse Tissebois']={[0]=900.0,},
  ['Apprivoise une bête']={[0]=20.0,},
  ['Armure IV']={[0]=3600.0,},
  ['Armure de puissance']={[0]=6.0,},
  ['Arrachevent']={[0]=20.0,},
  ['Aspect d\'Arlokk']={[0]=2.0,},
  ['Aspect de Jeklik']={[0]=5.0,},
  ['Aspect de Mar\'li']={[0]=6.0,},
  ['Aspect de Venoxis']={[0]=10.0,},
  ['Assaut sauvage']={[0]=30.0,},
  ['Assaut sauvage II']={[0]=30.0,},
  ['Assaut sauvage III']={[0]=30.0,},
  ['Assaut sauvage IV']={[0]=30.0,},
  ['Assaut sauvage V']={[0]=30.0,},
  ['Asservir']={[0]=15.0,},
  ['Asservir démon']={[0]=300.0,},
  ['Asservir un démon']={[0]=300.0,},
  ['Assommer']={[1]=25.0,[2]=35.0,[3]=45.0,[0]=45.0,},
  ['Attaque Arme de givre']={[0]=8.0,},
  ['Attaque de givre']={[0]=8.0,},
  ['Attaque surprise']={[0]=2.5,},
  ['Attirer esprit']={[0]=5.0,},
  ['Attirer un Rageur']={[0]=2.0,},
  ['Attraper une arme']={[0]=15.0,},
  ['Aura d\'Anub\'Rekhan']={[0]=5.0,},
  ['Aura d\'Emeriss']={[0]=10.0,},
  ['Aura d\'agonie']={[0]=8.0,},
  ['Aura de bataille']={[0]=10.0,},
  ['Aura de commandement']={[0]=30.0,},
  ['Aura de givre']={[0]=5.0,},
  ['Aura de la chandelle du rituel']={[0]=6.0,},
  ['Aura de peur']={[0]=3.0,},
  ['Aura de poison']={[0]=12.0,},
  ['Aura de sangsue (PT)']={[0]=15.0,},
  ['Aura de souillure']={[0]=5.0,},
  ['Aura du Seigneur sanglant']={[0]=5.0,},
  ['Aveuglement']={[0]=3.0,},
  ['Aveugleur']={[0]=12.0,},
  ['Baiser de l\'araignée']={[0]=10.0,},
  ['Balise de la faille']={[0]=60.0,},
  ['Bannir']={[1]=20.0,[2]=30.0,[0]=30.0,},
  ['Bave de Ver']={[0]=6.0,},
  ['Bave toxique']={[0]=120.0,},
  ['Berceuse enchantée']={[0]=10.0,},
  ['Berserk']={[0]=30.0,},
  ['Bile putride']={[0]=45.0,},
  ['Blessure']={[0]=25.0,},
  ['Blessure infectée']={[0]=300.0,},
  ['Blessure mortelle']={[0]=8.0,},
  ['Blessure profonde']={[0]=12.0,},
  ['Bombe fumigène']={[0]=4.0,},
  ['Bombe vivante']={[0]=8.0,},
  ['Bonne fortune de la Fête !']={[0]=1800.0,},
  ['Bonne fortune lunaire']={[0]=1800.0,},
  ['Bouclier de dégâts']={[0]=10.0,},
  ['Bouclier réflecteur']={[0]=5.0,},
  ['Boue projetée']={[0]=15.0,},
  ['Bouffée de maladie']={[0]=20.0,},
  ['Boule de feu']={[1]=4.0,[2]=6.0,[3]=6.0,[4]=8.0,[5]=8.0,[6]=8.0,[7]=8.0,[8]=8.0,[9]=8.0,[10]=8.0,[11]=8.0,[12]=8.0,[0]=8.0,},
  ['Bourbier']={[0]=5.0,},
  ['Bourrasque']={[0]=4.0,},
  ['Boutoir']={[0]=1.0,},
  ['Brasier']={[0]=30.0,},
  ['Brise-genou']={[0]=15.0,},
  ['Brise-genou amélioré']={[0]=5.0,},
  ['Brise-âme']={[0]=30.0,},
  ['Briser l\'armure']={[0]=45.0,},
  ['Briser l\'esprit']={[0]=3.0,},
  ['Brouillage']={[0]=15.0,},
  ['Broyeur de Malown']={[0]=2.0,},
  ['Brume pestiférée']={[0]=8.0,},
  ['Brûlure']={[0]=4.0,},
  ['Brûlure d\'âme']={[0]=16.0,},
  ['Brûlure de givre']={[0]=15.0,},
  ['Brûlure de l\'ombre']={[0]=5.0,},
  ['Brûlure de mana']={[0]=8.0,},
  ['Bulles d\'air']={[0]=10.0,},
  ['Bénédiction de Nordrassil']={[0]=10.0,},
  ['Bénédiction de la Marche noire']={[0]=6.0,},
  ['Bénédiction du chef de guerre']={[0]=3600.0,},
  ['Cadavre véreux']={[0]=600.0,},
  ['Capturer l\'esprit d\'un gangrechien']={[0]=9.0,},
  ['Capturer un Esprit']={[0]=9.0,},
  ['Capturer un Esprit d\'Infernal']={[0]=9.0,},
  ['Capturer un tréant']={[0]=5.0,},
  ['Carapace infernale']={[0]=10.0,},
  ['Catalyseur de Vekniss']={[0]=30.0,},
  ['Catalyseur de la Ruche\'Zara']={[0]=30.0,},
  ['Catalyseur nauséabond']={[0]=120.0,},
  ['Cauchemar de Naralex']={[0]=15.0,},
  ['Chaleur d\'affliction']={[0]=900.0,},
  ['Champignon d\'Aileron boueux']={[0]=8.0,},
  ['Champignon de Mirkfallon']={[0]=2700.0,},
  ['Chants de manifestation']={[0]=600.0,},
  ['Charge']={[0]=4.0,},
  ['Charge débilitante']={[0]=8.0,},
  ['Charge empoisonnée']={[0]=9.0,},
  ['Charge furieuse']={[0]=30.0,},
  ['Charge étourdissante']={[0]=1.0,},
  ['Charme des flots']={[0]=3.0,},
  ['Chaînes de Kel\'Thuzad']={[0]=20.0,},
  ['Chaînes de glace']={[1]=15.0,[2]=20.0,[0]=20.0,},
  ['Choc martial']={[0]=2.0,},
  ['Choc terrestre']={[0]=5.0,},
  ['Châtier le démon']={[0]=5.0,},
  ['Coiffe de contrôle mental gnome']={[0]=20.0,},
  ['Complainte de la Banshee']={[0]=12.0,},
  ['Compétence des Réprouvés']={[0]=300.0,},
  ['Conduit statique']={[0]=15.0,},
  ['Connexion d\'âme']={[0]=12.0,},
  ['Consumer']={[0]=15.0,},
  ['Conséquences']={[0]=5.0,},
  ['Contagion pourrissante']={[0]=240.0,},
  ['Contagion toxique']={[0]=60.0,},
  ['Contre-attaque']={[0]=5.0,},
  ['Contrecoup de métamorphose']={[0]=8.0,},
  ['Contresort']={[0]=10.0,},
  ['Contresort - Silencieux']={[0]=4.0,},
  ['Contrôle mental']={[0]=60.0,},
  ['Corruption']={[1]=12.0,[2]=15.0,[3]=18.0,[4]=18.0,[5]=18.0,[6]=18.0,[7]=18.0,[0]=18.0,},
  ['Corruption de l\'âme']={[0]=15.0,},
  ['Corruption de la terre']={[0]=10.0,},
  ['Corruptrice']={[0]=60.0,},
  ['Coup bas']={[0]=4.0,},
  ['Coup de Tonnerre']={[0]=5.0,},
  ['Coup de bouclier']={[0]=6.0,},
  ['Coup de bouclier - silencieux']={[0]=3.0,},
  ['Coup de pied']={[0]=5.0,},
  ['Coup de pied - Silencieux']={[0]=2.0,},
  ['Coup de pied rapide']={[0]=2.0,},
  ['Coup de tonnerre']={[1]=10.0,[2]=14.0,[3]=18.0,[4]=22.0,[5]=26.0,[6]=30.0,[0]=30.0,},
  ['Coup de tête']={[0]=2.0,},
  ['Coup fané']={[0]=8.0,},
  ['Coup railleur']={[0]=6.0,},
  ['Coup tordu']={[0]=3.0,},
  ['Coup traumatisant']={[0]=5.0,},
  ['Coup étourdissant']={[1]=5.0,[2]=8.0,[0]=8.0,},
  ['Coupure d\'ailes']={[0]=10.0,},
  ['Coupure d\'ailes améliorée']={[0]=5.0,},
  ['Courroux du porte-peste']={[0]=10.0,},
  ['Courroux naturel']={[0]=12.0,},
  ['Crachat acide']={[0]=30.0,},
  ['Crachat d\'Acide corrosif']={[0]=10.0,},
  ['Crachat d\'abomination']={[0]=10.0,},
  ['Crachat de diversion']={[0]=15.0,},
  ['Crachat de magma']={[0]=30.0,},
  ['Crachat de venin corrosif']={[0]=10.0,},
  ['Crachat empoisonné']={[0]=15.0,},
  ['Crachat infectieux']={[0]=10.0,},
  ['Crachat venimeux']={[0]=10.0,},
  ['Cravache d\'esclavagiste']={[0]=30.0,},
  ['Cri de défi']={[0]=6.0,},
  ['Cri de guerre']={[0]=900.0,},
  ['Cri de ralliement du tueur de dragon']={[0]=7200.0,},
  ['Cri démoralisant']={[0]=30.0,},
  ['Cri d’intimidation']={[0]=8.0,},
  ['Cri effrayant']={[0]=6.0,},
  ['Cri incapacitant']={[0]=60.0,},
  ['Cri perçant']={[0]=6.0,},
  ['Cri psychique']={[0]=8.0,},
  ['Cris du passé']={[0]=5.0,},
  ['Cristal de réduction']={[0]=120.0,},
  ['Croc de l\'araignée de cristal']={[0]=10.0,},
  ['Croc-en-jambe']={[0]=3.0,},
  ['Croc-en-jambe circulaire']={[0]=2.0,},
  ['Création de l\'explosion du Cœur d\'Hakkar']={[0]=30.0,},
  ['Création de la faille du Cœur d\'Hakkar']={[0]=30.0,},
  ['Créature de cauchemar']={[0]=30.0,},
  ['Créer Cercle d\'invocation du cœur d\'Hakkar']={[0]=30.0,},
  ['Créer l\'Aura de Zul']={[0]=30.0,},
  ['Cécité']={[0]=10.0,},
  ['Dard acéré']={[0]=300.0,},
  ['Dard empoisonné']={[0]=15.0,},
  ['Dard venimeux']={[0]=45.0,},
  ['Discombobuler']={[0]=12.0,},
  ['Disjonction']={[0]=300.0,},
  ['Disparition']={[0]=20.0,},
  ['Dissoudre l\'armure']={[0]=20.0,},
  ['Domination']={[0]=15.0,},
  ['Domination mentale']={[0]=120.0,},
  ['Don d\'Arthas']={[0]=180.0,},
  ['Don d’Arugal']={[0]=300.0,},
  ['Douleur accablante']={[0]=15.0,},
  ['Douleur déchirante']={[0]=15.0,},
  ['Douleur paralysante']={[0]=10.0,},
  ['Douleurs insupportables']={[0]=180.0,},
  ['Drain de mana']={[0]=5.0,},
  ['Drain de vie']={[0]=5.0,},
  ['Drain d’âme']={[0]=10.0,},
  ['Dysenterie des Gelées']={[0]=1800.0,},
  ['Débiliter']={[0]=15.0,},
  ['Débiter']={[0]=6.0,},
  ['Déchireur de Gutgore']={[0]=30.0,},
  ['Déchirure']={[0]=12.0,},
  ['Déchirure du tendon']={[0]=8.0,},
  ['Déchirure musculaire']={[0]=5.0,},
  ['Déchirure test']={[0]=12.0,},
  ['Décimer']={[0]=3.0,},
  ['Décrépitude spirituelle']={[0]=1200.0,},
  ['Déflagration']={[0]=10.0,},
  ['Déflagration sacrée']={[0]=4.0,},
  ['Dégénérescence mentale']={[0]=30.0,},
  ['Déluge de haches']={[0]=2.0,},
  ['Démangeaison']={[0]=8.0,},
  ['Démembrer']={[0]=10.0,},
  ['Démoralisation']={[0]=30.0,},
  ['Désarmement']={[0]=10.0,},
  ['Désespoir ancestral']={[0]=5.0,},
  ['Détection de l\'invisibilité supérieure']={[0]=600.0,},
  ['Détection de la magie']={[0]=120.0,},
  ['Echo de rugissement']={[0]=20.0,},
  ['Eclair de Vide']={[0]=10.0,},
  ['Eclair de givre']={[1]=5.0,[2]=6.0,[3]=6.0,[4]=7.0,[5]=7.0,[6]=8.0,[7]=8.0,[8]=9.0,[9]=9.0,[10]=9.0,[11]=9.0,[0]=9.0,},
  ['Eclair de glace']={[0]=2.0,},
  ['Eclair de poison']={[0]=10.0,},
  ['Eclair de tempête']={[0]=5.0,},
  ['Eclat lunaire']={[1]=9.0,[2]=12.0,[3]=12.0,[4]=12.0,[5]=12.0,[6]=12.0,[7]=12.0,[8]=12.0,[9]=12.0,[10]=12.0,[0]=12.0,},
  ['Eclats stellaires']={[0]=6.0,},
  ['Ecraser armure']={[0]=30.0,},
  ['Effacer toutes les entraves']={[0]=1.0,},
  ['Effet Apparition Leurre']={[0]=5.0,},
  ['Effet Apparition Leurre perfectionné']={[0]=10.0,},
  ['Effet Garde de Laze']={[0]=3.0,},
  ['Effet Harcèlement']={[0]=3.0,},
  ['Effet Piège explosif']={[0]=20.0,},
  ['Effet Piège givrant']={[1]=10.0,[2]=15.0,[3]=20.0,[0]=20.0,},
  ['Effet Piège immolation']={[0]=15.0,},
  ['Effet Plaie ouverte']={[0]=8.0,},
  ['Effet Sloth']={[0]=3.0,},
  ['Effet Venin de scorpion mineur']={[0]=60.0,},
  ['Effet de Charge farouche']={[0]=4.0,},
  ['Effet de Corruption sanguine']={[0]=10.0,},
  ['Effet de Provocation du Mannequin de Gizlock']={[0]=5.0,},
  ['Effet de Stase']={[0]=15.0,},
  ['Effet de souffrance de Taelan']={[0]=2.0,},
  ['Effet inferno']={[0]=2.0,},
  ['Effet étourdissant de la masse']={[0]=3.0,},
  ['Effrayer une bête']={[1]=10.0,[2]=15.0,[3]=20.0,[0]=20.0,},
  ['Effroi de la Sanssaint']={[0]=6.0,},
  ['Elixir des arcanes supérieur']={[0]=1800.0,},
  ['Elixir des géants']={[0]=1200.0,},
  ['Elémentaire de feu']={[0]=8.0,},
  ['Empaler']={[0]=9.0,},
  ['Empire des âmes']={[0]=60.0,},
  ['Empoisonnement par radiations']={[0]=25.0,},
  ['Emprise']={[0]=10.0,},
  ['Emprise de Crispechardon']={[0]=8.0,},
  ['Enchaînement mortel']={[0]=5.0,},
  ['Enchevêtrement']={[0]=8.0,},
  ['Encorner']={[0]=15.0,},
  ['Encouragement']={[0]=1800.0,},
  ['Endurance altérée']={[0]=4.0,},
  ['Energie noire']={[0]=300.0,},
  ['Enflammer']={[0]=4.0,},
  ['Enflammer la chair']={[0]=60.0,},
  ['Enflammer le mana']={[0]=300.0,},
  ['Entaille Traqueur de l\'ombre']={[0]=5.0,},
  ['Entraves de magma']={[0]=15.0,},
  ['Entraves des morts-vivants']={[1]=30.0,[2]=40.0,[3]=50.0,[0]=50.0,},
  ['Enzyme putride']={[0]=300.0,},
  ['Epine infectieuse']={[0]=300.0,},
  ['Epines']={[0]=600.0,},
  ['Epouvante']={[0]=5.0,},
  ['Esprit corrompu']={[0]=600.0,},
  ['Esprit de Lunegriffe']={[0]=120.0,},
  ['Esprit des Zandalar']={[0]=7200.0,},
  ['Esprit divin']={[0]=1800.0,},
  ['Esprit pestiféré']={[0]=600.0,},
  ['Esprits hanteurs']={[0]=300.0,},
  ['Essaim d\'insectes']={[0]=12.0,},
  ['Essence du Rouge']={[0]=180.0,},
  ['Etendard de bataille']={[0]=3.0,},
  ['Etouffement']={[0]=300.0,},
  ['Etouffer']={[0]=6.0,},
  ['Etourdir']={[0]=2.0,},
  ['Etourdissement de la Transformation de Balnazzar']={[0]=5.0,},
  ['Etourdissement vengeur']={[0]=3.0,},
  ['Etreinte de glace']={[0]=5.0,},
  ['Etreinte de la veuve']={[0]=30.0,},
  ['Etreinte stygienne']={[0]=5.0,},
  ['Etreinte vampirique']={[0]=60.0,},
  ['Expiation']={[0]=2.0,},
  ['Exploser']={[0]=2.5,},
  ['Explosion de gelée']={[0]=5.0,},
  ['Explosion de l\'insecte']={[0]=4.0,},
  ['Explosion de l\'œuf']={[0]=3.0,},
  ['Explosion de rets']={[0]=10.0,},
  ['Explosion de tonneau']={[0]=3.0,},
  ['Explosion paralysante']={[1]=3.0,[2]=5.0,[0]=5.0,},
  ['Explosion pyrotechnique']={[0]=12.0,},
  ['Explosion sonore']={[0]=10.0,},
  ['Exposer l\'armure']={[0]=30.0,},
  ['Extraction d\'essence']={[0]=12.0,},
  ['Faiblesse']={[0]=20.0,},
  ['Faire détoner mana']={[0]=5.0,},
  ['Fantômes hanteurs']={[0]=300.0,},
  ['Fatigue fièvreuse']={[0]=1800.0,},
  ['Faucher la foule']={[0]=5.0,},
  ['Faux Jeff 1']={[0]=30.0,},
  ['Faux Jeff 2']={[0]=30.0,},
  ['Fendoir vicieux']={[0]=15.0,},
  ['Fendre armure']={[0]=15.0,},
  ['Feu douillet']={[0]=60.0,},
  ['Feu stellaire étourdissant']={[0]=3.0,},
  ['Feu vertueux']={[0]=8.0,},
  ['Filet']={[0]=10.0,},
  ['Filet lesté']={[0]=10.0,},
  ['Filet électrifié']={[0]=10.0,},
  ['Fiole de poison']={[0]=30.0,},
  ['Fixer']={[0]=10.0,},
  ['Fièvre de décrépitude']={[0]=21.0,},
  ['Fièvre torride']={[0]=600.0,},
  ['Flamboyante de Stratholme']={[0]=30.0,},
  ['Flamme d\'ombre']={[0]=10.0,},
  ['Flammes de cautérisation']={[0]=900.0,},
  ['Flammes incendiaires']={[0]=9.0,},
  ['Flammes sacrées']={[0]=10.0,},
  ['Floraison fongique']={[0]=90.0,},
  ['Flot de gelée']={[0]=3.0,},
  ['Flèche d\'infection']={[0]=300.0,},
  ['Flèche de dispersion']={[0]=4.0,},
  ['Flèche de givre']={[0]=10.0,},
  ['Flèche noire']={[0]=30.0,},
  ['Flétrissement']={[0]=21.0,},
  ['Fondre armure']={[0]=60.0,},
  ['Fonte de minerai']={[0]=20.0,},
  ['Fonte des os']={[0]=20.0,},
  ['Force altérée']={[0]=4.0,},
  ['Force diminuée']={[0]=300.0,},
  ['Fouet']={[0]=2.0,},
  ['Fouet de flammes']={[0]=45.0,},
  ['Fouet mental']={[0]=3.0,},
  ['Fouette-bile']={[0]=20.0,},
  ['Fouette-queue']={[0]=2.0,},
  ['Fourche démoniaque']={[0]=25.0,},
  ['Fracas de vagues']={[0]=10.0,},
  ['Fracas du tonnerre']={[0]=2.5,},
  ['Fracas sacré']={[0]=60.0,},
  ['Fracasse-tête']={[0]=2.0,},
  ['Fracasser armure']={[0]=30.0,},
  ['Fragilité']={[0]=60.0,},
  ['Frappe de Rhahk\'Zor']={[0]=3.0,},
  ['Frappe déséquilibrante']={[0]=6.0,},
  ['Frappe fantomatique']={[0]=7.0,},
  ['Frappe fantôme']={[0]=20.0,},
  ['Frappe mortelle']={[0]=10.0,},
  ['Frappe étourdissante']={[0]=4.0,},
  ['Froid hivernal']={[0]=15.0,},
  ['Froid épouvantable']={[0]=120.0,},
  ['Fureur de la fête du feu']={[0]=3600.0,},
  ['Furie sanguinaire']={[0]=15.0,},
  ['Garde du filet']={[0]=20.0,},
  ['Garrot']={[0]=18.0,},
  ['Gel']={[0]=15.0,},
  ['Gel III (proc)']={[0]=5.0,},
  ['Gel instantané']={[0]=5.0,},
  ['Geler sur place']={[0]=10.0,},
  ['Gelée acide']={[0]=30.0,},
  ['Gelée putride']={[0]=120.0,},
  ['Gelée urticante']={[0]=1800.0,},
  ['Geyser']={[0]=5.0,},
  ['Givre']={[0]=10.0,},
  ['Glacial']={[0]=10.0,},
  ['Goudron poisseux']={[0]=4.0,},
  ['Grand enchaînement']={[0]=10.0,},
  ['Griffe d\'aigle']={[0]=15.0,},
  ['Griffe glaciale']={[0]=5.0,},
  ['Griffeglace']={[0]=6.0,},
  ['Griffes d\'Eskhandar']={[0]=30.0,},
  ['Griffure']={[0]=9.0,},
  ['Grognement d\'intimidation']={[0]=30.0,},
  ['Grondement']={[0]=3.0,},
  ['Grondement d’intimidation']={[0]=5.0,},
  ['Grondement menaçant']={[0]=30.0,},
  ['Grondement redoutable']={[0]=15.0,},
  ['Grondement ébranleur']={[0]=5.0,},
  ['Gueule enragée']={[0]=30.0,},
  ['Harcèlement']={[1]=10.0,[2]=20.0,[3]=30.0,[0]=30.0,},
  ['Harpon empoisonné']={[0]=60.0,},
  ['Heurt de bouclier']={[0]=2.0,},
  ['Heurtoir']={[0]=2.0,},
  ['Hibernation']={[1]=20.0,[2]=30.0,[3]=40.0,[0]=40.0,},
  ['Horion de flammes']={[0]=12.0,},
  ['Horion de givre']={[0]=8.0,},
  ['Horion de terre']={[0]=2.0,},
  ['Horion sonore']={[0]=300.0,},
  ['Humus rampant']={[0]=60.0,},
  ['Hurlement']={[0]=4.0,},
  ['Hurlement assourdissant']={[0]=8.0,},
  ['Hurlement de Cinglenuit']={[0]=15.0,},
  ['Hurlement de la Banshee']={[0]=5.0,},
  ['Hurlement de terreur']={[1]=10.0,[2]=15.0,[0]=15.0,},
  ['Hurlement perçant']={[0]=6.0,},
  ['Hurlement sanguinaire']={[0]=15.0,},
  ['Hurlement terrifiant']={[0]=4.0,},
  ['Hystérie ancienne']={[0]=900.0,},
  ['Hébétement']={[0]=4.0,},
  ['Hébétement prolongé']={[0]=6.0,},
  ['Hémorragie']={[0]=15.0,},
  ['Illusions de Jin\'do']={[0]=20.0,},
  ['Immolation']={[0]=15.0,},
  ['Immunité']={[0]=1800.0,},
  ['Impact']={[0]=2.0,},
  ['Incandescente']={[0]=30.0,},
  ['Incinérer']={[0]=60.0,},
  ['Inciter les Flammes']={[0]=60.0,},
  ['Infection de Crapaud-bile']={[0]=180.0,},
  ['Infection volatile']={[0]=120.0,},
  ['Injection mutante']={[0]=10.0,},
  ['Inquisition']={[0]=30.0,},
  ['Instinct de survie']={[0]=2.0,},
  ['Intelligence IX']={[0]=3600.0,},
  ['Intelligence altérée']={[0]=4.0,},
  ['Intelligence des arcanes']={[0]=1800.0,},
  ['Interception étourdissante']={[0]=3.0,},
  ['Interrompre (PT)']={[0]=30.0,},
  ['Intimidation']={[0]=3.0,},
  ['Invocation Divers DND']={[0]=600.0,},
  ['Invocation d\'Isalien DND']={[0]=600.0,},
  ['Invocation d\'un chevalier']={[0]=180.0,},
  ['Invocation d\'un chevalier monté']={[0]=120.0,},
  ['Invocation d\'un esprit de Darrowshire']={[0]=60.0,},
  ['Invocation d\'un esprit de hurleur']={[0]=120.0,},
  ['Invocation d\'une jeune recrue']={[0]=230.0,},
  ['Invocation d\'une vigne guérie de Celebrian']={[0]=604800.0,},
  ['Invocation d\'une âme libérée']={[0]=60.0,},
  ['Invocation de Jarien et Sothos DND']={[0]=600.0,},
  ['Invocation de Kormok DND']={[0]=600.0,},
  ['Invocation de Marduk le Noir']={[0]=300.0,},
  ['Invocation de Mor Grayhoof DND']={[0]=600.0,},
  ['Invocation de Thelrin DND']={[0]=600.0,},
  ['Invocation du générateur de Finkle']={[0]=5.0,},
  ['Invocation du seigneur Valthalak DND']={[0]=600.0,},
  ['Irradié']={[0]=60.0,},
  ['Jaillissement des arcanes']={[0]=8.0,},
  ['Jet d\'encre']={[0]=15.0,},
  ['Jet de rets']={[0]=10.0,},
  ['Jet de vapeur']={[0]=10.0,},
  ['Jet d’acide']={[0]=30.0,},
  ['Jugement de justice']={[0]=10.0,},
  ['Jugement de lumière']={[0]=10.0,},
  ['Jugement de sagesse']={[0]=10.0,},
  ['Jugement du Croisé']={[0]=10.0,},
  ['Justice du généralissime']={[0]=5.0,},
  ['Justification']={[0]=10.0,},
  ['La confiance de Golemagg']={[0]=2.0,},
  ['Lacérations']={[0]=60.0,},
  ['Lacérer']={[0]=8.0,},
  ['Lambeau']={[0]=12.0,},
  ['Lame des lamentations']={[0]=30.0,},
  ['Lame maudite']={[0]=20.0,},
  ['Lame-tonnerre']={[0]=12.0,},
  ['Lance-filet automatique']={[0]=10.0,},
  ['Lancement de torche']={[0]=30.0,},
  ['Lancer de Hache']={[0]=2.0,},
  ['Lancer de hache']={[0]=3.0,},
  ['Larve de Gelée']={[0]=6.0,},
  ['Lenteur']={[1]=10.0,[2]=15.0,[0]=15.0,},
  ['Lianes avides']={[0]=10.0,},
  ['Lien à la terre']={[0]=5.0,},
  ['Liens partagés']={[0]=4.0,},
  ['Limace des ténèbres']={[0]=300.0,},
  ['Limace noire']={[0]=120.0,},
  ['Limon corrosif']={[0]=60.0,},
  ['Limon ralentisseur']={[0]=10.0,},
  ['Linceul de flammes']={[0]=6.0,},
  ['Linceul de toiles']={[0]=8.0,},
  ['Linceul de vents']={[0]=10.0,},
  ['Lit de mort']={[0]=10.0,},
  ['Lucioles']={[0]=40.0,},
  ['Lucioles (farouche)']={[0]=40.0,},
  ['Lésions cérébrales']={[0]=30.0,},
  ['Léthargie cristalline']={[0]=15.0,},
  ['Léthargie druidique']={[0]=15.0,},
  ['Léthargie profonde']={[0]=15.0,},
  ['Magie sauvage']={[0]=30.0,},
  ['Main de Ragnaros']={[0]=2.0,},
  ['Main de Thaurissan']={[0]=5.0,},
  ['Mains couvertes de mousse']={[0]=180.0,},
  ['Maintien de givre']={[0]=10.0,},
  ['Maladie affaiblissante']={[0]=30.0,},
  ['Maladie du Champignon magenta']={[0]=1200.0,},
  ['Maladie térébrante']={[0]=300.0,},
  ['Maladresse']={[0]=30.0,},
  ['Maladresse II']={[0]=30.0,},
  ['Maladresse III']={[0]=30.0,},
  ['Malédiction d\'Hakkar']={[0]=120.0,},
  ['Malédiction d\'agonie']={[0]=24.0,},
  ['Malédiction d\'idiotie']={[0]=120.0,},
  ['Malédiction d\'impuissance']={[0]=120.0,},
  ['Malédiction de Brandefeu']={[0]=300.0,},
  ['Malédiction de Gehennas']={[0]=300.0,},
  ['Malédiction de Lucifron']={[0]=300.0,},
  ['Malédiction de Marduk']={[0]=5.0,},
  ['Malédiction de Mornecoeur']={[0]=180.0,},
  ['Malédiction de Shazzrah']={[0]=300.0,},
  ['Malédiction de Stalvan']={[0]=600.0,},
  ['Malédiction de Thule']={[0]=240.0,},
  ['Malédiction de Timmy']={[0]=60.0,},
  ['Malédiction de Tuten\'kash']={[0]=900.0,},
  ['Malédiction de faiblesse']={[0]=120.0,},
  ['Malédiction de fatigue']={[0]=12.0,},
  ['Malédiction de guérison']={[0]=180.0,},
  ['Malédiction de l\'ombre']={[0]=300.0,},
  ['Malédiction de la Banshee']={[0]=12.0,},
  ['Malédiction de la corne noire']={[0]=300.0,},
  ['Malédiction de l’oeil']={[0]=120.0,},
  ['Malédiction de témérité']={[0]=120.0,},
  ['Malédiction de vengeance']={[0]=900.0,},
  ['Malédiction des Cognepeurs']={[0]=60.0,},
  ['Malédiction des Magram défunts']={[0]=900.0,},
  ['Malédiction des Mort-bois']={[0]=120.0,},
  ['Malédiction des Tribus']={[0]=1800.0,},
  ['Malédiction des langages']={[0]=30.0,},
  ['Malédiction des éléments']={[0]=300.0,},
  ['Malédiction des épines']={[0]=180.0,},
  ['Malédiction du Porte-peste']={[0]=10.0,},
  ['Malédiction du Rat pestiféré']={[0]=14.0,},
  ['Malédiction du Sang']={[0]=600.0,},
  ['Malédiction du Seigneur élémentaire']={[0]=60.0,},
  ['Malédiction du Sombre maître']={[0]=60.0,},
  ['Malédiction du bois morne']={[0]=60.0,},
  ['Malédiction d’Arugal']={[0]=10.0,},
  ['Malédiction funeste']={[0]=60.0,},
  ['Malédiction imminente']={[0]=10.0,},
  ['Malédiction impie']={[0]=12.0,},
  ['Malédiction inévitable']={[0]=10.0,},
  ['Maléfice']={[0]=10.0,},
  ['Maléfice de Jammal\'an']={[0]=10.0,},
  ['Maléfice de Ravenclaw']={[0]=30.0,},
  ['Maléfice de faiblesse']={[0]=120.0,},
  ['Maléfice vaudou']={[0]=120.0,},
  ['Manteau de suie']={[0]=10.0,},
  ['Marque d\'Arlokk']={[0]=120.0,},
  ['Marque de Blaumeux']={[0]=75.0,},
  ['Marque de Kazzak']={[0]=60.0,},
  ['Marque de Korth\'azz']={[0]=75.0,},
  ['Marque de Mograine']={[0]=75.0,},
  ['Marque de Zeliek']={[0]=75.0,},
  ['Marque de détonation']={[0]=30.0,},
  ['Marque de givre']={[0]=12.0,},
  ['Marque de l\'ombre']={[0]=15.0,},
  ['Marque de la nature']={[0]=12.0,},
  ['Marque des flammes']={[0]=120.0,},
  ['Marque du chasseur']={[0]=120.0,},
  ['Marque du fauve']={[0]=1800.0,},
  ['Marteau de croisé']={[0]=4.0,},
  ['Marteau de la justice']={[1]=3.0,[2]=4.0,[3]=5.0,[4]=6.0,[0]=6.0,},
  ['Marteau du Juge']={[0]=10.0,},
  ['Martèlement violent']={[0]=10.0,},
  ['Masse suintante']={[0]=30.0,},
  ['Meurtrissure']={[0]=5.0,},
  ['Miasme aquatique']={[0]=3600.0,},
  ['Mise KO']={[0]=6.0,},
  ['Mise en cage']={[0]=30.0,},
  ['Moisson d\'âme']={[0]=60.0,},
  ['Montée d\'adrénaline']={[0]=20.0,},
  ['Morsure de la main']={[0]=8.0,},
  ['Morsure de la veuve']={[0]=4.0,},
  ['Morsure de serpent']={[0]=15.0,},
  ['Morsure de vipère']={[0]=8.0,},
  ['Morsure du givre']={[0]=5.0,},
  ['Morsure enragée']={[0]=6.0,},
  ['Morsure fatale']={[0]=12.0,},
  ['Morsure infectieuse']={[0]=180.0,},
  ['Morsure lacérante']={[0]=30.0,},
  ['Morsure putride']={[0]=30.0,},
  ['Morsures purulentes']={[0]=1800.0,},
  ['Mort du sous-chef Sombrefer']={[0]=6.0,},
  ['Mot de l\'ombre : Douleur']={[0]=18.0,},
  ['Mot de l\'ombre : Douleur']={[0]=18.0,},
  ['Mot sacré : Robustesse']={[0]=1800.0,},
  ['Mouvement de distraction']={[0]=7.0,},
  ['Murmures de C\'Thun']={[0]=20.0,},
  ['Mutation chromatique']={[0]=300.0,},
  ['Mutation de l\'insecte']={[0]=240.0,},
  ['Mutilation']={[0]=2.0,},
  ['Mutiler']={[0]=2.0,},
  ['Mélasse']={[0]=3.0,},
  ['Métal en fusion']={[0]=15.0,},
  ['Métamorphose']={[1]=20.0,[2]=30.0,[3]=40.0,[4]=50.0,[0]=50.0,},
  ['Métamorphose : cochon']={[0]=50.0,},
  ['Métamorphose : tortue']={[0]=50.0,},
  ['Métamorphose : vache']={[0]=50.0,},
  ['Métamorphose sauvage']={[0]=20.0,},
  ['Métamorphose supérieure']={[0]=20.0,},
  ['Métamorphose : mouton']={[0]=10.0,},
  ['Métamorphose : poulet']={[0]=10.0,},
  ['Nova de gel']={[0]=10.0,},
  ['Nova de givre']={[0]=8.0,},
  ['Nova de glace']={[0]=2.0,},
  ['Nova du Rat']={[0]=10.0,},
  ['Nuage de fumée']={[0]=3.0,},
  ['Nuage de poussière']={[0]=12.0,},
  ['Nuage empoisonné']={[0]=45.0,},
  ['Nuée de peste']={[0]=240.0,},
  ['Nuée de sauterelles']={[0]=6.0,},
  ['Ombre de Rochébène']={[0]=8.0,},
  ['Ombre perforante']={[0]=1800.0,},
  ['Onde de choc']={[0]=3.0,},
  ['Ordre d\'attaque']={[0]=10.0,},
  ['Ordre d\'ombre']={[0]=15.0,},
  ['Ordre de combat']={[0]=6.0,},
  ['Ordre frénétique']={[0]=10.0,},
  ['Oups !']={[0]=10.0,},
  ['Pacifier']={[0]=10.0,},
  ['Panique']={[0]=8.0,},
  ['Panneau de contrôle']={[0]=60.0,},
  ['Paralysie']={[0]=30.0,},
  ['Parasite']={[0]=75.0,},
  ['Perce-armure']={[0]=20.0,},
  ['Perce-faille']={[0]=5.0,},
  ['Percer']={[0]=10.0,},
  ['Percer cheville']={[0]=3.0,},
  ['Peste']={[0]=40.0,},
  ['Peste de goule']={[0]=1800.0,},
  ['Peste dévorante']={[0]=24.0,},
  ['Peste fièvreuse']={[0]=180.0,},
  ['Peste fiévreuse']={[0]=180.0,},
  ['Peste galopante']={[0]=300.0,},
  ['Peste noire']={[0]=90.0,},
  ['Peste progressive']={[0]=20.0,},
  ['Peste émétique']={[0]=300.0,},
  ['Petit Balayage']={[0]=12.0,},
  ['Peur']={[1]=10.0,[2]=15.0,[3]=20.0,[0]=20.0,},
  ['Peur (NYI)']={[0]=15.0,},
  ['Peur corrompue']={[0]=2.0,},
  ['Pieds couverts de mousse']={[0]=180.0,},
  ['Pinces acérées']={[0]=5.0,},
  ['Pinces de saisie']={[0]=15.0,},
  ['Piqûre de scorpide']={[0]=20.0,},
  ['Piqûre de scorpide améliorée']={[0]=20.0,},
  ['Piqûre de wyverne']={[0]=12.0,},
  ['Piège']={[0]=5.0,},
  ['Piège d\'herbes-fouet']={[0]=18.0,},
  ['Piège puant']={[0]=120.0,},
  ['Piétinement']={[0]=10.0,},
  ['Piétinement de kodo']={[0]=3.0,},
  ['Piétinement d’Azrethoc']={[0]=5.0,},
  ['Plaie sérieuse']={[0]=10.0,},
  ['Plainte mortelle']={[0]=6.0,},
  ['Planté']={[0]=3.0,},
  ['Plongeon frénétique']={[0]=2.0,},
  ['Pluie d\'acide']={[0]=10.0,},
  ['Poignarder Traqueur de l\'ombre']={[0]=5.0,},
  ['Poigne d\'autorité']={[0]=10.0,},
  ['Poignée de terre']={[0]=4.0,},
  ['Poignée griffue']={[0]=4.0,},
  ['Poing de Ragnaros']={[0]=5.0,},
  ['Poison']={[0]=30.0,},
  ['Poison Pétale-de-sang']={[0]=30.0,},
  ['Poison affaiblissant']={[0]=12.0,},
  ['Poison atal\'ai']={[0]=30.0,},
  ['Poison corrosif']={[0]=30.0,},
  ['Poison d\'affliction']={[0]=180.0,},
  ['Poison de Peau-de-venin']={[0]=30.0,},
  ['Poison de distraction mentale']={[0]=10.0,},
  ['Poison de distraction mentale II']={[0]=12.0,},
  ['Poison de distraction mentale III']={[0]=14.0,},
  ['Poison de drain mortel']={[0]=45.0,},
  ['Poison de l\'esprit']={[0]=15.0,},
  ['Poison de l’araignée']={[0]=30.0,},
  ['Poison de sangsue']={[0]=40.0,},
  ['Poison de scorpide']={[0]=10.0,},
  ['Poison douloureux']={[0]=15.0,},
  ['Poison du souvenir de Dirk']={[0]=30.0,},
  ['Poison du surveillant']={[0]=60.0,},
  ['Poison engourdissant']={[0]=25.0,},
  ['Poison faible']={[0]=12.0,},
  ['Poison lent']={[0]=30.0,},
  ['Poison lent II']={[0]=30.0,},
  ['Poison létal']={[0]=120.0,},
  ['Poison mortel']={[0]=12.0,},
  ['Poison mortel II']={[0]=12.0,},
  ['Poison mortel III']={[0]=12.0,},
  ['Poison mortel IV']={[0]=12.0,},
  ['Poison mortel V']={[0]=12.0,},
  ['Poison nécrotique']={[0]=30.0,},
  ['Poison paralysant']={[0]=8.0,},
  ['Poison tranquilisant']={[0]=8.0,},
  ['Poison virulent']={[0]=30.0,},
  ['Possession']={[0]=120.0,},
  ['Pourfendeur fracassant']={[0]=30.0,},
  ['Pourfendre']={[1]=9.0,[2]=12.0,[3]=15.0,[4]=18.0,[5]=21.0,[6]=21.0,[7]=21.0,[0]=21.0,},
  ['Pourfendre la chair']={[0]=12.0,},
  ['Pourriture de la goule']={[0]=600.0,},
  ['Pourriture noire']={[0]=1800.0,},
  ['Poussière projetée']={[0]=10.0,},
  ['Pouvoir d\'espèce : Bleu']={[0]=6.0,},
  ['Pouvoir d\'espèce : Bronze']={[0]=5.0,},
  ['Pouvoir d\'espèce : Rouge']={[0]=5.0,},
  ['Pouvoir d\'espèce : Vert']={[0]=6.0,},
  ['Problème de mobilité']={[0]=20.0,},
  ['Profanation']={[0]=10.0,},
  ['Projectiles des arcanes']={[1]=3.0,[2]=4.0,[3]=5.0,[4]=5.0,[5]=5.0,[6]=5.0,[7]=5.0,[8]=5.0,[0]=5.0,},
  ['Projection de magma']={[0]=30.0,},
  ['Prot Eclair de glace']={[0]=5.0,},
  ['Protection contre l\'Ombre']={[0]=3600.0,},
  ['Protection contre la Nature']={[0]=3600.0,},
  ['Protection contre le Feu']={[0]=3600.0,},
  ['Protection contre le Givre']={[0]=3600.0,},
  ['Protection contre le Sacré']={[0]=3600.0,},
  ['Provocation']={[0]=3.0,},
  ['Préméditation']={[0]=10.0,},
  ['Puanteur accablante']={[0]=6.0,},
  ['Puanteur pourrie']={[0]=10.0,},
  ['Puissance de Shahram']={[0]=5.0,},
  ['Puissance étourdissante']={[0]=300.0,},
  ['Puissant soufflet']={[0]=3.0,},
  ['Puits de lumière']={[0]=180.0,},
  ['Putréfaction de la chair']={[0]=10.0,},
  ['Pyroclasme']={[0]=3.0,},
  ['Pétrifier']={[0]=8.0,},
  ['Racines d\'herbe-fouet']={[0]=15.0,},
  ['Rafale de flammes']={[0]=45.0,},
  ['Rage']={[0]=600.0,},
  ['Rage d\'Arantir']={[0]=6.0,},
  ['Rage de Thule']={[0]=120.0,},
  ['Rage hurlante']={[0]=300.0,},
  ['Ralentissement']={[0]=10.0,},
  ['Rapetisser']={[0]=120.0,},
  ['Ravage']={[0]=2.0,},
  ['Rayon mortel gnome']={[0]=4.0,},
  ['Rayon polymorphique']={[0]=4.0,},
  ['Rayon réducteur']={[0]=20.0,},
  ['Reflet tordu']={[0]=45.0,},
  ['Regard de cristal']={[0]=6.0,},
  ['Regard menaçant']={[0]=6.0,},
  ['Regard repoussant']={[0]=8.0,},
  ['Remous']={[0]=4.0,},
  ['Renaissance de l\'âme']={[0]=1800.0,},
  ['Rendre fou']={[0]=6.0,},
  ['Renverser']={[0]=2.0,},
  ['Renvoi de la magie']={[0]=10.0,},
  ['Renvoi des morts-vivants']={[1]=10.0,[2]=15.0,[3]=20.0,[0]=20.0,},
  ['Repentir']={[0]=6.0,},
  ['Respiration aquatique']={[0]=600.0,},
  ['Retour de flammes']={[0]=30.0,},
  ['Rets']={[0]=8.0,},
  ['Rets II']={[0]=10.0,},
  ['Rets III']={[0]=12.0,},
  ['Rets de Naraxis']={[0]=30.0,},
  ['Rets enveloppants']={[0]=6.0,},
  ['Revers']={[0]=2.0,},
  ['Riposte']={[0]=6.0,},
  ['Robustesse de la fête du feu']={[0]=3600.0,},
  ['Rocher']={[0]=10.0,},
  ['Rougecroc']={[0]=6.0,},
  ['Rugissement d\'intimidation']={[0]=8.0,},
  ['Rugissement démoralisant']={[0]=30.0,},
  ['Rugissement glacial']={[0]=3.0,},
  ['Rugissement provocateur']={[0]=6.0,},
  ['Rugissement puissant']={[0]=3.0,},
  ['Rugissement terrifiant']={[0]=5.0,},
  ['Rupture']={[0]=6.0,},
  ['Régénération IV']={[0]=3600.0,},
  ['Réveiller les Gardiens terrestres']={[0]=5.0,},
  ['Saccager']={[0]=2.5,},
  ['Sacrifice']={[0]=8.0,},
  ['Salve d\'acide']={[0]=25.0,},
  ['Salve d\'éclairs de givre']={[0]=8.0,},
  ['Salve d\'éclairs de poison']={[0]=10.0,},
  ['Salve toxique']={[0]=15.0,},
  ['Sang maudit']={[0]=600.0,},
  ['Santé II']={[0]=3600.0,},
  ['Sarments']={[1]=12.0,[2]=15.0,[3]=18.0,[4]=21.0,[5]=24.0,[6]=27.0,[0]=27.0,},
  ['Secousse violente']={[0]=2.0,},
  ['Sensibilité au Feu']={[0]=45.0,},
  ['Sensibilité au Givre']={[0]=45.0,},
  ['Sensibilité aux Arcanes']={[0]=45.0,},
  ['Sensibilité à l\'Ombre']={[0]=45.0,},
  ['Sensibilité à la Nature']={[0]=45.0,},
  ['Serpent parasite']={[0]=10.0,},
  ['Serre d\'effroi']={[0]=60.0,},
  ['Silence']={[0]=5.0,},
  ['Siphon d\'âme']={[0]=15.0,},
  ['Siphon de Vie']={[0]=15.0,},
  ['Siphon de bénédiction']={[0]=30.0,},
  ['Siphon de sang']={[0]=8.0,},
  ['Siphon de santé']={[0]=15.0,},
  ['Siphon de vie']={[0]=30.0,},
  ['Siphon d’âme']={[0]=10.0,},
  ['Soins corrompus']={[0]=30.0,},
  ['Soins de masse']={[0]=12.0,},
  ['Sol L']={[0]=5.0,},
  ['Sommeil']={[1]=20.0,[2]=30.0,[0]=30.0,},
  ['Sommeil profond']={[0]=10.0,},
  ['Somnambulisme']={[0]=10.0,},
  ['Sonner']={[1]=2.0,[2]=3.0,[3]=4.0,[0]=4.0,},
  ['Souffle de Sargeras']={[0]=90.0,},
  ['Souffle glacial']={[0]=12.0,},
  ['Souffle putride']={[0]=30.0,},
  ['Stupidité']={[0]=30.0,},
  ['Stupidité II']={[0]=30.0,},
  ['Submersion']={[0]=60.0,},
  ['Sul\'thraze']={[0]=15.0,},
  ['Super rayon réducteur']={[0]=20.0,},
  ['Suriner']={[0]=4.0,},
  ['Séduction']={[0]=15.0,},
  ['TWEEP']={[0]=11.0,},
  ['Taille-tendon']={[0]=8.0,},
  ['Tape-crâne']={[0]=2.0,},
  ['Technique du cœur explosé par le gros doigt à cinq points']={[0]=30.0,},
  ['Tempête de grêle']={[0]=3.0,},
  ['Terreur ancienne']={[0]=900.0,},
  ['Terreur du démoniste']={[0]=2.0,},
  ['Terrifier']={[0]=4.0,},
  ['Test Frappe W35']={[0]=10.0,},
  ['Test Frappe W50']={[0]=10.0,},
  ['Test de Malédiction d\'agonie']={[0]=24.0,},
  ['Test de Stase']={[0]=5.0,},
  ['Tir désarçonnant']={[0]=2.0,},
  ['Tir empoisonné']={[0]=75.0,},
  ['Tir perforant']={[0]=15.0,},
  ['Tissage de toile']={[0]=7.0,},
  ['Toile d\'araignée']={[0]=1.0,},
  ['Toiles collantes']={[0]=8.0,},
  ['Tombeau de glace']={[0]=10.0,},
  ['Tonneau piège']={[0]=2.0,},
  ['Tornade']={[0]=4.0,},
  ['Totems corrompus']={[0]=30.0,},
  ['Toucher de Flétrissure']={[0]=180.0,},
  ['Toucher de Ravenclaw']={[0]=5.0,},
  ['Toucher de faiblesse']={[0]=120.0,},
  ['Toucher de flétrissement']={[0]=120.0,},
  ['Toucher du Bouteflammes']={[0]=3.0,},
  ['Toucher débilitant']={[0]=120.0,},
  ['Toucher glacial']={[0]=8.0,},
  ['Toucher glacial de Trelane']={[0]=12.0,},
  ['Tourbillon']={[0]=2.0,},
  ['Toxine de Limace']={[0]=45.0,},
  ['Toxine localisée']={[0]=60.0,},
  ['Toxine mortelle']={[0]=12.0,},
  ['Toxine mortelle II']={[0]=12.0,},
  ['Toxine mortelle III']={[0]=12.0,},
  ['Toxine mortelle IV']={[0]=12.0,},
  ['Trait de choc']={[0]=4.0,},
  ['Trait de choc amélioré']={[0]=3.0,},
  ['Trait de gelée']={[0]=6.0,},
  ['Trait de glace']={[0]=10.0,},
  ['Trait de l\'ombre']={[0]=6.0,},
  ['Trait incendiaire']={[0]=30.0,},
  ['Transfert']={[0]=1.0,},
  ['Transformation involontaire']={[0]=30.0,},
  ['Transi']={[0]=1.5,},
  ['Transir']={[0]=30.0,},
  ['Traquenard']={[0]=2.0,},
  ['Traquenard sanglant']={[0]=18.0,},
  ['Tremblement de terre']={[0]=2.0,},
  ['Tremblement psychique']={[0]=600.0,},
  ['Trou de temps']={[0]=8.0,},
  ['Trépanation']={[0]=20.0,},
  ['Tétanos']={[0]=1200.0,},
  ['Urticaire']={[0]=1800.0,},
  ['Vague explosive']={[0]=6.0,},
  ['Varicelle de silithide']={[0]=1800.0,},
  ['Vengeance']={[0]=600.0,},
  ['Vengeance des Pins-tordus']={[0]=6.0,},
  ['Vengeance du Bouteflammes']={[0]=2.0,},
  ['Venin de rampant']={[0]=300.0,},
  ['Venin enivrant']={[0]=120.0,},
  ['Vents brûlants']={[0]=8.0,},
  ['Vents cinglants']={[0]=1.0,},
  ['Verrou magique']={[1]=6.0,[2]=8.0,[0]=8.0,},
  ['Vision assombrie']={[0]=12.0,},
  ['Voile de l\'ombre']={[0]=15.0,},
  ['Voile des ténèbres']={[0]=7.0,},
  ['Voile mortel']={[0]=3.0,},
  ['Volonté de Hakkar']={[0]=20.0,},
  ['Volonté vacillante']={[0]=60.0,},
  ['Volée d\'épines']={[0]=2.0,},
  ['Volée de coups']={[0]=4.0,},
  ['Volée de coups sauvage']={[0]=5.0,},
  ['Vrilles d\'air']={[0]=2.0,},
  ['Vulnérabilité au Feu']={[0]=30.0,},
  ['Vulnérabilité aux sorts']={[0]=5.0,},
  ['Vulnérabilité à l\'Ombre']={[0]=15.0,},
  ['Vulnérabilité élémentaire']={[0]=30.0,},
  ['Vulnérable']={[0]=3.0,},
  ['Windsor renvoie le cheval DND']={[0]=55.0,},
  ['Zeste chimique']={[0]=10.0,},
  ['[PH] Décompte Cadavre Cristal']={[0]=7200.0,},
  ['test d\'échelle']={[0]=10.0,},
  ['Œil d\'Immol\'thar']={[0]=4.0,},
  ['Œil de glace']={[0]=15.0,},
}

-- custom entries not detected by DBC extractor
pfUI_locale["frFR"]["debuffs"]['Cône de froid']={[0]=8.0,} -- Cone of Cold

pfUI_locale["frFR"]["totems"] = {
  ["Totem de Purification des maladies"] = "spell_nature_diseasecleansingtotem",
  ["Totem élémentaire de terre"] = "spell_nature_earthelemental_totem",
  ["Totem de lien terrestre"] = "spell_nature_strengthofearthtotem02",
  ["Totem élémentaire de feu"] = "spell_fire_elemental_totem",
  ["Totem Nova de feu"] = "spell_fire_sealoffire",
  ["Totem de résistance au Feu"] = "spell_fireresistancetotem_01",
  ["Totem Langue de feu"] = "spell_nature_guardianward",
  ["Totem de résistance au Givre"] = "spell_frostresistancetotem_01",
  ["Totem de Grâce aérienne"] = "spell_nature_invisibilitytotem",
  ["Totem de Glèbe"] = "spell_nature_groundingtotem",
  ["Totem guérisseur"] = "Inv_spear_04",
  ["Totem de Magma"] = "spell_fire_selfdestruct",
  ["Totem Fontaine de mana"] = "spell_nature_manaregentotem",
  ["Totem de Vague de mana"] = "spell_frost_summonwaterelemental",
  ["Totem de résistance à la Nature"] = "spell_nature_natureresistancetotem",
  ["Totem de Purification du poison"] = "spell_nature_poisoncleansingtotem",
  ["Totem incendiaire"] = "spell_fire_searingtotem",
  ["Totem Sentinelle"] = "spell_nature_removecurse",
  ["Totem de Griffes de pierre"] = "spell_nature_stoneclawtotem",
  ["Totem de Peau de pierre"] = "spell_nature_stoneskintotem",
  ["Totem de Force de la Terre"] = "spell_nature_earthbindtotem",
  ["Totem de courroux"] = "spell_fire_totemofwrath",
  ["Totem de Séisme"] = "spell_nature_tremortotem",
  ["Totem Furie-des-vents"] = "spell_nature_windfury",
  ["Totem de Mur des vents"] = "spell_nature_earthbind",
  ["Totem de courroux de l'air"] = "spell_nature_slowingtotem",
}

pfUI_locale["frFR"]["icons"] = {
  ["Abolir maladie"] = "Spell_Nature_NullifyDisease",
  ["Abolir le poison"] = "Spell_Nature_NullifyPoison_02",
  ["Effet Abolir le poison"] = "Spell_Nature_NullifyPoison_02",
  ["Activation de la tourelle de mitrailleuse"] = "INV_Weapon_Rifle_10",
  ["Poussée d'adrénaline"] = "Spell_Shadow_ShadowWordDominate",
  ["Conséquences"] = "Spell_Fire_Fire",
  ["Agressivité"] = "Ability_Racial_Avatar",
  ["Flammes déchirantes"] = "Spell_Fire_BlueImmolation",
  ["Visée"] = "INV_Spear_07",
  ["Alchimie"] = "Trade_Alchemy",
  ["Embuscade"] = "Ability_Rogue_Ambush",
  ["Malédiction amplifiée"] = "Spell_Shadow_Contagion",
  ["Amplification de la magie"] = "Spell_Holy_FlashHeal",
  ["Robustesse des anciens"] = "Spell_Nature_UndyingStrength",
  ["Guérison des anciens"] = "Spell_Nature_UndyingStrength",
  ["Connaissance ancestrale"] = "Spell_Shadow_GrimWard",
  ["Esprit ancestral"] = "Spell_Nature_Regenerate",
  ["Hystérie ancienne"] = "Spell_Shadow_UnholyFrenzy",
  ["Poison anesthésiant"] = "Spell_Nature_SlowPoison",
  ["Maîtrise de la Rage"] = "Spell_Holy_BlessingOfStamina",
  ["Angoisse"] = "Spell_Shadow_GatherShadows",
  ["Anticipation"] = "Spell_Nature_MirrorImage",
  ["Forme aquatique"] = "Ability_Druid_AquaticForm",
  ["Déflagration des arcanes"] = "Spell_Arcane_Blast",
  ["Illumination des arcanes"] = "Spell_Holy_ArcaneIntellect",
  ["Concentration des arcanes"] = "Spell_Shadow_ManaBurn",
  ["Energie des arcanes"] = "Spell_Holy_MindVision",
  ["Explosion des arcanes"] = "Spell_Nature_WispSplode",
  ["Focalisation des arcanes"] = "Spell_Holy_Devotion",
  ["Robustesse des arcanes"] = "Spell_Arcane_ArcaneResilience",
  ["Impact des arcanes"] = "Spell_Nature_WispSplode",
  ["Instabilité des arcanes"] = "Spell_Shadow_Teleport",
  ["Intelligence des arcanes"] = "Spell_Holy_MagicalSentry",
  ["Méditation des arcanes"] = "Spell_Shadow_SiphonMana",
  ["Esprit des arcanes"] = "Spell_Shadow_Charm",
  ["Projectiles des arcanes"] = "Spell_Nature_StarFall",
  ["Toute-puissance des arcanes"] = "Spell_Arcane_ArcanePotency",
  ["Pouvoir des arcanes"] = "Spell_Nature_Lightning",
  ["Résistance aux Arcanes"] = "Spell_Nature_StarFall",
  ["Tir des arcanes"] = "Ability_ImpalingBolt",
  ["Voile des arcanes"] = "Spell_Magic_LesserInvisibilty",
  ["Subtilité des arcanes"] = "Spell_Holy_DispelMagic",
  ["Torrent arcanique"] = "Spell_Shadow_Teleport",
  ["Sensibilité aux Arcanes"] = "Spell_Shadow_SoulLeech_2",
  ["Allonge arctique"] = "Spell_Shadow_DarkRitual",
  ["Vents arctiques"] = "Spell_Frost_ArcticWinds",
  ["Armure de la Foi"] = "Spell_Holy_BlessingOfProtection",
  ["Fabricant d'armures"] = "Trade_BlackSmithing",
  ["Aspect de la bête"] = "Ability_Mount_PinkTiger",
  ["Aspect du guépard"] = "Ability_Mount_JungleTiger",
  ["Aspect du faucon"] = "Spell_Nature_RavenForm",
  ["Aspect du singe"] = "Ability_Hunter_AspectOfTheMonkey",
  ["Aspect de la meute"] = "Ability_Mount_WhiteTiger",
  ["Aspect de la vipère"] = "Ability_Hunter_AspectoftheViper",
  ["Aspect de la nature"] = "Spell_Nature_ProtectionformNature",
  ["Rappel astral"] = "Spell_Nature_AstralRecal",
  ["Attaque"] = "Temp",
  ["Attaque"] = "Temp",
  ["Tir automatique"] = "Ability_Whirlwind",
  ["Bouclier du vengeur"] = "Spell_Holy_AvengersShield",
  ["Courroux vengeur"] = "Spell_Holy_AvengineWrath",
  ["Evitement"] = "Spell_Magic_LesserInvisibilty",
  ["Spécialisation Hache"] = "INV_Axe_06",
  ["Contrecoup"] = "Spell_Fire_PlayingWithFire",
  ["Attaque sournoise"] = "Ability_BackStab",
  ["Insigne de garde-essaim"] = "INV_Misc_AhnQirajTrinket_04",
  ["Equilibre de la puissance"] = "Ability_Druid_BalanceofPower",
  ["Fléau"] = "Spell_Shadow_DeathPact",
  ["Bannir"] = "Spell_Shadow_Cripple",
  ["Malédiction de la Banshee"] = "Spell_Nature_Drowsy",
  ["Ecorce"] = "Spell_Nature_StoneClawTotem",
  ["Barrage"] = "Ability_UpgradeMoonGlaive",
  ["Sonner"] = "Ability_Druid_Bash",
  ["Feu de camp basique"] = "Spell_Fire_Fire",
  ["Ivresse de la bataille"] = "Ability_Whirlwind",
  ["Cri de guerre"] = "Ability_Warrior_BattleShout",
  ["Posture de combat"] = "Ability_Warrior_OffensiveStance",
  ["Posture de combat"] = "Ability_Warrior_OffensiveStance",
  ["Forme d'ours"] = "Ability_Racial_BearForm",
  ["Connaissance des bêtes"] = "Ability_Physical_Taunt",
  ["Tueur de bêtes"] = "INV_Misc_Pelt_Bear_Ruin_02",
  ["Dressage des bêtes"] = "Ability_Hunter_BeastCall02",
  ["Bénédiction"] = "Spell_Frost_WindWalkOn",
  ["Rage berserker"] = "Spell_Nature_AncestralGuardian",
  ["Posture berserker"] = "Ability_Racial_Avatar",
  ["Posture berserker"] = "Ability_Racial_Avatar",
  ["Berserker"] = "Racial_Troll_Berserk",
  ["Discipline bestiale"] = "Spell_Nature_AbolishMagic",
  ["Rapidité bestiale"] = "Ability_Druid_Dash",
  ["Courroux bestial"] = "Ability_Druid_FerociousBite",
  ["Soins de lien"] = "Spell_Holy_BlindingHeal",
  ["Morsure"] = "Ability_Racial_Cannibalize",
  ["Flèche noire"] = "Ability_TheBlackArrow",
  ["Aveuglement"] = "Spell_Shadow_GatherShadows",
  ["Forge"] = "Trade_BlackSmithing",
  ["Déluge de lames"] = "Ability_Warrior_PunishingBlow",
  ["Froissement"] = "Ability_Warrior_Challange",
  ["Tournoiement de lames"] = "Ability_Rogue_BladeTwisting",
  ["Vague explosive"] = "Spell_Holy_Excorcism_02",
  ["Vitesse flamboyante"] = "Spell_Fire_BurningSpeed",
  ["Rétablissement béni"] = "Spell_Holy_BlessedRecovery",
  ["Résilience bénie"] = "Spell_Holy_BlessedResillience",
  ["Bénédiction d'Auchindoun"] = "INV_Battery_02",
  ["Bénédiction de liberté"] = "Spell_Holy_SealOfValor",
  ["Bénédiction des rois"] = "Spell_Magic_MageArmor",
  ["Bénédiction de lumière"] = "Spell_Holy_PrayerOfHealing02",
  ["Bénédiction de puissance"] = "Spell_Holy_FistOfJustice",
  ["Bénédiction de protection"] = "Spell_Holy_SealOfProtection",
  ["Bénédiction de sacrifice"] = "Spell_Holy_SealOfSacrifice",
  ["Bénédiction de salut"] = "Spell_Holy_SealOfSalvation",
  ["Bénédiction du sanctuaire"] = "Spell_Nature_LightningShield",
  ["Bénédiction de sagesse"] = "Spell_Holy_SealOfWisdom",
  ["Cécité"] = "Spell_Shadow_MindSteal",
  ["Poudre aveuglante"] = "INV_Misc_Ammo_Gunpowder_02",
  ["Transfert"] = "Spell_Arcane_Blink",
  ["Blizzard"] = "Spell_Frost_IceStorm",
  ["Bloquer"] = "Ability_Defend",
  ["Folie sanguinaire"] = "Spell_Shadow_SummonImp",
  ["Frénésie sanglante"] = "Ability_Warrior_BloodFrenzy",
  ["Fureur sanguinaire"] = "Racial_Orc_BerserkerStrength",
  ["Pacte de sang"] = "Spell_Shadow_BloodBoil",
  ["Furie sanguinaire"] = "Spell_Nature_BloodLust",
  ["Rage sanguinaire"] = "Ability_Racial_BloodRage",
  ["Sanguinaire"] = "Spell_Nature_BloodLust",
  ["Boutoir"] = "Spell_Shadow_VampiricAura",
  ["Voix tonitruante"] = "Spell_Nature_Purge",
  ["Spécialisation Arc"] = "INV_Weapon_Bow_12",
  ["Arcs"] = "INV_Weapon_Bow_05",
  ["Feu de camp éclatant"] = "Spell_Fire_Fire",
  ["Armure fragile"] = "Spell_Shadow_GrimWard",
  ["Impact brutal"] = "Ability_Druid_Bash",
  ["Montée d'adrénaline"] = "INV_Gauntlets_03",
  ["Ame ardente"] = "Spell_Fire_Fire",
  ["Souhait ardent"] = "Spell_Shadow_PsychicScream",
  ["Appel du familier"] = "Ability_Hunter_BeastCall",
  ["Appel des flammes"] = "Spell_Fire_Immolation",
  ["Appel de la foudre"] = "Spell_Nature_CallStorm",
  ["Appel du nexus"] = "Spell_Holy_MindVision",
  ["Dissimulation"] = "Ability_Stealth",
  ["Cannibalisme"] = "Ability_Racial_Cannibalize",
  ["Forme de félin"] = "Ability_Druid_CatForm",
  ["Cataclysme"] = "Spell_Fire_WindsofWoe",
  ["Focalisation céleste"] = "Spell_Arcane_StarFire",
  ["Salve de guérison"] = "Spell_Nature_HealingWaveGreater",
  ["Chaîne d'éclairs"] = "Spell_Nature_ChainLightning",
  ["Rugissement provocateur"] = "Ability_Druid_ChallangingRoar",
  ["Cri de défi"] = "Ability_BullRush",
  ["Charge"] = "Ability_Warrior_Charge",
  ["Effet Bonus de Rage de la Charge"] = "Ability_Warrior_Charge",
  ["Charge étourdissante"] = "Spell_Frost_Stun",
  ["Châtier"] = "Spell_Holy_Chastise",
  ["Coup bas"] = "Ability_CheapShot",
  ["Transi"] = "Spell_Frost_IceStorm",
  ["Cercle de soins"] = "Spell_Holy_CircleOfRenewal",
  ["Griffe"] = "Ability_Druid_Rake",
  ["Epuration"] = "Spell_Holy_Renew",
  ["Idées claires"] = "Spell_Shadow_ManaBurn",
  ["Enchaînement"] = "Ability_Warrior_Cleave",
  ["Pièges astucieux"] = "Spell_Nature_TimeStop",
  ["Cape d'ombre"] = "Spell_Shadow_NetherCloak",
  ["Fermeture"] = "Temp",
  ["Tissu"] = "INV_Chest_Cloth_21",
  ["Pierre à aiguiser grossière"] = "INV_Stone_SharpeningStone_02",
  ["Réflexes du cobra"] = "Spell_Nature_GuardianWard",
  ["Sang froid"] = "Spell_Ice_Lament",
  ["Morsure de glace"] = "Spell_Frost_WizardMark",
  ["Endurance de combat"] = "Spell_Nature_AncestralGuardian",
  ["Combustion"] = "Spell_Fire_SealOfFire",
  ["Commandement"] = "Ability_Warrior_WarCry",
  ["Présence de commandement"] = "Ability_Warrior_BattleShout",
  ["Cri de commandement"] = "Ability_Warrior_RallyingCry",
  ["Aura de concentration"] = "Spell_Holy_MindSooth",
  ["Commotion"] = "Spell_Fire_Fireball",
  ["Bourrasque"] = "Ability_ThunderBolt",
  ["Barrage commotionnant"] = "Spell_Arcane_StarFire",
  ["Trait de choc"] = "Spell_Frost_Stun",
  ["Cône de froid"] = "Spell_Frost_Glacier",
  ["Conflagration"] = "Spell_Fire_Fireball",
  ["Déflagration"] = "Spell_Fire_Incinerate",
  ["Invocation de nourriture"] = "INV_Misc_Food_10",
  ["Invocation d'une agate de mana"] = "INV_Misc_Gem_Emerald_01",
  ["Invocation d'une citrine de mana"] = "INV_Misc_Gem_Opal_01",
  ["Invocation d'une émeraude de mana"] = "INV_Misc_Gem_Stone_01",
  ["Invocation d'une jade de mana"] = "INV_Misc_Gem_Emerald_02",
  ["Invocation d'un rubis de mana"] = "INV_Misc_Gem_Ruby_01",
  ["Invocation d'eau"] = "INV_Drink_06",
  ["Consécration"] = "Spell_Holy_InnerFire",
  ["Consumer la magie"] = "Spell_Arcane_StudentOfMagic",
  ["Consumer les ombres"] = "Spell_Shadow_AntiShadow",
  ["Contagion"] = "Spell_Shadow_PainfulAfflictions",
  ["Convection"] = "Spell_Nature_WispSplode",
  ["Conviction"] = "Spell_Holy_RetributionAura",
  ["Cuisine"] = "INV_Misc_Food_15",
  ["Corruption"] = "Spell_Shadow_AbominationExplosion",
  ["Contre-attaque"] = "Ability_Warrior_Challange",
  ["Contresort"] = "Spell_Frost_IceShock",
  ["Contresort - Silencieux"] = "Spell_Frost_IceShock",
  ["Dérobade"] = "Ability_Druid_Cower",
  ["Création de Pierre de feu"] = "INV_Ammo_FireTar",
  ["Création de Pierre de feu (supérieure)"] = "INV_Ammo_FireTar",
  ["Création de Pierre de feu (inférieure)"] = "INV_Ammo_FireTar",
  ["Création de Pierre de feu (majeure)"] = "INV_Ammo_FireTar",
  ["Création de Pierre de soins"] = "INV_Stone_04",
  ["Création de Pierre de soins (supérieure)"] = "INV_Stone_04",
  ["Création de Pierre de soins (inférieure)"] = "INV_Stone_04",
  ["Création de Pierre de soins (majeure)"] = "INV_Stone_04",
  ["Création de Pierre de soins (mineure)"] = "INV_Stone_04",
  ["Création de Pierre d'âme"] = "Spell_Shadow_SoulGem",
  ["Création de Pierre d'âme (supérieure)"] = "Spell_Shadow_SoulGem",
  ["Création de Pierre d'âme (inférieure)"] = "Spell_Shadow_SoulGem",
  ["Création de Pierre d'âme (majeure)"] = "Spell_Shadow_SoulGem",
  ["Création de Pierre d'âme (mineure)"] = "Spell_Shadow_SoulGem",
  ["Création de Pierre de sort"] = "INV_Misc_Gem_Sapphire_01",
  ["Création de Pierre de sort (supérieure)"] = "INV_Misc_Gem_Sapphire_01",
  ["Création de Pierre de sort (majeure)"] = "INV_Misc_Gem_Sapphire_01",
  ["Création de Pierre de sort (magistrale)"] = "INV_Misc_Gem_Sapphire_01",
  ["Paralysie progressive"] = "Spell_Nature_TimeStop",
  ["Faiblesse"] = "Spell_Shadow_Cripple",
  ["Poison affaiblissant"] = "Ability_PoisonSting",
  ["Poison affaiblissant II"] = "Ability_PoisonSting",
  ["Masse critique"] = "Spell_Nature_WispHeal",
  ["Arbalètes"] = "INV_Weapon_Crossbow_01",
  ["Cruauté"] = "Ability_Rogue_Eviscerate",
  ["Croisade"] = "Spell_Holy_Crusade",
  ["Aura de croisé"] = "Spell_Holy_CrusaderAura",
  ["Inquisition"] = "Spell_Holy_CrusaderStrike",
  ["Culture"] = "INV_Misc_Flower_01",
  ["Guérison des maladies"] = "Spell_Holy_NullifyDisease",
  ["Guérison du poison"] = "Spell_Nature_NullifyPoison",
  ["Malédiction d'agonie"] = "Spell_Shadow_CurseOfSargeras",
  ["Malédiction funeste"] = "Spell_Shadow_AuraOfDarkness",
  ["Effet Malédiction funeste"] = "Spell_Shadow_AuraOfDarkness",
  ["Malédiction de fatigue"] = "Spell_Shadow_GrimWard",
  ["Malédiction d'idiotie"] = "Spell_Shadow_MindRot",
  ["Malédiction de témérité"] = "Spell_Shadow_UnholyStrength",
  ["Malédiction de l'ombre"] = "Spell_Shadow_CurseOfAchimonde",
  ["Malédiction des langages"] = "Spell_Shadow_CurseOfTounges",
  ["Malédiction de faiblesse"] = "Spell_Shadow_CurseOfMannoroth",
  ["Malédiction des éléments"] = "Spell_Shadow_ChillTouch",
  ["Cyclone"] = "Spell_Nature_EarthBind",
  ["Spécialisation Dague"] = "INV_Weapon_ShortBlade_05",
  ["Dagues"] = "Ability_SteelMelee",
  ["Atténuation de la magie"] = "Spell_Nature_AbolishMagic",
  ["Barrage noir"] = "Spell_Shadow_PainSpike",
  ["Pacte noir"] = "Spell_Shadow_DarkRitual",
  ["Ténèbres"] = "Spell_Shadow_Twilight",
  ["Célérité"] = "Ability_Druid_Dash",
  ["Crabe de pierre d'aube"] = "Ability_Hunter_Pet_Crab",
  ["Hébétement"] = "Spell_Frost_Stun",
  ["Effet d'interruption mortel"] = "INV_ThrowingKnife_06",
  ["Poison mortel"] = "Ability_Rogue_DualWeild",
  ["Poison mortel II"] = "Ability_Rogue_DualWeild",
  ["Poison mortel III"] = "Ability_Rogue_DualWeild",
  ["Poison mortel IV"] = "Ability_Rogue_DualWeild",
  ["Poison mortel V"] = "Ability_Rogue_DualWeild",
  ["Poison mortel VI"] = "Ability_Rogue_DualWeild",
  ["Poison mortel VII"] = "Ability_Rogue_DualWeild",
  ["Lancer mortel"] = "INV_ThrowingKnife_06",
  ["Voile mortel"] = "Spell_Shadow_DeathCoil",
  ["Souhait mortel"] = "Spell_Shadow_DeathPact",
  ["Blessure profonde"] = "Ability_BackStab",
  ["Blessures profondes"] = "Ability_BackStab",
  ["Défense"] = "Ability_Racial_ShadowMeld",
  ["Posture défensive"] = "Ability_Warrior_DefensiveStance",
  ["Posture défensive"] = "Ability_Warrior_DefensiveStance",
  ["Défi"] = "Ability_Warrior_InnerRage",
  ["Déviation"] = "Ability_Parry",
  ["Illusions de Jin'do"] = "Spell_Shadow_UnholyFrenzy",
  ["Armure démoniaque"] = "Spell_Shadow_RagingScream",
  ["Peau de démon"] = "Spell_Shadow_RagingScream",
  ["Egide démoniaque"] = "Spell_Shadow_RagingScream",
  ["Baiser démoniaque"] = "Spell_Shadow_Metamorphosis",
  ["Frénésie démoniaque"] = "Spell_Shadow_DeathPact",
  ["Connaissance démoniaque"] = "Spell_Shadow_ImprovedVampiricEmbrace",
  ["Résilience démoniaque"] = "Spell_Shadow_DemonicFortitude",
  ["Sacrifice démoniaque"] = "Spell_Shadow_PsychicScream",
  ["Tactique démoniaque"] = "Spell_Shadow_DemonicTactics",
  ["Rugissement démoralisant"] = "Ability_Druid_DemoralizingRoar",
  ["Cri démoralisant"] = "Ability_Warrior_WarCry",
  ["Pierre à aiguiser dense"] = "INV_Stone_SharpeningStone_05",
  ["Prière du désespoir"] = "Spell_Holy_Restoration",
  ["Allonge de destruction"] = "Spell_Shadow_CorpseExplode",
  ["Détection"] = "Ability_Hibernation",
  ["Détection de l'invisibilité supérieure"] = "Spell_Shadow_DetectInvisibility",
  ["Détection de l'invisibilité"] = "Spell_Shadow_DetectInvisibility",
  ["Détection de l'invisibilité inférieure"] = "Spell_Shadow_DetectLesserInvisibility",
  ["Détection de la magie"] = "Spell_Holy_Dizzy",
  ["Détection des pièges"] = "Ability_Spy",
  ["Dissuasion"] = "Ability_Whirlwind",
  ["Dévaster"] = "INV_Sword_11",
  ["Dévastation"] = "Spell_Fire_FlameShock",
  ["Aura de dévotion"] = "Spell_Holy_DevotionAura",
  ["Dévorer la magie"] = "Spell_Nature_Purge",
  ["Effet dévorer la magie"] = "Spell_Nature_Purge",
  ["Peste dévorante"] = "Spell_Shadow_BlackPlague",
  ["Diplomatie"] = "INV_Misc_Note_02",
  ["Forme d'ours redoutable"] = "Ability_Racial_BearForm",
  ["Coups fourrés"] = "Spell_Shadow_SummonSuccubus",
  ["Coup tordu"] = "Ability_Sap",
  ["Désarmement"] = "Ability_Warrior_Disarm",
  ["Désarmement de piège"] = "Spell_Shadow_GrimWard",
  ["Totem de Purification des maladies"] = "Spell_Nature_DiseaseCleansingTotem",
  ["Désenchanter"] = "Spell_Holy_RemoveCurse",
  ["Désengagement"] = "Ability_Rogue_Feint",
  ["Renvoyer le familier"] = "Spell_Nature_SpiritWolf",
  ["Dissipation de la magie"] = "Spell_Holy_DispelMagic",
  ["Distraction"] = "Ability_Rogue_Distract",
  ["Trait provocateur"] = "Spell_Arcane_Blink",
  ["Plongeon"] = "Spell_Shadow_BurningSpirit",
  ["Faveur divine"] = "Spell_Holy_Heal",
  ["Fureur divine"] = "Spell_Holy_SealOfWrath",
  ["Illumination divine"] = "Spell_Holy_DivineIllumination",
  ["Intelligence divine"] = "Spell_Nature_Sleep",
  ["Intervention divine"] = "Spell_Nature_TimeStop",
  ["Protection divine"] = "Spell_Holy_Restoration",
  ["Bouclier divin"] = "Spell_Holy_DivineIntervention",
  ["Esprit divin"] = "Spell_Holy_DivineSpirit",
  ["Force divine"] = "Ability_GolemThunderClap",
  ["Courroux divin"] = "Spell_Holy_SearingLight",
  ["Esquiver"] = "Spell_Nature_Invisibilty",
  ["Feu funeste"] = "Spell_Fire_Incinerate",
  ["Souffle du dragon"] = "INV_Misc_Head_Dragon_01",
  ["Travail du cuir d'écailles de dragon"] = "INV_Misc_MonsterScales_03",
  ["Drain de vie"] = "Spell_Shadow_LifeDrain02",
  ["Drain de mana"] = "Spell_Shadow_SiphonMana",
  ["Drain d'âme"] = "Spell_Shadow_Haunting",
  ["Sommeil sans rêve"] = "Spell_Nature_Sleep",
  ["Boisson"] = "INV_Drink_07",
  ["Ambidextrie"] = "Ability_DualWield",
  ["Spécialisation Ambidextrie"] = "Ability_DualWield",
  ["Duel"] = "Temp",
  ["Nuage de poussière"] = "Ability_Hibernation",
  ["Oeil d'aigle"] = "Ability_Hunter_EagleEye",
  ["Totem élémentaire de terre"] = "Spell_Nature_EarthElemental_Totem",
  ["Bouclier de terre"] = "Spell_Nature_SkinofEarth",
  ["Horion de terre"] = "Spell_Nature_EarthShock",
  ["Lien à la terre"] = "Spell_Nature_StrengthOfEarthTotem02",
  ["Totem de lien terrestre"] = "Spell_Nature_StrengthOfEarthTotem02",
  ["Choc de terre"] = "Spell_Nature_AbolishMagic",
  ["Efficacité"] = "Spell_Frost_WizardMark",
  ["Dévastation élémentaire"] = "Spell_Fire_ElementalDevastation",
  ["Focalisation élémentaire"] = "Spell_Shadow_ManaBurn",
  ["Fureur élémentaire"] = "Spell_Fire_Volcano",
  ["Travail du cuir élémentaire"] = "Trade_LeatherWorking",
  ["Maîtrise élémentaire"] = "Spell_Nature_WispHeal",
  ["Précision élémentaire"] = "Spell_Nature_ElementalPrecision_1",
  ["Pierre à aiguiser élémentaire"] = "INV_Stone_02",
  ["Armes élémentaires"] = "Spell_Fire_FlameTounge",
  ["Grâce d'Elune"] = "Spell_Holy_ElunesGrace",
  ["Insaisissable"] = "Spell_Magic_LesserInvisibilty",
  ["Tempête ardente"] = "Spell_Fire_SelfDestruct",
  ["Projectiles des arcanes surpuissants"] = "Spell_Nature_StarFall",
  ["Corruption surpuissante"] = "Spell_Shadow_AbominationExplosion",
  ["Boule de feu surpuissante"] = "Spell_Fire_FlameBolt",
  ["Eclair de givre surpuissant"] = "Spell_Frost_FrostBolt02",
  ["Soins surpuissants"] = "Spell_Holy_GreaterHeal",
  ["Récupération surpuissante"] = "Ability_Druid_EmpoweredRejuvination",
  ["Toucher surpuissant"] = "Ability_Druid_EmpoweredTouch",
  ["Esprit de l'eau amoureux"] = "INV_Wand_01",
  ["Enchantement"] = "Trade_Engraving",
  ["Endurance"] = "Spell_Nature_UnyeildingStamina",
  ["Entraînement à l'Endurance"] = "Spell_Nature_Reincarnation",
  ["Ingénierie"] = "Trade_Engineering",
  ["Spécialisation"] = "INV_Misc_Gear_01",
  ["Enrager"] = "Ability_Druid_Enrage",
  ["Biscuit enrichi en manne"] = "INV_Misc_Fork&Knife",
  ["Asservir démon"] = "Spell_Shadow_EnslaveDemon",
  ["Sarments"] = "Spell_Nature_StrangleVines",
  ["Piège"] = "Spell_Nature_StrangleVines",
  ["Envenimer"] = "Ability_Rogue_Disembowel",
  ["Maître de l'évasion"] = "Ability_Rogue_Trip",
  ["Evasion"] = "Spell_Shadow_ShadowWard",
  ["Eviscération"] = "Ability_Rogue_Eviscerate",
  ["Evocation"] = "Spell_Nature_Purge",
  ["Exécution"] = "INV_Sword_48",
  ["Exorcisme"] = "Spell_Holy_Excorcism_02",
  ["Pensée expansive"] = "INV_Enchant_EssenceEternalLarge",
  ["Piège explosif"] = "Spell_Fire_SelfDestruct",
  ["Effet Piège explosif"] = "Spell_Fire_SelfDestruct",
  ["Exposer l'armure"] = "Ability_Warrior_Riposte",
  ["Perce-faille"] = "Ability_Hunter_SniperShot",
  ["Extraction de gaz"] = "Spell_Nature_AbolishMagic",
  ["Oeil pour oeil"] = "Spell_Holy_EyeforanEye",
  ["Oeil de Kilrogg"] = "Spell_Shadow_EvilEye",
  ["Oeil du cyclone"] = "Spell_Shadow_SoulLeech_2",
  ["Oeil de la bête"] = "Ability_EyeOfTheOwl",
  ["Oubli"] = "Spell_Magic_LesserInvisibilty",
  ["Lucioles"] = "Spell_Nature_FaerieFire",
  ["Lucioles (farouche)"] = "Spell_Nature_FaerieFire",
  ["Fanatisme"] = "Spell_Holy_Fanaticism",
  ["Double vue"] = "Spell_Nature_FarSight",
  ["Liaison fatale"] = "Spell_Shadow_Shadowfury",
  ["Peur"] = "Spell_Shadow_Possession",
  ["Gardien de peur"] = "Spell_Holy_Excorcism",
  ["Nourrir le familier"] = "Ability_Hunter_BeastTraining",
  ["Effet Nourrir le familier"] = "Ability_Hunter_BeastTraining",
  ["Réaction"] = "Spell_Shadow_RitualOfSacrifice",
  ["Feindre la mort"] = "Ability_Rogue_FeignDeath",
  ["Feinte"] = "Ability_Rogue_Feint",
  ["Gangrarmure"] = "Spell_Shadow_FelArmour",
  ["Concentration corrompue"] = "Spell_Shadow_FingerOfDeath",
  ["Domination corrompue"] = "Spell_Nature_RemoveCurse",
  ["Energie corrompue"] = "Spell_Shadow_PsychicScream",
  ["Intelligence corrompue"] = "Spell_Holy_MagicalSentry",
  ["Gangrerage"] = "Spell_Fire_ElementalDevastation",
  ["Endurance corrompue"] = "Spell_Shadow_AntiShadow",
  ["Gangrefeu"] = "Spell_Fire_Fireball",
  ["Grâce féline"] = "INV_Feather_01",
  ["Agressivité farouche"] = "Ability_Druid_DemoralizingRoar",
  ["Charge farouche"] = "Ability_Hunter_Pet_Bear",
  ["Instinct farouche"] = "Ability_Ambush",
  ["Célérité farouche"] = "Spell_Nature_SpiritWolf",
  ["Morsure féroce"] = "Ability_Druid_FerociousBite",
  ["Inspiration féroce"] = "Ability_Hunter_FerociousInspiration",
  ["Ferocité"] = "INV_Misc_MonsterClaw_04",
  ["Fétiche"] = "INV_Misc_Horn_01",
  ["Découverte d'herbes"] = "INV_Misc_Flower_02",
  ["Découverte de gisements"] = "Spell_Nature_Earthquake",
  ["Découverte de trésors"] = "Racial_Dwarf_FindTreasure",
  ["Découverte des faiblesses"] = "Ability_Rogue_FindWeakness",
  ["Trait de feu"] = "Spell_Fire_Fireball",
  ["Souffle de feu"] = "Spell_Fire_Burnout",
  ["Totem élémentaire de feu"] = "Spell_Fire_Elemental_Totem",
  ["Totem Nova de feu"] = "Spell_Fire_SealOfFire",
  ["Puissance du feu"] = "Spell_Fire_Immolation",
  ["Résistance au Feu"] = "Spell_Fire_FireArmor",
  ["Aura de résistance au Feu"] = "Spell_Fire_SealOfFire",
  ["Totem de résistance au Feu"] = "Spell_FireResistanceTotem_01",
  ["Bouclier de feu"] = "Spell_Fire_FireArmor",
  ["Vulnérabilité au Feu"] = "Spell_Fire_SoulBurn",
  ["Gardien de feu"] = "Spell_Fire_FireArmor",
  ["Sensibilité au Feu"] = "INV_Misc_QirajiCrystal_02",
  ["Boule de feu"] = "Spell_Fire_FlameBolt",
  ["Eclair de feu"] = "Spell_Fire_FireBolt",
  ["Premiers soins"] = "Spell_Holy_SealOfSacrifice",
  ["Pêche"] = "Trade_Fishing",
  ["Cannes à pêche"] = "Trade_Fishing",
  ["Spécialisation Arme de pugilat"] = "INV_Gauntlets_04",
  ["Armes de pugilat"] = "INV_Gauntlets_04",
  ["Horion de flammes"] = "Spell_Fire_FlameShock",
  ["Jet de flammes"] = "Spell_Fire_Flare",
  ["Choc de flammes"] = "Spell_Fire_SelfDestruct",
  ["Lance-flammes"] = "Spell_Fire_Incinerate",
  ["Attaque Langue de feu"] = "Spell_Fire_FlameTounge",
  ["Totem Langue de feu"] = "Spell_Nature_GuardianWard",
  ["Arme Langue de feu"] = "Spell_Fire_FlameTounge",
  ["Fusée éclairante"] = "Spell_Fire_Flare",
  ["Soins rapides"] = "Spell_Holy_FlashHeal",
  ["Eclair lumineux"] = "Spell_Holy_FlashHeal",
  ["Forme de vol"] = "Ability_Druid_FlightForm",
  ["Rafale"] = "Ability_GhoulFrenzy",
  ["Incantation focalisée"] = "Spell_Arcane_Blink",
  ["Esprit focalisé"] = "Spell_Nature_FocusedMind",
  ["Puissance focalisée"] = "Spell_Shadow_FocusedPower",
  ["Rage focalisée"] = "Ability_Warrior_FocusedRage",
  ["Lumière stellaire focalisée"] = "INV_Staff_01",
  ["Nourriture"] = "INV_Misc_Fork&Knife",
  ["Longanimité"] = "Spell_Holy_RemoveCurse",
  ["Force de la nature"] = "Ability_Druid_ForceofNature",
  ["Force de volonté"] = "Spell_Nature_SlowingTotem",
  ["Piège givrant"] = "Spell_Frost_ChainsOfIce",
  ["Effet Piège givrant"] = "Spell_Frost_ChainsOfIce",
  ["Régénération frénétique"] = "Ability_BullRush",
  ["Frénésie"] = "INV_Misc_MonsterClaw_03",
  ["Effet de frénésie"] = "INV_Misc_MonsterClaw_03",
  ["Armure de givre"] = "Spell_Frost_FrostArmor02",
  ["Trait de givre"] = "Spell_Frost_FrostBolt02",
  ["Canalisation du givre"] = "Spell_Frost_Stun",
  ["Nova de givre"] = "Spell_Frost_FrostNova",
  ["Résistance au Givre"] = "Spell_Frost_FrostWard",
  ["Aura de résistance au Givre"] = "Spell_Frost_WizardMark",
  ["Totem de résistance au Givre"] = "Spell_FrostResistanceTotem_01",
  ["Horion de givre"] = "Spell_Frost_FrostShock",
  ["Piège de givre"] = "Spell_Frost_FreezingBreath",
  ["Aura Piège de givre"] = "Spell_Frost_FrostNova",
  ["Gardien de givre"] = "Spell_Frost_FrostWard",
  ["Protection contre le Givre"] = "Spell_Frost_FrostWard",
  ["Sensibilité au Givre"] = "INV_Misc_QirajiCrystal_04",
  ["Morsure de givre"] = "Spell_Frost_FrostArmor",
  ["Eclair de givre"] = "Spell_Frost_FrostBolt02",
  ["Attaque Arme de givre"] = "Spell_Frost_FrostBrand",
  ["Arme de givre"] = "Spell_Frost_FrostBrand",
  ["Cœur de gel"] = "Spell_Frost_FrozenCore",
  ["Hurlement furieux"] = "Ability_Hunter_Pet_Wolf",
  ["Fureur"] = "Spell_Holy_BlessingOfStamina",
  ["Fureur des déferlantes"] = "Spell_Nature_UnrelentingStorm",
  ["Garrot"] = "Ability_Rogue_Garrote",
  ["Garrot - Silence"] = "Ability_Rogue_Garrote",
  ["Loup fantôme"] = "Spell_Nature_SpiritWolf",
  ["Frappe fantomatique"] = "Spell_Shadow_Curse",
  ["Don de vie"] = "INV_Misc_Gem_Pearl_05",
  ["Don de la Nature"] = "Spell_Nature_ProtectionformNature",
  ["Don des naaru"] = "Spell_Holy_HolyProtection",
  ["Don du fauve"] = "Spell_Nature_Regeneration",
  ["Suriner"] = "Ability_Gouge",
  ["Grâce aérienne"] = "Spell_Nature_InvisibilityTotem",
  ["Totem de Grâce aérienne"] = "Spell_Nature_InvisibilityTotem",
  ["Endurance supérieure"] = "Spell_Nature_UnyeildingStamina",
  ["Bénédiction des rois supérieure"] = "Spell_Magic_GreaterBlessingofKings",
  ["Bénédiction de lumière supérieure"] = "Spell_Holy_GreaterBlessingofLight",
  ["Bénédiction de puissance supérieure"] = "Spell_Holy_GreaterBlessingofKings",
  ["Bénédiction de salut supérieure"] = "Spell_Holy_GreaterBlessingofSalvation",
  ["Bénédiction du sanctuaire supérieure"] = "Spell_Holy_GreaterBlessingofSanctuary",
  ["Bénédiction de sagesse supérieure"] = "Spell_Holy_GreaterBlessingofWisdom",
  ["Sommeil sans rêve supérieur"] = "Spell_Nature_Sleep",
  ["Soins supérieurs"] = "Spell_Holy_GreaterHeal",
  ["Lancer effroyable"] = "Ability_BackStab",
  ["Allonge sinistre"] = "Spell_Shadow_CallofBone",
  ["Totem de Glèbe"] = "Spell_Nature_GroundingTotem",
  ["Effet Totem de Glèbe"] = "Spell_Nature_GroundingTotem",
  ["Ramper"] = "Temp",
  ["Grondement"] = "Ability_Physical_Taunt",
  ["Faveur du Gardien"] = "Spell_Holy_SealOfProtection",
  ["Spécialisation Armes à feu"] = "INV_Musket_03",
  ["Armes à feu"] = "INV_Weapon_Rifle_01",
  ["Marteau de la justice"] = "Spell_Holy_SealOfMight",
  ["Marteau de courroux"] = "Ability_ThunderClap",
  ["Brise-genou"] = "Ability_ShockWave",
  ["Harcèlement"] = "Ability_Hunter_Harass",
  ["Solidité"] = "INV_Helmet_23",
  ["Hâte"] = "INV_Potion_108",
  ["Oeil de faucon"] = "Ability_TownWatch",
  ["Soins"] = "Spell_Holy_Heal",
  ["Focalisation des soins"] = "Spell_Holy_HealingFocus",
  ["Lumière guérisseuse"] = "Spell_Holy_HolyBolt",
  ["Guérisseur"] = "INV_Spear_04",
  ["Totem guérisseur"] = "INV_Spear_04",
  ["Toucher guérisseur"] = "Spell_Nature_HealingTouch",
  ["Vague de soins"] = "Spell_Nature_MagicImmunity",
  ["Flots de soins"] = "Spell_Nature_HealingWay",
  ["Captation de vie"] = "Spell_Shadow_LifeDrain",
  ["Cœur de fauve"] = "Spell_Holy_BlessingOfAgility",
  ["Pierre à aiguiser lourde"] = "INV_Stone_SharpeningStone_03",
  ["Flammes infernales"] = "Spell_Fire_Incinerate",
  ["Effet Flammes infernales"] = "Spell_Fire_Incinerate",
  ["Supériorité des Flammes infernales"] = "INV_BannerPVP_02",
  ["Hémorragie"] = "Spell_Shadow_LifeDrain",
  ["Cueillette"] = "Spell_Nature_NatureTouchGrow",
  ["Herboristerie"] = "Spell_Nature_NatureTouchGrow",
  ["Frappe héroïque"] = "Ability_Rogue_Ambush",
  ["Héroïsme"] = "Ability_Shaman_Heroism",
  ["Maléfice de faiblesse"] = "Spell_Shadow_FingerOfDeath",
  ["Hibernation"] = "Spell_Nature_Sleep",
  ["Flammes sacrées"] = "Spell_Holy_SearingLight",
  ["Lumière sacrée"] = "Spell_Holy_HolyBolt",
  ["Nova sacrée"] = "Spell_Holy_HolyNova",
  ["Puissance sacrée"] = "Spell_Holy_Power",
  ["Allonge du Sacré"] = "Spell_Holy_Purify",
  ["Bouclier sacré"] = "Spell_Holy_BlessingOfProtection",
  ["Horion sacré"] = "Spell_Holy_SearingLight",
  ["Spécialisation"] = "Spell_Holy_SealOfSalvation",
  ["Colère divine"] = "Spell_Holy_Excorcism",
  ["Cible sans honneur"] = "Spell_Magic_LesserInvisibilty",
  ["Equitation"] = "Spell_Nature_Swiftness",
  ["Hurlement de terreur"] = "Spell_Shadow_DeathScream",
  ["Tueur d'humanoïdes"] = "Spell_Holy_PrayerOfHealing",
  ["Marque du chasseur"] = "Ability_Hunter_SniperShot",
  ["Ouragan"] = "Spell_Nature_Cyclone",
  ["Hypothermie"] = "Spell_Fire_BlueImmolation",
  ["Armure de glace"] = "Spell_Frost_FrostArmor02",
  ["Barrière de glace"] = "Spell_Ice_Lament",
  ["Bloc de glace"] = "Spell_Frost_Frost",
  ["Iceberg"] = "Spell_Frost_IceFloes",
  ["Javelot de glace"] = "Spell_Frost_FrostBlast",
  ["Eclats de glace"] = "Spell_Frost_IceShard",
  ["Enflammer"] = "Spell_Fire_Incinerate",
  ["Enflammer le mana"] = "Spell_Fire_Incinerate",
  ["Illumination"] = "Spell_Holy_GreaterHeal",
  ["Immolation"] = "Spell_Fire_Immolation",
  ["Piège d'immolation"] = "Spell_Fire_FlameShock",
  ["Effet de Piège d'immolation"] = "Spell_Fire_FlameShock",
  ["Impact"] = "Spell_Fire_MeteorStorm",
  ["Empaler"] = "Ability_SearingArrow",
  ["Epine de perforation"] = "Spell_Frost_IceShard",
  ["Embuscade améliorée"] = "Ability_Rogue_Ambush",
  ["Projectiles des arcanes améliorés"] = "Spell_Nature_StarFall",
  ["Tir des arcanes amélioré"] = "Ability_ImpalingBolt",
  ["Aspect du faucon amélioré"] = "Spell_Nature_RavenForm",
  ["Aspect du singe amélioré"] = "Ability_Hunter_AspectOfTheMonkey",
  ["Attaque sournoise améliorée"] = "Ability_BackStab",
  ["Barrage amélioré"] = "Ability_UpgradeMoonGlaive",
  ["Rage berserker améliorée"] = "Spell_Nature_AncestralGuardian",
  ["Bénédiction de puissance améliorée"] = "Spell_Holy_FistOfJustice",
  ["Bénédiction de sagesse améliorée"] = "Spell_Holy_SealOfWisdom",
  ["Transfert amélioré"] = "Spell_Arcane_Blink",
  ["Blizzard amélioré"] = "Spell_Frost_IceStorm",
  ["Rage sanguinaire améliorée"] = "Ability_Racial_BloodRage",
  ["Salve de guérison améliorée"] = "Spell_Nature_HealingWaveGreater",
  ["Chaîne d'éclairs améliorée"] = "Spell_Nature_ChainLightning",
  ["Cri de défi amélioré"] = "Ability_Warrior_Challange",
  ["Charge améliorée"] = "Ability_Warrior_Charge",
  ["Enchaînement amélioré"] = "Ability_Warrior_Cleave",
  ["Aura de concentration améliorée"] = "Spell_Holy_MindSooth",
  ["Trait de choc amélioré"] = "Spell_Frost_Stun",
  ["Cône de froid amélioré"] = "Spell_Frost_Glacier",
  ["Corruption améliorée"] = "Spell_Shadow_AbominationExplosion",
  ["Contresort amélioré"] = "Spell_Frost_IceShock",
  ["Malédiction d'agonie améliorée"] = "Spell_Shadow_CurseOfSargeras",
  ["Malédiction de faiblesse améliorée"] = "Spell_Shadow_CurseOfMannoroth",
  ["Cri démoralisant amélioré"] = "Ability_Warrior_WarCry",
  ["Aura de dévotion améliorée"] = "Spell_Holy_DevotionAura",
  ["Désarmement amélioré"] = "Ability_Warrior_Disarm",
  ["Drain de vie amélioré"] = "Spell_Shadow_LifeDrain02",
  ["Siphon d'âme amélioré"] = "Spell_Shadow_Haunting",
  ["Enrager amélioré"] = "Ability_Druid_Enrage",
  ["Asservir démon amélioré"] = "Spell_Shadow_EnslaveDemon",
  ["Eviscération améliorée"] = "Ability_Rogue_Eviscerate",
  ["Exécution améliorée"] = "INV_Sword_48",
  ["Exposer l'armure amélioré"] = "Ability_Warrior_Riposte",
  ["Oeil de la bête amélioré"] = "Ability_EyeOfTheOwl",
  ["Oubli amélioré"] = "Spell_Magic_LesserInvisibilty",
  ["Feindre la mort amélioré"] = "Ability_Rogue_FeignDeath",
  ["Trait de feu amélioré"] = "Spell_Fire_Fireball",
  ["Totems de Feu améliorés"] = "Spell_Fire_SealOfFire",
  ["Gardien de feu amélioré"] = "Spell_Fire_FireArmor",
  ["Boule de feu améliorée"] = "Spell_Fire_FlameBolt",
  ["Eclair de feu amélioré"] = "Spell_Fire_FireBolt",
  ["Choc de flammes amélioré"] = "Spell_Fire_SelfDestruct",
  ["Eclair lumineux amélioré"] = "Spell_Holy_FlashHeal",
  ["Nova de givre améliorée"] = "Spell_Frost_FreezingBreath",
  ["Eclair de givre amélioré"] = "Spell_Frost_FrostBolt02",
  ["Loup fantôme amélioré"] = "Spell_Nature_SpiritWolf",
  ["Suriner amélioré"] = "Ability_Gouge",
  ["Marteau de la justice amélioré"] = "Spell_Holy_SealOfMight",
  ["Brise-genou amélioré"] = "Ability_ShockWave",
  ["Soin amélioré"] = "Spell_Holy_Heal02",
  ["Toucher guérisseur amélioré"] = "Spell_Nature_HealingTouch",
  ["Vague de soins améliorée"] = "Spell_Nature_MagicImmunity",
  ["Captation de vie améliorée"] = "Spell_Shadow_LifeDrain",
  ["Pierre de soins améliorée"] = "INV_Stone_04",
  ["Frappe héroïque améliorée"] = "Ability_Rogue_Ambush",
  ["Bouclier sacré amélioré"] = "Spell_Holy_BlessingOfProtection",
  ["Hurlement de terreur amélioré"] = "Spell_Shadow_DeathScream",
  ["Marque du chasseur améliorée"] = "Ability_Hunter_SniperShot",
  ["Immolation améliorée"] = "Spell_Fire_Immolation",
  ["Diablotin amélioré"] = "Spell_Shadow_SummonImp",
  ["Feu intérieur amélioré"] = "Spell_Holy_InnerFire",
  ["Interception améliorée"] = "Ability_Rogue_Sprint",
  ["Cri d’intimidation amélioré"] = "Ability_GolemThunderClap",
  ["Jugement amélioré"] = "Spell_Holy_RighteousFury",
  ["Coup de pied amélioré"] = "Ability_Kick",
  ["Aiguillon perfide amélioré"] = "Ability_Rogue_KidneyShot",
  ["Fouet de la douleur amélioré"] = "Spell_Shadow_Curse",
  ["Imposition des mains améliorée"] = "Spell_Holy_LayOnHands",
  ["Chef de la meute amélioré"] = "Spell_Nature_UnyeildingStamina",
  ["Connexion améliorée"] = "Spell_Shadow_BurningSpirit",
  ["Eclair amélioré"] = "Spell_Nature_Lightning",
  ["Bouclier de foudre amélioré"] = "Spell_Nature_LightningShield",
  ["Totem de Magma amélioré"] = "Spell_Fire_SelfDestruct",
  ["Brûlure de mana améliorée"] = "Spell_Shadow_ManaBurn",
  ["Bouclier de mana amélioré"] = "Spell_Shadow_DetectLesserInvisibility",
  ["Totem Fontaine de mana amélioré"] = "Spell_Nature_ManaRegenTotem",
  ["Marque du fauve améliorée"] = "Spell_Nature_Regeneration",
  ["Guérison du familier améliorée"] = "Ability_Hunter_MendPet",
  ["Attaque mentale améliorée"] = "Spell_Shadow_UnholyFrenzy",
  ["Eclat lunaire amélioré"] = "Spell_Nature_StarFall",
  ["Frappe mortelle améliorée"] = "Ability_Warrior_SavageBlow",
  ["Emprise de la nature améliorée"] = "Spell_Nature_NaturesWrath",
  ["Fulgurance améliorée"] = "INV_Sword_05",
  ["Mot de pouvoir : Robustesse amélioré"] = "Spell_Holy_WordFortitude",
  ["Mot de pouvoir : Bouclier amélioré"] = "Spell_Holy_PowerWordShield",
  ["Prière de soins améliorée"] = "Spell_Holy_PrayerOfHealing02",
  ["Cri psychique amélioré"] = "Spell_Shadow_PsychicScream",
  ["Rétablissement amélioré"] = "Spell_Nature_ResistNature",
  ["Réincarnation améliorée"] = "Spell_Nature_Reincarnation",
  ["Récupération améliorée"] = "Spell_Nature_Rejuvenation",
  ["Pourfendre amélioré"] = "Ability_Gouge",
  ["Rénovation améliorée"] = "Spell_Holy_Renew",
  ["Aura de vindicte améliorée"] = "Spell_Holy_AuraOfLight",
  ["Vengeance améliorée"] = "Ability_Warrior_Revenge",
  ["Ressusciter le familier amélioré"] = "Ability_Hunter_BeastSoothe",
  ["Fureur vertueuse améliorée"] = "Spell_Holy_SealOfFury",
  ["Rupture améliorée"] = "Ability_Rogue_Rupture",
  ["Brûlure améliorée"] = "Spell_Fire_SoulBurn",
  ["Piqûre de scorpide améliorée"] = "Ability_Hunter_CriticalShot",
  ["Sceau de piété amélioré"] = "Ability_ThunderBolt",
  ["Sceau du Croisé amélioré"] = "Spell_Holy_HolySmite",
  ["Douleur brûlante améliorée"] = "Spell_Fire_SoulBurn",
  ["Morsure de serpent améliorée"] = "Ability_Hunter_Quickshot",
  ["Trait de l'ombre amélioré"] = "Spell_Shadow_ShadowBolt",
  ["Mot de l'ombre : Douleur amélioré"] = "Spell_Shadow_ShadowWordPain",
  ["Coup de bouclier amélioré"] = "Ability_Warrior_ShieldBash",
  ["Maîtrise du blocage améliorée"] = "Ability_Defend",
  ["Mur protecteur amélioré"] = "Ability_Warrior_ShieldWall",
  ["Attaque pernicieuse améliorée"] = "Spell_Shadow_RitualOfSacrifice",
  ["Heurtoir amélioré"] = "Ability_Warrior_DecisiveStrike",
  ["Débiter amélioré"] = "Ability_Rogue_SliceDice",
  ["Sprint amélioré"] = "Ability_Rogue_Sprint",
  ["Morsures et piqûres améliorées"] = "Ability_Hunter_Quickshot",
  ["Succube améliorée"] = "Spell_Shadow_SummonSuccubus",
  ["Fracasser armure amélioré"] = "Ability_Warrior_Sunder",
  ["Provocation améliorée"] = "Spell_Nature_Reincarnation",
  ["Coup de tonnerre amélioré"] = "Ability_ThunderClap",
  ["Tranquillité améliorée"] = "Spell_Nature_Tranquility",
  ["Etreinte vampirique améliorée"] = "Spell_Shadow_ImprovedVampiricEmbrace",
  ["Disparition améliorée"] = "Ability_Vanish",
  ["Marcheur du Vide amélioré"] = "Spell_Shadow_SummonVoidWalker",
  ["Totems d'armes améliorés"] = "Spell_Fire_EnchantWeapon",
  ["Tourbillon amélioré"] = "Ability_Whirlwind",
  ["Coupure d'ailes améliorée"] = "Ability_Rogue_Trip",
  ["Incinérer"] = "Spell_Fire_Burnout",
  ["Incinération"] = "Spell_Fire_FlameShock",
  ["Inferno"] = "Spell_Shadow_SummonInfernal",
  ["Initiative"] = "Spell_Shadow_Fumble",
  ["Feu intérieur"] = "Spell_Holy_InnerFire",
  ["Focalisation améliorée"] = "Spell_Frost_WindWalkOn",
  ["Innervation"] = "Spell_Nature_Lightning",
  ["Essaim d'insectes"] = "Spell_Nature_InsectSwarm",
  ["Murmure insidieux"] = "Spell_Shadow_ManaFeed",
  ["Insignifiance"] = "Ability_Hibernation",
  ["Inspiration"] = "Spell_Holy_LayOnHands",
  ["Poison instantané"] = "Ability_Poisons",
  ["Poison instantané II"] = "Ability_Poisons",
  ["Poison instantané III"] = "Ability_Poisons",
  ["Poison instantané IV"] = "Ability_Poisons",
  ["Poison instantané V"] = "Ability_Poisons",
  ["Poison instantané VI"] = "Ability_Poisons",
  ["Poison instantané VII"] = "Ability_Poisons",
  ["Intensité"] = "Spell_Fire_LavaSpawn",
  ["Interception"] = "Ability_Rogue_Sprint",
  ["Interception étourdissante"] = "Spell_Frost_Stun",
  ["Intervention"] = "Ability_Warrior_VictoryRush",
  ["Cri d’intimidation"] = "Ability_GolemThunderClap",
  ["Intimidation"] = "Ability_Devour",
  ["Invisibilité"] = "Ability_Mage_Invisibility",
  ["Volonté de fer"] = "Spell_Magic_MageArmor",
  ["Joaillerie"] = "INV_Misc_Gem_02",
  ["Jom Gabbar"] = "INV_Misc_EngGizmos_19",
  ["Jugement"] = "Spell_Holy_RighteousFury",
  ["Jugement de sang"] = "Spell_Shadow_LifeDrain",
  ["Jugement d'autorité"] = "Ability_Warrior_InnerRage",
  ["Jugement de justice"] = "Spell_Holy_SealOfWrath",
  ["Jugement de lumière"] = "Spell_Holy_HealingAura",
  ["Jugement de vengeance"] = "Ability_Warrior_InnerRage",
  ["Jugement de piété"] = "Ability_ThunderBolt",
  ["Jugement de sagesse"] = "Spell_Holy_RighteousnessAura",
  ["Jugement du Croisé"] = "Spell_Holy_HolySmite",
  ["Coup de pied"] = "Ability_Kick",
  ["Coup de pied - Silencieux"] = "Ability_Kick",
  ["Aiguillon perfide"] = "Ability_Rogue_KidneyShot",
  ["Ordre de tuer"] = "Ability_Hunter_KillCommand",
  ["Instinct du tueur"] = "Spell_Holy_BlessingOfStamina",
  ["Baiser de l'araignée"] = "INV_Trinket_Naxxramas04",
  ["Monte de kodo"] = "Spell_Nature_Swiftness",
  ["Lacérer"] = "Ability_Druid_Lacerate",
  ["Fouet de la douleur"] = "Spell_Shadow_Curse",
  ["Dernier rempart"] = "Spell_Holy_AshesToAshes",
  ["Imposition des mains"] = "Spell_Holy_LayOnHands",
  ["Chef de la meute"] = "Spell_Nature_UnyeildingStamina",
  ["Cuir"] = "INV_Chest_Leather_09",
  ["Travail du cuir"] = "INV_Misc_ArmorKit_17",
  ["Soins inférieurs"] = "Spell_Holy_LesserHeal",
  ["Vague de soins inférieurs"] = "Spell_Nature_HealingWaveLesser",
  ["Invisibilité inférieure"] = "Spell_Magic_LesserInvisibilty",
  ["Coups fatals"] = "Ability_SearingArrow",
  ["Mortalité"] = "Ability_CriticalStrike",
  ["Lévitation"] = "Spell_Holy_LayOnHands",
  ["Libram"] = "INV_Misc_Book_11",
  ["Connexion"] = "Spell_Shadow_BurningSpirit",
  ["Fleur de vie"] = "INV_Misc_Herb_Felblossom",
  ["Grâce de la lumière"] = "Spell_Holy_LightsGrace",
  ["Eclair"] = "Spell_Nature_Lightning",
  ["Souffle de foudre"] = "Spell_Nature_Lightning",
  ["Maîtrise de la foudre"] = "Spell_Lightning_LightningBolt01",
  ["Réflexes éclairs"] = "Spell_Nature_Invisibilty",
  ["Bouclier de foudre"] = "Spell_Nature_LightningShield",
  ["Vitesse de l'éclair"] = "Spell_Nature_UnrelentingStorm",
  ["Puits de lumière"] = "Spell_Holy_SummonLightwell",
  ["Rénovation du Puits de lumière"] = "Spell_Holy_SummonLightwell",
  ["Crochetage"] = "Spell_Nature_MoonKey",
  ["Hébétement prolongé"] = "Spell_Frost_Stun",
  ["Soif de bataille"] = "Spell_Shadow_DeathPact",
  ["Spécialisation Masse"] = "INV_Mace_01",
  ["Effet étourdissant de la masse"] = "Spell_Frost_Stun",
  ["Armure du mage"] = "Spell_MageArmor",
  ["Absorption de magie"] = "Spell_Nature_AstralRecalGroup",
  ["Harmonisation de la magie"] = "Spell_Nature_AbolishMagic",
  ["Totem de Magma"] = "Spell_Fire_SelfDestruct",
  ["Mailles"] = "INV_Chest_Chain_05",
  ["Estropier"] = "Ability_Druid_Mangle",
  ["Sommeil sans rêve majeur"] = "Spell_Nature_Sleep",
  ["Imprécation"] = "Spell_Shadow_CurseOfAchimonde",
  ["Malice"] = "Ability_Racial_BloodRage",
  ["Brûlure de mana"] = "Spell_Shadow_ManaBurn",
  ["Festin de mana"] = "Spell_Shadow_ManaFeed",
  ["Bouclier de mana"] = "Spell_Shadow_DetectLesserInvisibility",
  ["Fontaine de mana"] = "Spell_Nature_ManaRegenTotem",
  ["Totem Fontaine de mana"] = "Spell_Nature_ManaRegenTotem",
  ["Ponction de mana"] = "Spell_Arcane_ManaTap",
  ["Vague de mana"] = "Spell_Frost_SummonWaterElemental",
  ["Totem de Vague de mana"] = "Spell_Frost_SummonWaterElemental",
  ["Mutilation"] = "Ability_Druid_Mangle",
  ["Mutilation (ours)"] = "Ability_Druid_Mangle2",
  ["Mutilation (félin)"] = "Ability_Druid_Mangle2",
  ["Marque du fauve"] = "Spell_Nature_Regeneration",
  ["Martyre"] = "Spell_Nature_Tranquility",
  ["Dissipation de masse"] = "Spell_Arcane_MassDispel",
  ["Maître conjurateur"] = "INV_Ammo_FireTar",
  ["Maître démonologue"] = "Spell_Shadow_ShadowPact",
  ["Maître invocateur"] = "Spell_Shadow_ImpPhaseShift",
  ["Maître tacticien"] = "Ability_Hunter_MasterTactitian",
  ["Maître des illusions"] = "Spell_Shadow_Charm",
  ["Maître des éléments"] = "Spell_Fire_MasterOfElements",
  ["Maître de la discrétion"] = "Ability_Rogue_MasterOfSubtlety",
  ["Mutiler"] = "Ability_Druid_Maul",
  ["Pilotage de mécanotrotteur"] = "Spell_Nature_Swiftness",
  ["Méditation"] = "Spell_Nature_Sleep",
  ["Spécialisation Mêlée"] = "INV_Axe_02",
  ["Fondre armure"] = "Spell_Fire_Immolation",
  ["Guérison du familier"] = "Ability_Hunter_MendPet",
  ["Sagacité"] = "Ability_Hibernation",
  ["Force mentale"] = "Spell_Nature_EnchantArmor",
  ["Attaque mentale"] = "Spell_Shadow_UnholyFrenzy",
  ["Contrôle mental"] = "Spell_Shadow_ShadowWordDominate",
  ["Fouet mental"] = "Spell_Shadow_SiphonMana",
  ["Maîtrise mentale"] = "Spell_Arcane_MindMastery",
  ["Apaisement"] = "Spell_Holy_MindSooth",
  ["Vision télépathique"] = "Spell_Holy_MindVision",
  ["Poison de distraction mentale"] = "Spell_Nature_NullifyDisease",
  ["Poison de distraction mentale II"] = "Spell_Nature_NullifyDisease",
  ["Poison de distraction mentale III"] = "Spell_Nature_NullifyDisease",
  ["Minage"] = "Spell_Fire_FlameBlades",
  ["Détournement"] = "Ability_Hunter_Misdirection",
  ["Misère"] = "Spell_Shadow_Misery",
  ["Coup railleur"] = "Ability_Warrior_PunishingBlow",
  ["Armure de la fournaise"] = "Ability_Mage_MoltenArmor",
  ["Sang de la fournaise"] = "Spell_Fire_MoltenBlood",
  ["Morsure de la mangouste"] = "Ability_Hunter_SwiftStrike",
  ["Tueur de monstres"] = "INV_Misc_Head_Dragon_Black",
  ["Eclat lunaire"] = "Spell_Nature_StarFall",
  ["Fureur lunaire"] = "Spell_Nature_MoonGlow",
  ["Lueur de la lune"] = "Spell_Nature_Sentinal",
  ["Aura de sélénien"] = "Spell_Nature_MoonGlow",
  ["Forme de sélénien"] = "Spell_Nature_ForceOfNature",
  ["Coups mortels"] = "Ability_PierceDamage",
  ["Frappe mortelle"] = "Ability_Warrior_SavageBlow",
  ["Flèches multiples"] = "Ability_UpgradeMoonGlaive",
  ["Meurtre"] = "Spell_Shadow_DeathScream",
  ["Estropier"] = "Ability_Rogue_ShadowStrikes",
  ["Armure naturelle"] = "Spell_Nature_SpiritArmor",
  ["Perfection naturelle"] = "Ability_Druid_NaturalPerfection",
  ["Changeforme naturel"] = "Spell_Nature_WispSplode",
  ["Naturaliste"] = "Spell_Nature_HealingTouch",
  ["Résistance à la Nature"] = "Spell_Nature_ResistNature",
  ["Totem de résistance à la Nature"] = "Spell_Nature_NatureResistanceTotem",
  ["Sensibilité à la Nature"] = "INV_Misc_QirajiCrystal_03",
  ["Focalisation de la nature"] = "Spell_Nature_HealingWaveGreater",
  ["Grâce de la nature"] = "Spell_Nature_NaturesBlessing",
  ["Emprise de la nature"] = "Spell_Nature_NaturesWrath",
  ["Soutien de la nature"] = "Spell_Frost_Stun",
  ["Allonge de la Nature"] = "Spell_Nature_NatureTouchGrow",
  ["Rapidité de la nature"] = "Spell_Nature_RavenForm",
  ["Charge négative"] = "Spell_ChargeNegative",
  ["Protection du Néant"] = "Spell_Shadow_NetherProtection",
  ["Drapeau de Raz-de-Néant"] = "INV_Misc_Summerfest_BrazierGreen",
  ["Crépuscule"] = "Spell_Shadow_Twilight",
  ["Instinct Nourricier"] = "Ability_Druid_HealingInstincts",
  ["Augure de clarté"] = "Spell_Nature_CrystalBall",
  ["Haches à une main"] = "INV_Axe_01",
  ["Masses à une main"] = "INV_Mace_01",
  ["Epées à une main"] = "Ability_MeleeDamage",
  ["Spécialisation Arme 1M"] = "INV_Sword_20",
  ["Ouverture"] = "Temp",
  ["Ouverture - pas de texte"] = "Temp",
  ["Opportunité"] = "Ability_Warrior_WarCry",
  ["Fulgurance"] = "Ability_MeleeDamage",
  ["Suppression de la douleur"] = "Spell_Holy_PainSupression",
  ["Panique"] = "INV_Misc_Drum_06",
  ["Paranoïa"] = "Spell_Shadow_AuraOfDarkness",
  ["Ombrefiel parasite"] = "Spell_Shadow_SoulLeech_3",
  ["Parade"] = "Ability_Parry",
  ["Science des chemins"] = "Ability_Mount_JungleTiger",
  ["Faiblesse perçue"] = "Spell_Holy_ArcaneIntellect",
  ["Perception"] = "Spell_Nature_Sleep",
  ["Gel prolongé"] = "Spell_Frost_Wisp",
  ["Agressivité du familier"] = "Ability_Druid_Maul",
  ["Robustesse du familier"] = "Ability_BullRush",
  ["Rétablissement du familier"] = "Ability_Hibernation",
  ["Résistance du familier"] = "Spell_Holy_BlessingOfAgility",
  ["Changement de phase"] = "Spell_Shadow_ImpPhaseShift",
  ["Crochetage"] = "Spell_Nature_MoonKey",
  ["Vol à la tire"] = "INV_Misc_Bag_11",
  ["Hurlement perçant"] = "Spell_Shadow_DeathScream",
  ["Glace perçante"] = "Spell_Frost_Frostbolt",
  ["Armure en plaques"] = "INV_Chest_Plate01",
  ["Jouer avec le feu"] = "Spell_Fire_PlayingWithFire",
  ["Totem de Purification du poison"] = "Spell_Nature_PoisonCleansingTotem",
  ["Crachat de poison"] = "Spell_Nature_CorrosiveBreath",
  ["Poisons"] = "Trade_BrewPoison",
  ["Armes d'hast"] = "INV_Spear_06",
  ["Spécialisation Arme d'hast"] = "INV_Weapon_Halbard_01",
  ["Métamorphose"] = "Spell_Nature_Polymorph",
  ["Métamorphose : cochon"] = "Spell_Magic_PolymorphPig",
  ["Métamorphose : tortue"] = "Ability_Hunter_Pet_Turtle",
  ["Portail : Darnassus"] = "Spell_Arcane_PortalDarnassus",
  ["Portail : Exodar"] = "Spell_Arcane_PortalExodar",
  ["Portail : Forgefer"] = "Spell_Arcane_PortalIronForge",
  ["Portail : Orgrimmar"] = "Spell_Arcane_PortalOrgrimmar",
  ["Portail : Shattrath"] = "Spell_Arcane_PortalShattrath",
  ["Portail : Lune-d'argent"] = "Spell_Arcane_PortalSilvermoon",
  ["Portail : Hurlevent"] = "Spell_Arcane_PortalStormWind",
  ["Portail : les Pitons du Tonnerre"] = "Spell_Arcane_PortalThunderBluff",
  ["Portail : Fossoyeuse"] = "Spell_Arcane_PortalUnderCity",
  ["Charge positive"] = "Spell_ChargePositive",
  ["Traquenard"] = "Ability_Druid_SupriseAttack",
  ["Traquenard sanglant"] = "Ability_Druid_SupriseAttack",
  ["Infusion de puissance"] = "Spell_Holy_PowerInfusion",
  ["Mot de pouvoir : Robustesse"] = "Spell_Holy_WordFortitude",
  ["Mot de pouvoir : Bouclier"] = "Spell_Holy_PowerWordShield",
  ["Prière de robustesse"] = "Spell_Holy_PrayerOfFortitude",
  ["Prière de soins"] = "Spell_Holy_PrayerOfHealing02",
  ["Prière de guérison"] = "Spell_Holy_PrayerOfMendingtga",
  ["Prière de protection contre l'Ombre"] = "Spell_Holy_PrayerofShadowProtection",
  ["Prière d'Esprit"] = "Spell_Holy_PrayerofSpirit",
  ["Précision"] = "Ability_Marksmanship",
  ["Instincts de prédateur"] = "Ability_Druid_PredatoryInstincts",
  ["Frappes de prédateur"] = "Ability_Hunter_Pet_Cat",
  ["Préméditation"] = "Spell_Shadow_Possession",
  ["Préparation"] = "Spell_Shadow_AntiShadow",
  ["Présence spirituelle"] = "Spell_Nature_EnchantArmor",
  ["Fureur primitive"] = "Ability_Racial_Cannibalize",
  ["Cape prismatique"] = "Spell_Arcane_PrismaticCloak",
  ["Prospection"] = "INV_Misc_Gem_Bloodstone_01",
  ["Rôder"] = "Ability_Druid_SupriseAttack",
  ["Cri psychique"] = "Spell_Shadow_PsychicScream",
  ["Volée de coups"] = "INV_Gauntlets_04",
  ["Expiation"] = "Spell_Nature_Purge",
  ["Purification"] = "Spell_Frost_WizardMark",
  ["Purification"] = "Spell_Holy_Purify",
  ["Puissance purifiante"] = "Spell_Holy_PurifyingPower",
  ["Poursuite de la justice"] = "Spell_Holy_PersuitofJustice",
  ["Explosion pyrotechnique"] = "Spell_Fire_Fireball02",
  ["Pyroclasme"] = "Spell_Fire_Volcano",
  ["Pyromane"] = "Spell_Fire_Burnout",
  ["Rétablissement rapide"] = "Ability_Rogue_QuickRecovery",
  ["Tir rapide"] = "Ability_Warrior_InnerRage",
  ["Rapidité"] = "Ability_Racial_ShadowMeld",
  ["Rage du détrameur"] = "Racial_Orc_BerserkerStrength",
  ["Pluie de feu"] = "Spell_Shadow_RainOfFire",
  ["Griffure"] = "Ability_Druid_Disembowel",
  ["Monte de bélier"] = "Spell_Nature_Swiftness",
  ["Saccager"] = "Ability_Warrior_Rampage",
  ["Spécialisation Armes à distance"] = "INV_Weapon_Rifle_06",
  ["Tir rapide"] = "Ability_Hunter_RunningShot",
  ["Tueur rapide"] = "Ability_Hunter_RapidKilling",
  ["Monte de raptor"] = "Spell_Nature_Swiftness",
  ["Attaque du raptor"] = "Ability_MeleeDamage",
  ["Ravage"] = "Ability_Druid_Ravage",
  ["Promptitude"] = "Ability_Hunter_Readiness",
  ["Renaissance"] = "Spell_Nature_Reincarnation",
  ["Charge furieuse"] = "Spell_Nature_AstralRecal",
  ["Témérité"] = "Ability_CriticalStrike",
  ["Rétribution"] = "Spell_Holy_BlessingOfStrength",
  ["Rédemption"] = "Spell_Holy_Resurrection",
  ["Redoute"] = "Ability_Defend",
  ["Renvoi"] = "Spell_Frost_WindWalkOn",
  ["Régénération"] = "Spell_Nature_Regenerate",
  ["Rétablissement"] = "Spell_Nature_ResistNature",
  ["Réincarnation"] = "Spell_Nature_Reincarnation",
  ["Bouclier renforcé"] = "INV_Shield_06",
  ["Récupération"] = "Spell_Nature_Rejuvenation",
  ["Frappes implacables"] = "Ability_Warrior_DecisiveStrike",
  ["Impitoyable"] = "Ability_FiegnDead",
  ["Attaques impitoyables"] = "Ability_FiegnDead",
  ["Jouet à distance"] = "INV_Misc_Urn_01",
  ["Délivrance de la malédiction"] = "Spell_Holy_RemoveCurse",
  ["Enlever l'insigne"] = "Temp",
  ["Délivrance de la malédiction mineure"] = "Spell_Nature_RemoveCurse",
  ["Pourfendre"] = "Ability_Gouge",
  ["Rénovation"] = "Spell_Holy_Renew",
  ["Repentir"] = "Spell_Holy_PrayerOfHealing",
  ["Force inconstante"] = "Ability_CriticalStrike",
  ["Totems de restauration"] = "Spell_Nature_ManaRegenTotem",
  ["Résurrection"] = "Spell_Holy_Resurrection",
  ["Représailles"] = "Ability_Warrior_Challange",
  ["Aura de vindicte"] = "Spell_Holy_AuraOfLight",
  ["Vengeance"] = "Ability_Warrior_Revenge",
  ["Etourdissement vengeur"] = "Ability_Warrior_Revenge",
  ["Réverbération"] = "Spell_Frost_FrostWard",
  ["Ressusciter le familier"] = "Ability_Hunter_BeastSoothe",
  ["Défense vertueuse"] = "INV_Shoulder_37",
  ["Fureur vertueuse"] = "Spell_Holy_SealOfFury",
  ["Déchirure"] = "Ability_GhoulFrenzy",
  ["Riposte"] = "Ability_Warrior_Challange",
  ["Rituel de malédiction"] = "Spell_Shadow_AntiMagicShell",
  ["Effet Rituel de malédiction"] = "Spell_Arcane_PortalDarnassus",
  ["Rituel des rafraîchissements"] = "Spell_Arcane_MassDispel",
  ["Rituel des âmes"] = "Spell_Shadow_Shadesofdarkness",
  ["Rituel d'invocation"] = "Spell_Shadow_Twilight",
  ["Arme Croque-roc"] = "Spell_Nature_RockBiter",
  ["Pierre à aiguiser brute"] = "INV_Stone_SharpeningStone_01",
  ["Ruine"] = "Spell_Shadow_ShadowWordPain",
  ["Rupture"] = "Ability_Rogue_Rupture",
  ["Némésis"] = "Ability_Druid_Disembowel",
  ["Sacrifice"] = "Spell_Shadow_SacrificialShield",
  ["Chute amortie"] = "INV_Feather_01",
  ["Croisé sanctifié"] = "Spell_Holy_HolySmite",
  ["Jugement sanctifié"] = "Spell_Holy_RighteousFury",
  ["Lumière sanctifiée"] = "Spell_Holy_HealingAura",
  ["Aura de sainteté"] = "Spell_Holy_MindVision",
  ["Assommer"] = "Ability_Sap",
  ["Furie sauvage"] = "Ability_Druid_Ravage",
  ["Frappes sauvages"] = "Ability_Racial_BloodRage",
  ["Effrayer une bête"] = "Ability_Druid_Cower",
  ["Flèche de dispersion"] = "Ability_GolemStormBolt",
  ["Brûlure"] = "Spell_Fire_SoulBurn",
  ["Poison de scorpide"] = "Ability_PoisonSting",
  ["Piqûre de scorpide"] = "Ability_Hunter_CriticalShot",
  ["Hurlement"] = "Ability_Hunter_Pet_Bat",
  ["Scelle le destin"] = "Spell_Shadow_ChillTouch",
  ["Sceau de sang"] = "Spell_Holy_SealOfBlood",
  ["Sceau d'autorité"] = "Ability_Warrior_InnerRage",
  ["Sceau de justice"] = "Spell_Holy_SealOfWrath",
  ["Sceau de lumière"] = "Spell_Holy_HealingAura",
  ["Sceau de piété"] = "Ability_ThunderBolt",
  ["Sceau de vengeance"] = "Spell_Holy_SealOfVengeance",
  ["Sceau de sagesse"] = "Spell_Holy_RighteousnessAura",
  ["Sceau du Croisé"] = "Spell_Holy_HolySmite",
  ["Lumière incendiaire"] = "Spell_Holy_SearingLightPriest",
  ["Douleur brûlante"] = "Spell_Fire_SoulBurn",
  ["Totem incendiaire"] = "Spell_Fire_SearingTotem",
  ["Second souffle"] = "Ability_Hunter_Harass",
  ["Séduction"] = "Spell_Shadow_MindSteal",
  ["Graine de Corruption"] = "Spell_Shadow_SeedOfDestruction",
  ["Bouillant de rage"] = "Ability_Druid_ChallangingRoar",
  ["Détection des démons"] = "Spell_Shadow_Metamorphosis",
  ["Détection des morts-vivants"] = "Spell_Holy_SenseUndead",
  ["Totem Sentinelle"] = "Spell_Nature_RemoveCurse",
  ["Morsure de serpent"] = "Ability_Hunter_Quickshot",
  ["Lames dentelées"] = "INV_Sword_17",
  ["Préparatifs"] = "Spell_Nature_MirrorImage",
  ["Entraves des morts-vivants"] = "Spell_Nature_Slow",
  ["Affinité avec l'ombre"] = "Spell_Shadow_ShadowWard",
  ["Trait de l'ombre"] = "Spell_Shadow_ShadowBolt",
  ["Etreinte de l'ombre"] = "Spell_Shadow_ShadowEmbrace",
  ["Focalisation de l'ombre"] = "Spell_Shadow_BurningSpirit",
  ["Maîtrise de l'ombre"] = "Spell_Shadow_ShadeTrueSight",
  ["Puissance de l'ombre"] = "Spell_Shadow_ShadowPower",
  ["Protection contre l'Ombre"] = "Spell_Shadow_AntiShadow",
  ["Allonge de l'Ombre"] = "Spell_Shadow_ChillTouch",
  ["Résistance à l'Ombre"] = "Spell_Shadow_AntiShadow",
  ["Aura de résistance à l'Ombre"] = "Spell_Shadow_SealOfKings",
  ["Transe de l'ombre"] = "Spell_Shadow_Twilight",
  ["Vulnérabilité à l'Ombre"] = "Spell_Shadow_ShadowBolt",
  ["Gardien de l'ombre"] = "Spell_Shadow_AntiShadow",
  ["Sensibilité à l'Ombre"] = "INV_Misc_QirajiCrystal_05",
  ["Tissage de l'ombre"] = "Spell_Shadow_BlackPlague",
  ["Mot de l'ombre : Mort"] = "Spell_Shadow_DemonicFortitude",
  ["Mot de l'ombre : Douleur"] = "Spell_Shadow_ShadowWordPain",
  ["Ombre et flammes"] = "Spell_Shadow_ShadowandFlame",
  ["Ombre de la mort"] = "Spell_Arcane_PrismaticCloak",
  ["Brûlure de l'ombre"] = "Spell_Shadow_ScourgeBuild",
  ["Ombrefiel"] = "Spell_Shadow_Shadowfiend",
  ["Forme d'Ombre"] = "Spell_Shadow_Shadowform",
  ["Furie de l'ombre"] = "Spell_Shadow_Shadowfury",
  ["Garde de l'ombre"] = "Spell_Nature_LightningShield",
  ["Camouflage dans l'ombre"] = "Ability_Ambush",
  ["Camouflage dans l'ombre"] = "Ability_Ambush",
  ["Pas de l'ombre"] = "Ability_Rogue_Shadowstep",
  ["Rage du chaman"] = "Spell_Nature_ShamanRage",
  ["Griffes aiguisées"] = "INV_Misc_MonsterClaw_04",
  ["Fracasser"] = "Spell_Frost_FrostShock",
  ["Tonte"] = "Spell_Shadow_FocusedPower",
  ["Carapace bouclier"] = "Ability_Hunter_Pet_Turtle",
  ["Bouclier"] = "INV_Shield_04",
  ["Coup de bouclier"] = "Ability_Warrior_ShieldBash",
  ["Coup de bouclier - silencieux"] = "Ability_Warrior_ShieldBash",
  ["Maîtrise du blocage"] = "Ability_Defend",
  ["Heurt de bouclier"] = "INV_Shield_05",
  ["Spécialisation Bouclier"] = "INV_Shield_06",
  ["Mur protecteur"] = "Ability_Warrior_ShieldWall",
  ["Kriss"] = "INV_ThrowingKnife_04",
  ["Tir"] = "Ability_ShootWand",
  ["Tir à l'arc"] = "Ability_Marksmanship",
  ["Tir avec une arme à feu"] = "Ability_Marksmanship",
  ["Lambeau"] = "Spell_Shadow_VampiricAura",
  ["Attaques lacérantes"] = "Spell_Shadow_VampiricAura",
  ["Silence"] = "Spell_Shadow_ImpPhaseShift",
  ["Flèche-baîllon"] = "Ability_TheBlackArrow",
  ["Résolution silencieuse"] = "Spell_Nature_ManaRegenTotem",
  ["Drapeau d'Aile-argent"] = "INV_BannerPVP_02",
  ["Attaque pernicieuse"] = "Spell_Shadow_RitualOfSacrifice",
  ["Siphon de vie"] = "Spell_Shadow_Requiem",
  ["Dépeçage"] = "INV_Misc_Pelt_Wolf_01",
  ["Heurtoir"] = "Ability_Warrior_DecisiveStrike",
  ["Blason de tueur"] = "INV_Trinket_Naxxramas03",
  ["Sommeil"] = "Spell_Nature_Sleep",
  ["Débiter"] = "Ability_Rogue_SliceDice",
  ["Lenteur"] = "Spell_Nature_Slow",
  ["Chute lente"] = "Spell_Magic_FeatherFall",
  ["Fondre"] = "Spell_Fire_FlameBlades",
  ["Châtiment"] = "Spell_Holy_HolySmite",
  ["Piège à serpent"] = "Ability_Hunter_SnakeTrap",
  ["Pierre à aiguiser solide"] = "INV_Stone_SharpeningStone_04",
  ["Explosion sonore"] = "Spell_Shadow_Teleport",
  ["Apaiser les animaux"] = "Ability_Hunter_BeastSoothe",
  ["Baiser apaisant"] = "Spell_Shadow_SoothingKiss",
  ["Feu de l'âme"] = "Spell_Fire_Fireball02",
  ["Suceur d'âme"] = "Spell_Shadow_SoulLeech_3",
  ["Lien spirituel"] = "Spell_Shadow_GatherShadows",
  ["Siphon d'âme"] = "Spell_Shadow_LifeDrain02",
  ["Brise-âme"] = "Spell_Arcane_Arcane01",
  ["Résurrection de Pierre d'âme"] = "INV_Misc_Orb_04",
  ["Hâte des sorts"] = "Spell_Holy_SearingLight",
  ["Verrou magique"] = "Spell_Shadow_MindRot",
  ["Puissance des sorts"] = "Spell_Arcane_ArcaneTorrent",
  ["Renvoi de sort"] = "Ability_Warrior_ShieldReflection",
  ["Vulnérabilité aux sorts"] = "Spell_Holy_ElunesGrace",
  ["Protection contre les sorts"] = "Spell_Holy_SpellWarding",
  ["Vol de sort"] = "Spell_Arcane_Arcane02",
  ["Engagement spirituel"] = "Ability_Druid_DemoralizingRoar",
  ["Connexion spirituelle"] = "Spell_Shadow_Requiem",
  ["Esprit de rédemption"] = "INV_Enchant_EssenceEternalLarge",
  ["Harmonisation spirituelle"] = "Spell_Holy_ReviveChampion",
  ["Focalisation spirituelle"] = "Spell_Arcane_Blink",
  ["Direction spirituelle"] = "Spell_Holy_SpiritualGuidence",
  ["Soins spirituels"] = "Spell_Nature_MoonGlow",
  ["Fureur malveillante"] = "Ability_Warrior_Rampage",
  ["Sprint"] = "Ability_Rogue_Sprint",
  ["Maîtrise des postures"] = "Spell_Nature_EnchantArmor",
  ["Feu stellaire"] = "Spell_Arcane_StarFire",
  ["Feu stellaire étourdissant"] = "Spell_Arcane_StarFire",
  ["Colère stellaire"] = "Spell_Nature_AbolishMagic",
  ["Eclats stellaires"] = "Spell_Arcane_StarFire",
  ["Charge statique"] = "Spell_Nature_WispSplode",
  ["Bâtons"] = "INV_Staff_08",
  ["Tir assuré"] = "Ability_Hunter_SteadyShot",
  ["Camouflage"] = "Ability_Stealth",
  ["Etourdissement de Griffes de pierre"] = "Spell_Nature_StoneClawTotem",
  ["Totem de Griffes de pierre"] = "Spell_Nature_StoneClawTotem",
  ["Forme de pierre"] = "Spell_Shadow_UnholyStrength",
  ["Peau de pierre"] = "Spell_Nature_StoneSkinTotem",
  ["Totem de Peau de pierre"] = "Spell_Nature_StoneSkinTotem",
  ["Courroux naturel"] = "Spell_Holy_SealOfMight",
  ["Force de la Terre"] = "Spell_Nature_EarthBindTotem",
  ["Totem de Force de la Terre"] = "Spell_Nature_EarthBindTotem",
  ["Force des Halaani"] = "INV_Trinket_Naxxramas01",
  ["Bloqué"] = "Spell_Shadow_Teleport",
  ["Etourdi"] = "Spell_Frost_Stun",
  ["Discrétion"] = "Ability_EyeOfTheOwl",
  ["Souffrance"] = "Spell_Shadow_BlackPlague",
  ["Invocation de destrier"] = "Ability_Mount_Charger",
  ["Invocation d'un destrier de l'effroi"] = "Ability_Mount_Dreadsteed",
  ["Invocation d'un gangregarde"] = "Spell_Shadow_SummonFelGuard",
  ["Invocation d'un chasseur corrompu"] = "Spell_Shadow_SummonFelHunter",
  ["Invocation d'un palefroi corrompu"] = "Spell_Nature_Swiftness",
  ["Invocation d'un diablotin"] = "Spell_Shadow_SummonImp",
  ["Invocation d'une succube"] = "Spell_Shadow_SummonSuccubus",
  ["Invocation d'un marcheur du Vide"] = "Spell_Shadow_SummonVoidWalker",
  ["Invocation d'un cheval de guerre"] = "Spell_Nature_Swiftness",
  ["Invocation d'un élémentaire d'eau"] = "Spell_Frost_SummonWaterElemental_2",
  ["Fracasser armure"] = "Ability_Warrior_Sunder",
  ["Suppression"] = "Spell_Shadow_UnsummonBuilding",
  ["Pied sûr"] = "Ability_Kick",
  ["Vague de lumière"] = "Spell_Holy_SurgeOfLight",
  ["Attaques surprises"] = "Ability_Rogue_SurpriseAttack",
  ["Survivant"] = "Spell_Shadow_Twilight",
  ["Attaques circulaires"] = "Ability_Rogue_SliceDice",
  ["Forme de vol rapide"] = "Ability_Druid_FlightForm",
  ["Prompte guérison"] = "INV_Relics_IdolofRejuvenation",
  ["Balayage"] = "INV_Misc_MonsterClaw_03",
  ["Spécialisation Epée"] = "INV_Sword_27",
  ["Symbole d'espoir"] = "Spell_Holy_SymbolOfHope",
  ["Maître tacticien"] = "Spell_Nature_EnchantArmor",
  ["Couture"] = "Trade_Tailoring",
  ["Corruption sanguine"] = "Spell_Shadow_LifeDrain",
  ["Esprit corrompu"] = "Spell_Shadow_ShadowPact",
  ["Dompte une bête"] = "Ability_Hunter_BeastTaming",
  ["Provocation"] = "Spell_Nature_Reincarnation",
  ["Téléportation : Darnassus"] = "Spell_Arcane_TeleportDarnassus",
  ["Téléportation : Exodar"] = "Spell_Arcane_TeleportExodar",
  ["Téléportation : Forgefer"] = "Spell_Arcane_TeleportIronForge",
  ["Téléportation : Reflet-de-Lune"] = "Spell_Arcane_TeleportMoonglade",
  ["Téléportation : Orgrimmar"] = "Spell_Arcane_TeleportOrgrimmar",
  ["Téléportation : Shattrath"] = "Spell_Arcane_TeleportShattrath",
  ["Téléportation : Lune-d'argent"] = "Spell_Arcane_TeleportSilvermoon",
  ["Téléportation : Hurlevent"] = "Spell_Arcane_TeleportStormWind",
  ["Téléportation : les Pitons du Tonnerre"] = "Spell_Arcane_TeleportThunderBluff",
  ["Téléportation : Fossoyeuse"] = "Spell_Arcane_TeleportUnderCity",
  ["Faille temporelle"] = "Spell_Nature_TimeStop",
  ["La bête intérieure"] = "Ability_Hunter_BeastWithin",
  ["L'esprit humain"] = "INV_Enchant_ShardBrilliantSmall",
  ["Peau épaisse"] = "INV_Misc_Pelt_Bear_03",
  ["Epines"] = "Spell_Nature_Thorns",
  ["Frisson de la chasse"] = "Ability_Hunter_ThrilloftheHunt",
  ["Lancer"] = "Ability_Throw",
  ["Spécialisation Armes de jet"] = "INV_ThrowingAxe_03",
  ["Armes de jet"] = "INV_ThrowingKnife_02",
  ["Coup de tonnerre"] = "Spell_Nature_ThunderClap",
  ["Lame-tonnerre"] = "Spell_Nature_Cyclone",
  ["Frappe foudroyante"] = "Ability_ThunderBolt",
  ["Grondeterre"] = "Ability_Hunter_Pet_Gorilla",
  ["Focalisation des flots"] = "Spell_Frost_ManaRecharge",
  ["Maîtrise des flots"] = "Spell_Nature_Tranquility",
  ["Monte de tigre"] = "Spell_Nature_Swiftness",
  ["Fureur du tigre"] = "Ability_Mount_JungleTiger",
  ["Faveur du temps"] = "Ability_Rogue_FleetFooted",
  ["Tourment"] = "Spell_Shadow_GatherShadows",
  ["Totem"] = "Spell_Nature_StoneClawTotem",
  ["Totem de courroux"] = "Spell_Fire_TotemOfWrath",
  ["Appel totémique"] = "Spell_unused",
  ["Focalisation totémique"] = "Spell_Nature_MoonGlow",
  ["Toucher de l'ombre"] = "Spell_Shadow_PsychicScream",
  ["Toucher de faiblesse"] = "Spell_Shadow_DeadofNight",
  ["Résistance"] = "Spell_Holy_Devotion",
  ["Traces de silithyste"] = "Spell_Nature_TimeStop",
  ["Pistage des bêtes"] = "Ability_Tracking",
  ["Pistage des démons"] = "Spell_Shadow_SummonFelHunter",
  ["Pistage des draconiens"] = "INV_Misc_Head_Dragon_01",
  ["Pistage des élémentaires"] = "Spell_Frost_SummonWaterElemental",
  ["Pistage des géants"] = "Ability_Racial_Avatar",
  ["Pistage des camouflés"] = "Ability_Stealth",
  ["Pistage des humanoïdes"] = "Spell_Holy_PrayerOfHealing",
  ["Pistage des morts-vivants"] = "Spell_Shadow_DarkSummoning",
  ["Tranquillité de l'air"] = "Spell_Nature_Brilliance",
  ["Totem de Tranquillité de l'air"] = "Spell_Nature_Brilliance",
  ["Tranquillité de l'esprit"] = "Spell_Holy_ElunesGrace",
  ["Tranquillité"] = "Spell_Nature_Tranquility",
  ["Tir tranquillisant"] = "Spell_Nature_Drowsy",
  ["Maîtrise des pièges"] = "Ability_Ensnare",
  ["Forme de voyage"] = "Ability_Druid_TravelForm",
  ["Arbre de vie"] = "Ability_Druid_TreeofLife",
  ["Totem de Séisme"] = "Spell_Nature_TremorTotem",
  ["Travail du cuir tribal"] = "Spell_Nature_NullWard",
  ["Aura de précision"] = "Ability_TrueShot",
  ["Renvoi des morts-vivants"] = "Spell_Holy_TurnUndead",
  ["Bénédiction des flèches jumelles"] = "Spell_Nature_ElementalPrecision_1",
  ["Haches à deux mains"] = "INV_Axe_04",
  ["Haches et masses à deux mains"] = "INV_Axe_10",
  ["Masses à deux mains"] = "INV_Mace_04",
  ["Epées à deux mains"] = "Ability_MeleeDamage",
  ["Spécialisation Arme 2M"] = "INV_Axe_09",
  ["Mains nues"] = "Ability_GolemThunderClap",
  ["Volonté inflexible"] = "Spell_Magic_MageArmor",
  ["Colère déchaînée"] = "Spell_Nature_StoneClawTotem",
  ["Monte de cheval squelette"] = "Spell_Nature_Swiftness",
  ["Respiration aquatique"] = "Spell_Shadow_DemonBreath",
  ["Respiration interminable"] = "Spell_Shadow_DemonBreath",
  ["Puissance impie"] = "Spell_Shadow_ShadowWordDominate",
  ["Fureur libérée"] = "Ability_BullRush",
  ["Rage libérée"] = "Spell_Nature_UnleashedRage",
  ["Affliction instable"] = "Spell_Shadow_UnstableAffliction_3",
  ["Puissance instable"] = "Spell_Lightning_LightningBolt01",
  ["Foi inflexible"] = "Spell_Holy_UnyieldingFaith",
  ["Mal au ventre"] = "Ability_Hunter_Pet_Boar",
  ["Etreinte vampirique"] = "Spell_Shadow_UnsummonBuilding",
  ["Toucher vampirique"] = "Spell_Holy_Stoicism",
  ["Disparition"] = "Ability_Vanish",
  ["Invisible"] = "Ability_Vanish",
  ["Vengeance"] = "Spell_Nature_Purge",
  ["Totem venimeux"] = "Spell_Totem_WardOfDraining",
  ["Ivresse de la victoire"] = "Ability_Warrior_Devastate",
  ["Vigueur"] = "Spell_Nature_EarthBindTotem",
  ["Rayon abominable"] = "Spell_Shadow_ShadowBolt",
  ["Poisons abominables"] = "Ability_Rogue_FeignDeath",
  ["Justification"] = "Spell_Holy_Vindication",
  ["Morsure de vipère"] = "Ability_Hunter_AimedShot",
  ["Salve"] = "Ability_Marksmanship",
  ["Spécialisation Baguette"] = "INV_Wand_01",
  ["Baguettes"] = "Ability_ShootWand",
  ["Choc martial"] = "Ability_WarStomp",
  ["Distorsion"] = "Spell_Arcane_Arcane04",
  ["Drapeau Chanteguerre"] = "INV_BannerPVP_01",
  ["Respiration aquatique"] = "Spell_Shadow_DemonBreath",
  ["Bouclier d'eau"] = "Ability_Shaman_WaterShield",
  ["Tombe aquatique"] = "Spell_Frost_ManaRecharge",
  ["Marcher sur l’eau"] = "Spell_Frost_WindWalkOn",
  ["Eclair d'eau"] = "Spell_Frost_FrostBolt",
  ["Tombeau aquatique"] = "Spell_Shadow_DemonBreath",
  ["Ame affaiblie"] = "Spell_Holy_AshesToAshes",
  ["Maîtrise des armes"] = "Ability_Warrior_WeaponMastery",
  ["Fabricant d'armes"] = "INV_Sword_25",
  ["Tourbillon"] = "Ability_Whirlwind",
  ["Volonté des Réprouvés"] = "Spell_Shadow_RaiseDead",
  ["Furie-des-vents"] = "Spell_Nature_Cyclone",
  ["Attaque Furie-des-vents"] = "Spell_Nature_Cyclone",
  ["Totem Furie-des-vents"] = "Spell_Nature_Windfury",
  ["Arme Furie-des-vents"] = "Spell_Nature_Cyclone",
  ["Mur des vents"] = "Spell_Nature_EarthBind",
  ["Totem de Mur des vents"] = "Spell_Nature_EarthBind",
  ["Coupure d'ailes"] = "Ability_Rogue_Trip",
  ["Froid hivernal"] = "Spell_Frost_ChillingBlast",
  ["Esprit feu follet"] = "Spell_Nature_WispSplode",
  ["Monte de loup"] = "Spell_Nature_Swiftness",
  ["Poison douloureux"] = "INV_Misc_Herb_16",
  ["Poison douloureux II"] = "INV_Misc_Herb_16",
  ["Poison douloureux III"] = "INV_Misc_Herb_16",
  ["Poison douloureux IV"] = "INV_Misc_Herb_16",
  ["Poison douloureux V"] = "INV_Misc_Herb_16",
  ["Colère"] = "Spell_Nature_AbolishMagic",
  ["Totem de courroux de l'air"] = "Spell_Nature_SlowingTotem",
  ["Colère de Cénarius"] = "Ability_Druid_TwilightsWrath",
  ["Courroux de l'Astromancien"] = "Spell_Arcane_Arcane02",
  ["Piqûre de wyverne"] = "INV_Spear_02",
}
