<Ui xmlns="http://www.blizzard.com/wow/ui/" 
xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" 
xsi:schemaLocation="http://www.blizzard.com/wow/ui/
C:\Projects\WoW\Bin\Interface\FrameXML\UI.xsd">
	
	<Frame name="LazyScriptMinimapFrame" parent="Minimap" enableMouse="false" frameStrata="MEDIUM" hidden="true">
		<Scripts>
			<OnLoad>
				lazyScript.metadata:updateRevisionFromKeyword("$Revision: 171 $")
			</OnLoad>
		</Scripts>
		<Size>
			<AbsDimension x="33" y="33"/>
		</Size>
		<Anchors>
			<Anchor point="CENTER" relativeTo="Minimap" relativePoint="CENTER"/>
		</Anchors>
		<Frames>
			<Button name="LazyScriptMinimapButton">
				<Size>
					<AbsDimension x="33" y="33"/>
				</Size>
				<Anchors>
					<Anchor point="CENTER"/>
				</Anchors>
				<Layers>
					<Layer level="ARTWORK">
						<Texture name="LazyScriptMinimapIcon" file="Interface\Icons\INV_Misc_Note_05">
							<Size>
								<AbsDimension x="18" y="18"/>
							</Size>
							<Anchors>
								<Anchor point="TOPLEFT">
									<Offset>
										<AbsDimension x="7" y="-6"/>
									</Offset>
								</Anchor>
							</Anchors>
						</Texture>
					</Layer>
					<Layer level="OVERLAY">
						<Texture file="Interface\Minimap\MiniMap-TrackingBorder">
							<Size>
								<AbsDimension x="52" y="52"/>
							</Size>
							<Anchors>
								<Anchor point="TOPLEFT"/>
							</Anchors>
						</Texture>
					</Layer>
				</Layers>
				<HighlightTexture alphaMode="ADD" file="Interface\Minimap\UI-Minimap-ZoomButton-Highlight"/>
				<Scripts>
					<OnLoad>
						lazyScript.mm.OnLoad()
						this:RegisterForDrag("RightButton")
						this.dragme = false
					</OnLoad>
					<OnDragStart> 
						this.dragme = true
					</OnDragStart> 
					<OnDragStop>
						this.dragme = false
					</OnDragStop>
					<OnUpdate>
						if (this.dragme == true) then
						lazyScript.mm.Button_BeingDragged()
						end
					</OnUpdate>
					<OnEnter>
						lazyScript.mm.OnEnter()
					</OnEnter>
					<OnLeave>
						GameTooltip:Hide()
					</OnLeave>
					<OnClick>
						lazyScript.mm.OnClick(arg1)
					</OnClick>
					<OnEvent>
						lazyScript.mm.OnEvent()
					</OnEvent>
				</Scripts>
			</Button>
		</Frames>
	</Frame>
	
	<Frame name="LazyScriptMinimapMenu" inherits="UIDropDownMenuTemplate" id="1" hidden="false">
		<Size>
			<AbsDimension x="0" y="0"/>
		</Size>
		<Anchors>
			<Anchor point="TOPLEFT" relativeTo="LazyScriptMinimapButton" relativePoint="CENTER">
				<Offset>
					<AbsDimension x="300" y="0"/>
				</Offset>
			</Anchor>
		</Anchors>
		<Scripts>
			<OnLoad>
				this:SetFrameLevel(0)
				UIDropDownMenu_SetWidth(180)
			</OnLoad>
			<OnShow>
				UIDropDownMenu_Initialize(this, lazyScript.mm.Menu_Initialize, "MENU")
			</OnShow>
		</Scripts>
	</Frame>
	
</Ui>