-- Modern
local modern = {
  ["chat"] = {
    ["global"] = {
      ["custombg"] = "1",
      ["border"] = "0.1,0.1,0.1,0.5",
      ["background"] = "0.1,0.1,0.1,0.2",
    },
    ["right"] = {
      ["enable"] = "1",
      ["alwaysshow"] = "1",
    },
  },
  ["global"] = {
    ["font_unit"] = "Interface\\AddOns\\pfUI\\fonts\\Myriad-Pro.ttf",
    ["font_size"] = "11",
    ["font_unit_size"] = "11",
  },
  ["panel"] = {
    ["left"] = {
      ["left"] = "exp",
    },
    ["xp"] = {
      ["dont_overlap"] = "1",
      ["xp_always"] = "1",
      ["xp_mode"] = "HORIZONTAL",
      ["xp_position"] = "TOP",
      ["xp_anchor"] = "pfActionBarLeft",
      ["xp_color"] = "0.6,0.6,0.6,1",
      ["rest_color"] = "0.4,0.4,0.4,0.5",
      ["xp_height"] = "4",
      ["rep_always"] = "1",
      ["rep_mode"] = "HORIZONTAL",
      ["rep_position"] = "TOP",
      ["rep_anchor"] = "pfActionBarRight",
      ["rep_height"] = "4",
      ["rep_display"] = "FLEX",
    },
  },
  ["unitframes"] = {
    ["custom"] = "2",
    ["custompbgcolor"] = "0.3,0.1,0.1,1",
    ["custombgcolor"] = "0.3,0.1,0.1,1",
    ["ragecolor"] = "0.6,0.2,0.2,1",
    ["energycolor"] = "0.6,0.4,0.2,1",
    ["manacolor"] = "0.2,0.2,0.4,1",
    ["focuscolor"] = "0.6,0.4,0.2,1",
    ["customcolor"] = "0.1,0.1,0.1,1",

    ["grouptarget"] = {
      ["bartexture"] = "Interface\\AddOns\\pfUI\\img\\bar_gradient",
      ["pbartexture"] = "Interface\\AddOns\\pfUI\\img\\bar_gradient",
    },
    ["ptarget"] = {
      ["bartexture"] = "Interface\\AddOns\\pfUI\\img\\bar_gradient",
      ["pbartexture"] = "Interface\\AddOns\\pfUI\\img\\bar_gradient",
    },
    ["target"] = {
      ["bartexture"] = "Interface\\AddOns\\pfUI\\img\\bar_gradient",
      ["pbartexture"] = "Interface\\AddOns\\pfUI\\img\\bar_gradient",
      ["txthpleft"] = "healthdyn",
      ["pspace"] = "1",
      ["txtpowerleft"] = "powerdyn",
      ["height"] = "22",
      ["buffs"] = "TOPRIGHT",
      ["txthpright"] = "unitrev",
      ["buffsize"] = "16",
      ["portrait"] = "right",
      ["width"] = "160",
    },
    ["ttarget"] = {
      ["bartexture"] = "Interface\\AddOns\\pfUI\\img\\bar_gradient",
      ["pbartexture"] = "Interface\\AddOns\\pfUI\\img\\bar_gradient",
      ["pspace"] = "1",
      ["height"] = "7",
      ["portrait"] = "off",
    },
    ["pet"] = {
      ["bartexture"] = "Interface\\AddOns\\pfUI\\img\\bar_gradient",
      ["pbartexture"] = "Interface\\AddOns\\pfUI\\img\\bar_gradient",
      ["pspace"] = "1",
      ["pheight"] = "3",
      ["txtpowercenter"] = "healthdyn",
      ["height"] = "7",
      ["buffs"] = "BOTTOMLEFT",
      ["portrait"] = "off",
      ["debuffs"] = "BOTTOMRIGHT",
    },
    ["player"] = {
      ["txtpowerright"] = "powerdyn",
      ["bartexture"] = "Interface\\AddOns\\pfUI\\img\\bar_gradient",
      ["pbartexture"] = "Interface\\AddOns\\pfUI\\img\\bar_gradient",
      ["pspace"] = "1",
      ["debuffs"] = "TOPRIGHT",
      ["portrait"] = "left",
      ["buffsize"] = "16",
      ["width"] = "160",
      ["height"] = "22",
      ["showPVP"] = "1",
    },
    ["customfullhp"] = "1",
    ["focustarget"] = {
      ["bartexture"] = "Interface\\AddOns\\pfUI\\img\\bar_gradient",
      ["pbartexture"] = "Interface\\AddOns\\pfUI\\img\\bar_gradient",
    },
    ["customfade"] = "1",
    ["group"] = {
      ["bartexture"] = "Interface\\AddOns\\pfUI\\img\\bar_gradient",
      ["pbartexture"] = "Interface\\AddOns\\pfUI\\img\\bar_gradient",
    },
    ["raid"] = {
      ["indicator_pos"] = "TOPRIGHT",
      ["verticalbar"] = "1",
      ["txthpleft"] = "none",
      ["defcolor"] = "0",
      ["customfade"] = "1",
      ["focuscolor"] = "0.6,0.4,0.2,1",
      ["raidpadding"] = "5",
      ["txthpcenter"] = "namehealthbreak",
      ["ragecolor"] = "0.6,0.2,0.2,1",
      ["bartexture"] = "Interface\\AddOns\\pfUI\\img\\bar_gradient",
      ["pbartexture"] = "Interface\\AddOns\\pfUI\\img\\bar_gradient",
      ["glowaggro"] = "0",
      ["height"] = "35",
      ["txthpright"] = "none",
      ["customcolor"] = "0.1,0.1,0.1,1",
      ["width"] = "40.7",
      ["manacolor"] = "0.2,0.2,0.4,1",
      ["energycolor"] = "0.6,0.4,0.2,1",
      ["customfullhp"] = "1",
      ["custom"] = "2",
    },
    ["focus"] = {
      ["bartexture"] = "Interface\\AddOns\\pfUI\\img\\bar_gradient",
      ["pbartexture"] = "Interface\\AddOns\\pfUI\\img\\bar_gradient",
    },
    ["tttarget"] = {
      ["bartexture"] = "Interface\\AddOns\\pfUI\\img\\bar_gradient",
      ["pbartexture"] = "Interface\\AddOns\\pfUI\\img\\bar_gradient",
    },
    ["grouppet"] = {
      ["bartexture"] = "Interface\\AddOns\\pfUI\\img\\bar_gradient",
      ["pbartexture"] = "Interface\\AddOns\\pfUI\\img\\bar_gradient",
    },
  },
  ["appearance"] = {
    ["castbar"] = {
      ["texture"] = "Interface\\AddOns\\pfUI\\img\\bar_gradient",
    },
    ["minimap"] = {
      ["coordsloc"] = "bottomleft",
    },
    ["border"] = {
      ["color"] = "0.1,0.1,0.1,1",
      ["bags"] = "3",
      ["chat"] = "3",
      ["default"] = "2",
    },
  },
  ["tooltip"] = {
    ["statusbar"] = {
      ["texture"] = "Interface\\AddOns\\pfUI\\img\\bar_gradient",
    },
  },
  ["castbar"] = {
    ["player"] = {
      ["showrank"] = "1",
      ["height"] = "12",
      ["showlag"] = "1",
      ["showicon"] = "1",
    },
    ["target"] = {
      ["height"] = "12",
      ["showicon"] = "1",
    },
  },
  ["abuttons"] = {
    ["enable"] = "1",
    ["hideincombat"] = "0",
  },
  ["nameplates"] = {
    ["heighthealth"] = "10",
    ["healthtexture"] = "Interface\\AddOns\\pfUI\\img\\bar_gradient",
    ["health"] = {
      ["offset"] = "10",
    },
  },
  ["bars"] = {
    ["bar4"] = {
      ["enable"] = "0",
    },
    ["bar11"] = {
      ["background"] = "0",
      ["spacing"] = "3",
    },
    ["bar5"] = {
      ["formfactor"] = "12 x 1",
      ["buttons"] = "6",
    },
    ["bar3"] = {
      ["formfactor"] = "12 x 1",
      ["buttons"] = "6",
    },
  }
}

-- Legacy
local legacy = {
  ["chat"] = {
    ["right"] = {
      ["enable"] = "1",
    },
  },
}

-- Slim
local slim = {
  ["panel"] = {
    ["use_unitfonts"] = "1",
  },
  ["tooltip"] = {
    ["position"] = "bottom",
  },
  ["appearance"] = {
    ["infight"] = {
      ["screen"] = "0",
      ["common"] = "1",
    },
    ["border"] = {
      ["color"] = "0,0,0,1",
      ["bags"] = "3",
      ["panels"] = "1",
      ["unitframes"] = "1",
      ["default"] = "1",
      ["background"] = "0.1,0.1,0.1,0.8",
      ["chat"] = "2",
      ["actionbars"] = "1",
    },
  },
  ["buffbar"] = {
    ["pdebuff"] = {
      ["enable"] = "1",
      ["height"] = "14",
    },
    ["pbuff"] = {
      ["enable"] = "1",
      ["height"] = "14",
    },
    ["tdebuff"] = {
      ["enable"] = "1",
      ["height"] = "14",
    },
  },
  ["chat"] = {
    ["global"] = {
      ["tabmouse"] = "1",
      ["tabdock"] = "0",
      ["border"] = "0,0,0,0",
      ["background"] = "0,0,0,0.3",
      ["custombg"] = "1",
    },
    ["text"] = {
      ["outline"] = "0",
    },
    ["left"] = {
      ["height"] = "160",
      ["width"] = "405",
    },
    ["right"] = {
      ["enable"] = "1",
      ["height"] = "160",
      ["width"] = "405",
    },
  },
  ["bars"] = {
    ["background"] = "0",
    ["icon_size"] = "22",
  },
  ["version"] = "3.3.0",
  ["nameplates"] = {
    ["use_unitfonts"] = "1",
  },
  ["unitframes"] = {
    ["target"] = {
      ["buffsize"] = "14",
      ["pheight"] = "6",
      ["height"] = "35",
      ["panchor"] = "TOPLEFT",
      ["pspace"] = "-1",
      ["width"] = "225",
    },
    ["pet"] = {
      ["debuffs"] = "top",
      ["pheight"] = "6",
      ["pspace"] = "-1",
      ["width"] = "125",
    },
    ["player"] = {
      ["buffsize"] = "14",
      ["txthpleft"] = "powerdyn",
      ["height"] = "35",
      ["showPVP"] = "1",
      ["panchor"] = "TOPRIGHT",
      ["pspace"] = "-1",
      ["width"] = "225",
      ["pheight"] = "6",
    },
    ["custombgcolor"] = "0.5,0.2,0.2,1",
    ["focus"] = {
      ["pheight"] = "6",
    },
    ["raid"] = {
      ["height"] = "24",
      ["width"] = "48",
    },
    ["custombg"] = "1",
    ["customcolor"] = "0.15,0.15,0.15,1",
    ["custom"] = "1",
    ["ttarget"] = {
      ["portrait"] = "off",
      ["pheight"] = "6",
      ["height"] = "16",
      ["pspace"] = "-1",
      ["width"] = "125",
    },
    ["tttarget"] = {
      ["portrait"] = "off",
      ["pheight"] = "6",
      ["height"] = "16",
      ["pspace"] = "-1",
      ["width"] = "125",
    },
    ["ptarget"] = {
      ["portrait"] = "off",
      ["pheight"] = "-1",
      ["height"] = "8",
      ["pspace"] = "-1",
      ["width"] = "125",
    },
  },
}

-- Adapta
local adapta = {
  ["appearance"] = {
    ["infight"] = {
      ["screen"] = "1",
      ["common"] = "0",
    },
    ["border"] = {
      ["color"] = "0,0,0,1",
      ["unitframes"] = "1",
      ["background"] = "0.05,0.15,0.15,0.75",
      ["actionbars"] = "4",
    },
  },
  ["unitframes"] = {
    ["target"] = {
      ["portrait"] = "off",
      ["height"] = "35",
      ["panchor"] = "TOPLEFT",
      ["pspace"] = "-1",
      ["width"] = "225",
      ["pheight"] = "6",
    },
    ["pet"] = {
      ["debuffs"] = "top",
      ["portrait"] = "off",
      ["pspace"] = "-1",
      ["width"] = "125",
      ["pheight"] = "6",
    },
    ["player"] = {
      ["portrait"] = "off",
      ["txthpleft"] = "powerdyn",
      ["height"] = "35",
      ["showPVP"] = "1",
      ["panchor"] = "TOPRIGHT",
      ["pspace"] = "-1",
      ["width"] = "225",
      ["pheight"] = "6",
    },
    ["focus"] = {
      ["portrait"] = "off",
      ["pheight"] = "6",
    },
    ["ttarget"] = {
      ["portrait"] = "off",
      ["height"] = "16",
      ["pspace"] = "-1",
      ["width"] = "125",
      ["pheight"] = "6",
    },
    ["tttarget"] = {
      ["portrait"] = "off",
      ["height"] = "16",
      ["pspace"] = "-1",
      ["width"] = "125",
      ["pheight"] = "6",
    },
    ["ptarget"] = {
      ["portrait"] = "off",
      ["height"] = "8",
      ["pspace"] = "-1",
      ["width"] = "125",
      ["pheight"] = "-1",
    },
    ["customcolor"] = "0.1,0.2,0.2,1",
    ["custombg"] = "1",
    ["custom"] = "1",
  },
  ["bars"] = {
    ["icon_size"] = "17",
    ["right"] = {
      ["formfactor"] = "4 x 3",
    },
    ["bottomright"] = {
      ["formfactor"] = "4 x 3",
    },
  },
  ["chat"] = {
    ["global"] = {
      ["tabmouse"] = "1",
      ["tabdock"] = "0",
      ["border"] = "0,0,0,1",
      ["custombg"] = "1",
      ["background"] = "0.15,0.25,0.25,0.3",
    },
    ["text"] = {
      ["outline"] = "0",
    },
    ["left"] = {
      ["height"] = "161",
      ["width"] = "400",
    },
    ["right"] = {
      ["enable"] = "1",
      ["height"] = "161",
      ["width"] = "400",
    },
  },
}

-- assign profiles to userdata
pfUI_profiles["Modern"] = modern
pfUI_profiles["Legacy"] = legacy
pfUI_profiles["Adapta"] = adapta
pfUI_profiles["Slim"] = slim

-- overwrite core profiles in userdata
local profile_loader = CreateFrame("Frame")
profile_loader:RegisterEvent("VARIABLES_LOADED")
profile_loader:SetScript("OnEvent", function()
  pfUI_profiles["Modern"] = modern
  pfUI_profiles["Legacy"] = legacy
  pfUI_profiles["Adapta"] = adapta
  pfUI_profiles["Slim"] = slim
  this:UnregisterAllEvents()
end)