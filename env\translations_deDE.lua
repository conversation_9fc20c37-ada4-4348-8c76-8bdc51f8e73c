pfUI_translation["deDE"] = {
  ["3D World Unit Font"] = nil,
  ["Abbreviate Numbers (4200 -> 4.2k)"] = nil,
  ["Abbreviate Unit Names"] = nil,
  ["About"] = nil,
  ["Actionbar"] = nil,
  ["Action Bar"] = nil,
  ["Action Bar Border Size"] = nil,
  ["Active Addons"] = nil,
  ["Add button to the frame"] = nil,
  ["Added button"] = nil,
  ["Addon Buttons"] = nil,
  ["Addon Buttons Panel Position"] = nil,
  ["Addon List"] = nil,
  ["Addon Profile"] = nil,
  ["AddOns"] = nil,
  ["Add to Blacklist"] = nil,
  ["Add to Whitelist"] = nil,
  ["Align Chat Windows"] = nil,
  ["Aligned Position"] = nil,
  ["All messages will be forwarded to:"] = nil,
  ["Alt-Click Action"] = nil,
  ["Always Allow Drag Via Shift Key"] = nil,
  ["Always Show"] = nil,
  ["Always Show Extended Vendor Values"] = nil,
  ["Always Show Health In Percent"] = nil,
  ["Always Show Item Comparison"] = nil,
  ["Always Show On Target Units"] = nil,
  ["Always Show On Units With Missing HP"] = nil,
  ["Always Show Self In Raid Frames"] = nil,
  ["Always Use 2D Portraits"] = nil,
  ["Ammo Counter"] = nil,
  ["Anchor Bags Above Chat"] = nil,
  ["A new installation of |cff33ffccpf|rUI ships with 4 prebuilt design profiles. Click below if you wish to load one of these profiles."] = nil,
  ["A new version is available"] = nil,
  ["Appearance"] = nil,
  ["Ascending"] = nil,
  ["Author"] = nil,
  ["Auto-Castable Action Indicator"] = nil,
  ["Auto Hide"] = nil,
  ["Autohide Timeout"] = nil,
  ["Automatic"] = nil,
  ["Automatic Background Color"] = nil,
  ["Automatic Border Color"] = nil,
  ["Automatic Class Text Color"] = nil,
  ["Automatic Health Text Color"] = nil,
  ["Automatic Level Text Color"] = nil,
  ["Automatic Power Text Color"] = nil,
  ["Automatic Text Color"] = nil,
  ["Auto Paging"] = nil,
  ["Auto Repair Items"] = nil,
  ["Auto Sell Grey Items"] = nil,
  ["Average Per Hour"] = nil,
  ["Background Color"] = nil,
  ["Bags & Bank"] = nil,
  ["Bags Border Size"] = nil,
  ["Bagslots Per Row"] = nil,
  ["Bagspace"] = nil,
  ["Banker"] = nil,
  ["Bankslots Per Row"] = nil,
  ["Bar Background"] = nil,
  ["Bar Texture"] = nil,
  ["Battleground Statistics"] = nil,
  ["Blacklist"] = nil,
  ["Blinding Powder"] = nil,
  ["Blue Border On Friendly Players"] = nil,
  ["BoE"] = nil,
  ["BoP"] = nil,
  ["Border Around Target Unit"] = nil,
  ["Border Color"] = nil,
  ["Border Color Around Target Unit"] = nil,
  ["Border Size"] = nil,
  ["Bottom"] = nil,
  ["Bottom Left"] = nil,
  ["Bottom Right"] = nil,
  ["Buffbar Height"] = nil,
  ["Buffbar Width"] = nil,
  ["Buff/Debuff Icons"] = nil,
  ["Buff/Debuff Indicators"] = nil,
  ["Buff Font Size"] = nil,
  ["Buff Limit"] = nil,
  ["Buff Position"] = nil,
  ["Buffs"] = nil,
  ["Buff Size"] = nil,
  ["Buff Spacing"] = nil,
  ["Buffs Per Row"] = nil,
  ["Button"] = nil,
  ["Button already exists in pfMinimapButtons frame"] = nil,
  ["Button Animation"] = nil,
  ["Button Animation Trigger"] = nil,
  ["Button not found in pfMinimapButtons frame"] = nil,
  ["Buttons"] = nil,
  ["Button Spacing"] = nil,
  ["Buy"] = nil,
  ["Cache"] = nil,
  ["Castbar"] = nil,
  ["Castbar Height"] = nil,
  ["Castbar Texture"] = nil,
  ["Castbar Width"] = nil,
  ["Casting Color"] = nil,
  ["Center"] = nil,
  ["Center Text"] = nil,
  ["Center Text X Offset"] = nil,
  ["Center Text Y Offset"] = nil,
  ["|cff33ffccBlizzard: \"Interface Options\"|r\n\nDo you want me to set up the recommended Blizzard UI settings? This will enable settings that can be found in the Interface section of your client. Options like Buff Durations, Instant Quest Text, Auto Selfcast and others will be set."] = nil,
  ["|cff33ffccChat: \"Channels\"|r\n\nDo you want me to setup the chat channels of your chatframes? This would set important or personal messages to the left chat and world channels and lootinformation to the right chat."] = nil,
  ["|cff33ffccChat: \"Layout\"|r\n\nDo you want me to adjust the layout of your chatframes? This would make sure, that every window is placed on its dedicated position."] = nil,
  ["|cff33ffccChat: \"Loot & Spam\"|r\n\nDo you want me to create and manage a specific Chatframe called \"Loot & Spam\"? This chat will display world channels, loot information and miscellaneous messages, that would otherwise clutter your main chatframe."] = nil,
  ["|cff33ffccKeybind Mode|r\nThis mode allows you to bind keyboard shortcuts to your actionbars.\nBy hovering a button with your cursor and pressing a key, the key will be assigned to that button.\nHit Escape on a button to remove bindings.\n\nPress Escape or click on an empty area to leave the keybind mode."] = nil,
  ["|cff33ffccpf|cffffffffUI|cffcccccc Config"] = nil,
  ["|cff33ffccpf|rUI: New version available! Have a look at http://shagu.org !"] = nil,
  ["|cffddddddIt's always safe to upgrade |cff33ffccpf|rUI. |cffddddddYou won't lose any of your configuration."] = nil,
  ["|cffff5555EVERYTHING"] = nil,
  ["Channeling Color"] = nil,
  ["Chat"] = nil,
  ["Chat Background Color"] = nil,
  ["Chat Border Color"] = nil,
  ["Chat Border Size"] = nil,
  ["Chat Bubble Transparency"] = nil,
  ["Chat Default Brackets"] = nil,
  ["Class"] = nil,
  ["Clear Rolls"] = nil,
  ["Click Action"] = nil,
  ["Click Casting"] = nil,
  ["Clock"] = nil,
  ["Close"] = nil,
  ["Color"] = nil,
  ["Color Buff Stacks"] = nil,
  ["Color Debuff Stacks"] = nil,
  ["Colorize player name on WorldMap and BattlefieldMinimap"] = nil,
  ["Colorize Unknown Classes"] = nil,
  ["Colors"] = nil,
  ["Combat"] = nil,
  ["Combat/Aggro Indicators"] = nil,
  ["Combat Text"] = nil,
  ["Combat Text Font"] = nil,
  ["Combat Text Size"] = nil,
  ["Combat Timer"] = nil,
  ["Combopoint Height"] = nil,
  ["Combopoint Width"] = nil,
  ["Compare Item Base Stats"] = nil,
  ["Components"] = nil,
  ["Config UI Settings"] = nil,
  ["Configuration"] = nil,
  ["Configure"] = nil,
  ["Cooldown"] = nil,
  ["Cooldown Color (Days)"] = nil,
  ["Cooldown Color (Hours)"] = nil,
  ["Cooldown Color (Less than 3 Sec)"] = nil,
  ["Cooldown Color (Minutes)"] = nil,
  ["Cooldown Color (Seconds)"] = nil,
  ["Cooldown / Durations"] = nil,
  ["Cooldown Text Font"] = nil,
  ["Cooldown Text Font Size"] = nil,
  ["Cooldown Text Font Size (Blizzard Frames)"] = nil,
  ["Cooldown Text Font Size (Foreign Frames)"] = nil,
  ["Cooldown Text Size"] = nil,
  ["Cooldown Text Threshold"] = nil,
  ["Cooldown Text Time Threshold"] = nil,
  ["Coordinates Location"] = nil,
  ["Coordinates On Minimap"] = nil,
  ["Count"] = nil,
  ["Create Profile"] = nil,
  ["Ctrl-Click"] = nil,
  ["Ctrl-Click Action"] = nil,
  ["Current"] = nil,
  ["Current HP"] = nil,
  ["Current HP - Max HP"] = nil,
  ["Current HP / Max HP"] = nil,
  ["Current HP - Max HP | Percent"] = nil,
  ["Current HP / Max HP | Percent"] = nil,
  ["Current HP | Percent"] = nil,
  ["Cursor"] = nil,
  ["Cursor Tooltip Align"] = nil,
  ["Cursor Tooltip Offset"] = nil,
  ["Custom"] = nil,
  ["Custom Font Name"] = nil,
  ["Custom Font Size"] = nil,
  ["Custom Font Style"] = nil,
  ["Custom Health Bar Background Color"] = nil,
  ["Custom Health Bar Color"] = nil,
  ["Custom Power Bar Background Color"] = nil,
  ["Custom Transparency"] = nil,
  ["Debuff Icon Offset"] = nil,
  ["Debuff Icon Size"] = nil,
  ["Debuff Limit"] = nil,
  ["Debuff Position"] = nil,
  ["Debuffs"] = nil,
  ["Debuff Size"] = nil,
  ["Debuffs Per Row"] = nil,
  ["Decode"] = nil,
  ["Default"] = nil,
  ["Defaults"] = nil,
  ["Default Transparency"] = nil,
  ["Deficit"] = nil,
  ["Delete profile"] = nil,
  ["Delete / Reset"] = nil,
  ["Descending"] = nil,
  ["Description Font"] = nil,
  ["Description Font Size"] = nil,
  ["Detailed Random Roll Announcement"] = nil,
  ["Detect Enemy Buffs"] = nil,
  ["Disable"] = nil,
  ["Disable Blizzard Castbar"] = nil,
  ["Disabled"] = nil,
  ["Disable Errors in UIErrors Frame"] = nil,
  ["Disable Focus Castbar"] = nil,
  ["Disable GM-Mode"] = nil,
  ["Disable Item Quality Color For \"Common\" Items"] = nil,
  ["Disable Loot Confirmation Dialog (Without Group)"] = nil,
  ["Disable Module"] = nil,
  ["Disable pfUI Unit Frames"] = nil,
  ["Disable Player Castbar"] = nil,
  ["Disable Skin"] = nil,
  ["Disable Target Castbar"] = nil,
  ["Disenchanter"] = nil,
  ["Dispel Indicators"] = nil,
  ["Display Addon Errors In Chat"] = nil,
  ["Display Aggro Indicator"] = nil,
  ["Display Combat Indicator"] = nil,
  ["Display Debuff Durations"] = nil,
  ["Display Frame"] = nil,
  ["Display Mode"] = nil,
  ["Display Overheal"] = nil,
  ["Dock"] = nil,
  ["Dodge"] = nil,
  ["Donate"] = nil,
  ["Don't overlap rested"] = nil,
  ["Do you really want to reset |cffffaaaaEVERYTHING|r?"] = nil,
  ["Do you really want to reset |cffffaaaaEVERYTHING|r?\n\nThis will reset:\n - Current Configuration\n - Current Frame Positions\n - Firstrun Wizard\n - Addon Cache\n - Saved Profiles"] = nil,
  ["Do you really want to reset the Cache?"] = nil,
  ["Do you really want to reset your configuration?\nThis also includes frame positions"] = nil,
  ["Do you want to disable the addon?"] = nil,
  ["Do you want to reload the UI now?"] = nil,
  ["Draw Glow Around Target Nameplate"] = nil,
  ["Druid Mana Bar Height"] = nil,
  ["Druid Mana Bar Text"] = nil,
  ["Elastic Zoom"] = nil,
  ["Elysium Based Core"] = nil,
  ["Enable"] = nil,
  ["Enable 24h Clock"] = nil,
  ["Enable 2D Portraits As Fallback"] = nil,
  ["Enable 40y-Range Check"] = nil,
  ["Enable Addon Button Frame"] = nil,
  ["Enable Advanced Master Loot Menu"] = nil,
  ["Enable Aggro Glow"] = nil,
  ["Enable Aggro Glow Effects On Screen Edges"] = nil,
  ["Enable Autohide"] = nil,
  ["Enable Autohide For Left Chat Panel"] = nil,
  ["Enable Autohide For Microbar Panel"] = nil,
  ["Enable Autohide For Minimap Panel"] = nil,
  ["Enable Autohide For Right Chat Panel"] = nil,
  ["Enable Auto-Resize Loot Frame"] = nil,
  ["Enable Bar"] = nil,
  ["Enable Buff Display"] = nil,
  ["Enable Castbars"] = nil,
  ["Enable Chat Bubble Borders"] = nil,
  ["Enable Chat Bubbles"] = nil,
  ["Enable Chat Dock Background"] = nil,
  ["Enable Chat Fade"] = nil,
  ["Enable Chat History"] = nil,
  ["Enable Chat Tab Flashing"] = nil,
  ["Enable Class Colors"] = nil,
  ["Enable Class Colors On Enemies"] = nil,
  ["Enable Class Colors On Friends"] = nil,
  ["Enable Class Colors On Friends Name"] = nil,
  ["Enable Clickthrough"] = nil,
  ["Enable Combat Glow"] = nil,
  ["Enable Combat Glow Effects On Screen Edges"] = nil,
  ["Enable Combo Point Display"] = nil,
  ["Enable Custom Color Health Bar Background"] = nil,
  ["Enable Custom Color Power Bar Background"] = nil,
  ["Enable Custom Colors"] = nil,
  ["Enable Custom Incoming Whispers Layout"] = nil,
  ["Enable Debuff Display"] = nil,
  ["Enable Debuffs"] = nil,
  ["Enable Durations On Blizzard Frames"] = nil,
  ["Enable Durations On Foreign Frames"] = nil,
  ["Enable Energy Ticks"] = nil,
  ["Enable Extended Guild Information"] = nil,
  ["Enable Frame Shadow"] = nil,
  ["Enable Full-Text Search"] = nil,
  ["Enable Indicators"] = nil,
  ["Enable Item Quality Color For Equipment Only"] = nil,
  ["Enable \"Loot & Spam\" Chat Window"] = nil,
  ["Enable \"Loot & Spam\" Window"] = nil,
  ["Enable Loot Window On MouseCursor"] = nil,
  ["Enable Low Health Glow Effects On Screen Edges"] = nil,
  ["Enable Mana Ticks"] = nil,
  ["Enable Micro Bar"] = nil,
  ["Enable Mouselook With Right Click"] = nil,
  ["Enable Mouseover Tooltip"] = nil,
  ["Enable Movable Bags"] = nil,
  ["Enable Offscreen Frame Positions"] = nil,
  ["Enable Overlap"] = nil,
  ["Enable Party Chat Bubbles"] = nil,
  ["Enable Pastel Colors"] = nil,
  ["Enable Pixel Perfect Borders"] = nil,
  ["Enable Range Fading"] = nil,
  ["Enable Region Compatible Font"] = nil,
  ["Enable Seconds"] = nil,
  ["Enable Spellname"] = nil,
  ["Enable Sticky Chat"] = nil,
  ["Enable Text Shadow"] = nil,
  ["Enable Timestamps"] = nil,
  ["Enable UI-Scale"] = nil,
  ["Enable URL Detection"] = nil,
  ["Enable Vertical Health Bar"] = nil,
  ["Enable Weapon Buff Display"] = nil,
  ["Encode"] = nil,
  ["Ended"] = nil,
  ["Energy Color"] = nil,
  ["Equipped Item Color"] = nil,
  ["Estimate Debuffs"] = nil,
  ["Estimate Enemy Health Points"] = nil,
  ["Every feature that \"|cff33ffcc%s|r\" offers is already built into pfUI."] = nil,
  ["Exp"] = nil,
  ["Experience"] = nil,
  ["Experience Bar"] = nil,
  ["Experience Color"] = nil,
  ["Exploration Point"] = nil,
  ["Export"] = nil,
  ["Face"] = nil,
  ["Fade To Custom Color"] = nil,
  ["Fast"] = nil,
  ["Filter Mode"] = nil,
  ["Finish"] = nil,
  ["Firstrun"] = nil,
  ["Flash Powder"] = nil,
  ["Focus"] = nil,
  ["Focus Castbar"] = nil,
  ["Focus Color"] = nil,
  ["Focus-Target"] = nil,
  ["Font"] = nil,
  ["Font Options"] = nil,
  ["Font Padding"] = nil,
  ["Font Style"] = nil,
  ["Force Blizzard Borders (|cffffaaaaExperimental|r)"] = nil,
  ["fps"] = nil,
  ["FPS & Ping"] = nil,
  ["Frame Anchor"] = nil,
  ["Frame Shadow Intensity"] = nil,
  ["Friends Online"] = nil,
  ["Gameclient"] = nil,
  ["General"] = nil,
  ["Generate Playerlinks"] = nil,
  ["GitHub"] = nil,
  ["Global Border Size"] = nil,
  ["Glow"] = nil,
  ["Glow Color Around Target Nameplate"] = nil,
  ["GM-Mode"] = nil,
  ["Gold"] = nil,
  ["Graphic Renderer"] = nil,
  ["Green Border On Friendly NPCs"] = nil,
  ["Group"] = nil,
  ["Group Frames"] = nil,
  ["Group Options"] = nil,
  ["Group-Pet"] = nil,
  ["Group-Target"] = nil,
  ["Gryphon"] = nil,
  ["Gryphons"] = nil,
  ["Guild Online"] = nil,
  ["Happiness Icon Size"] = nil,
  ["Heal Color"] = nil,
  ["Health"] = nil,
  ["Health - Auto"] = nil,
  ["Healthbar"] = nil,
  ["Healthbar Animation Speed"] = nil,
  ["Health Bar Color"] = nil,
  ["Healthbar Embedded"] = nil,
  ["Healthbar Height"] = nil,
  ["Health Bar Height"] = nil,
  ["Healthbar Texture"] = nil,
  ["Health Bar Texture"] = nil,
  ["Healthbar Vertical Offset"] = nil,
  ["Health Bar Width"] = nil,
  ["Health - Current"] = nil,
  ["Health - Max"] = nil,
  ["Health - Min/Max"] = nil,
  ["Health - Missing"] = nil,
  ["Health - Percentage"] = nil,
  ["Health Point Estimation"] = nil,
  ["Health Text Format"] = nil,
  ["Health Text Position"] = nil,
  ["Hearth"] = nil,
  ["Hearthstone"] = nil,
  ["Height"] = nil,
  ["Hide Addon Buttons On Combat"] = nil,
  ["Hide All UI Elements"] = nil,
  ["Hide Channel Names"] = nil,
  ["Hide Chat When Bags Are Opened"] = nil,
  ["Hide Combat Log"] = nil,
  ["Hide Foreign Cooldown Animations"] = nil,
  ["Hide Group Frames While In Raid"] = nil,
  ["Hide Healthbar On Critters"] = nil,
  ["Hide Healthbar On Enemy NPCs"] = nil,
  ["Hide Healthbar On Enemy Players"] = nil,
  ["Hide Healthbar On Friendly NPCs"] = nil,
  ["Hide Healthbar On Friendly Players"] = nil,
  ["Hide Healthbar On Neutral NPCs"] = nil,
  ["Hide Healthbar On Totems"] = nil,
  ["Hide Timeout"] = nil,
  ["Hide When Entering Combat"] = nil,
  ["Highlight Equipped Items"] = nil,
  ["Highlight Not Usable Spells"] = nil,
  ["Highlight Out Of Mana Spells"] = nil,
  ["Highlight Out Of Range Spells"] = nil,
  ["Highlight Settings That Require Reload"] = nil,
  ["Highlight Unusable Items"] = nil,
  ["Hold [Shift] to use item."] = nil,
  ["Honorable Kill"] = nil,
  ["Honorable Kill!"] = nil,
  ["Horizontal"] = nil,
  ["Horizontal Offset"] = nil,
  ["Hoverbind"] = nil,
  ["Huge"] = nil,
  ["Huge (PixelPerfect)"] = nil,
  ["Icon"] = nil,
  ["Icon Size"] = nil,
  ["Ignore Layout"] = nil,
  ["Import"] = nil,
  ["Inactive Nameplate Alpha"] = nil,
  ["Incoming Whispers Color"] = nil,
  ["Indicator Position"] = nil,
  ["Indicator Size"] = nil,
  ["Indicator Spacing"] = nil,
  ["Inherit Default Colors"] = nil,
  ["Inputbox Height"] = nil,
  ["Inputbox Width"] = nil,
  ["Instant"] = nil,
  ["Integrations"] = nil,
  ["Invert Health Bar"] = nil,
  ["is not allowed in profile name"] = nil,
  ["is now blacklisted."] = nil,
  ["is now whitelisted."] = nil,
  ["Item Count Text Color"] = nil,
  ["Item Count Text Size"] = nil,
  ["Item Durability"] = nil,
  ["ItemID"] = nil,
  ["Item Looted"] = nil,
  ["Item Slot Size"] = nil,
  ["Keybind Text Color"] = nil,
  ["Keybind Text Size"] = nil,
  ["Language"] = nil,
  ["Large"] = nil,
  ["Last Reputation"] = nil,
  ["Layout"] = nil,
  ["Left"] = nil,
  ["Left Actionbar"] = nil,
  ["Left Anchor"] = nil,
  ["Left Chat Frame"] = nil,
  ["Left Chat Height"] = nil,
  ["Left Chat Width"] = nil,
  ["Left Click"] = nil,
  ["Left Mouse Button"] = nil,
  ["Left Panel: Center"] = nil,
  ["Left Panel: Left"] = nil,
  ["Left Panel: Right"] = nil,
  ["Left Text"] = nil,
  ["Left Text X Offset"] = nil,
  ["Left Text Y Offset"] = nil,
  ["Legacy"] = nil,
  ["Level"] = nil,
  ["Level Up"] = nil,
  ["Lion"] = nil,
  ["Lists of added and deleted buttons are cleared"] = nil,
  ["Load Defaults"] = nil,
  ["Load profile"] = nil,
  ["Localtime"] = nil,
  ["Lock Actionbars"] = nil,
  ["Login"] = nil,
  ["Look & Feel"] = nil,
  ["Loot"] = nil,
  ["Loot & Spam"] = nil,
  ["Macro Text Color"] = nil,
  ["Macro Text Size"] = nil,
  ["Main Actionbar"] = nil,
  ["Mana - Auto"] = nil,
  ["Mana Color"] = nil,
  ["Mana - Current"] = nil,
  ["Mana - Max"] = nil,
  ["Mana - Min/Max"] = nil,
  ["Mana - Missing"] = nil,
  ["Mana - Percentage"] = nil,
  ["Map Exploration Points"] = nil,
  ["Map Group/Raid Circle Size"] = nil,
  ["Map Reveal Color"] = nil,
  ["Map Tooltip Scale"] = nil,
  ["Max Amount Of Raid Frames"] = nil,
  ["Maximum Number Of Chat Lines"] = nil,
  ["Medium"] = nil,
  ["Memory Usage"] = nil,
  ["Menu Font"] = nil,
  ["Menu Font Size"] = nil,
  ["Messages are no longer forwarded to:"] = nil,
  ["Middle Mouse Button"] = nil,
  ["Minimap"] = nil,
  ["Minimap Panel"] = nil,
  ["Minimap Size (|cffffaaaaExperimental|r)"] = nil,
  ["Minimap Tracking"] = nil,
  ["Modules"] = nil,
  ["Money"] = nil,
  ["Monochrome"] = nil,
  ["Mouse Button 4"] = nil,
  ["Mouse Button 5"] = nil,
  ["Mouseover"] = nil,
  ["Mousewheel Scroll Speed"] = nil,
  ["ms"] = nil,
  ["N/A"] = nil,
  ["Name"] = nil,
  ["Name | Health Missing"] = nil,
  ["Name (Linebreak) -Health Missing"] = nil,
  ["Nameplate Border Size"] = nil,
  ["Nameplates"] = nil,
  ["Nameplate Width"] = nil,
  ["Name (Short)"] = nil,
  ["Name (Short) | Health Missing"] = nil,
  ["Native"] = nil,
  ["Network Down"] = nil,
  ["Network Latency"] = nil,
  ["Network Up"] = nil,
  ["New entry:"] = nil,
  ["NEW TIMER"] = nil,
  ["Next"] = nil,
  ["Next Memory Cleanup"] = nil,
  ["No"] = nil,
  ["No Anchor"] = nil,
  ["None"] = nil,
  ["Not a valid button!"] = nil,
  ["No tracking spell active"] = nil,
  ["Not Set"] = nil,
  ["Not Usable Color"] = nil,
  ["Now"] = nil,
  ["Number Of Buffs Per Row"] = nil,
  ["Number Of Buttons Per Row/Column"] = nil,
  ["Number Of Debuffs Per Row"] = nil,
  ["Number Of Weapon Buffs Per Row"] = nil,
  ["Off"] = nil,
  ["Offline Transparency"] = nil,
  ["On Key Press"] = nil,
  ["Only"] = nil,
  ["Only Class Dispellable"] = nil,
  ["Only Count Bagspace On Regular Bags"] = nil,
  ["Only Show Chat Dock On Mouseover"] = nil,
  ["Only Show On Mouse Over"] = nil,
  ["Only Show Own Debuffs (|cffffaaaaExperimental|r)"] = nil,
  ["Only Show Target Castbar"] = nil,
  ["On State Change"] = nil,
  ["Options"] = nil,
  ["Orientation"] = nil,
  ["Other Panel: Minimap"] = nil,
  ["Outline"] = nil,
  ["Outlines"] = nil,
  ["Out Of Mana Color"] = nil,
  ["Out Of Range Color"] = nil,
  ["Out Of Range Transparency"] = nil,
  ["Overwrite Border Color With Combat State"] = nil,
  ["Overwrite Colors"] = nil,
  ["Overwrite Fonts"] = nil,
  ["Overwrite Health Color With Combat State"] = nil,
  ["Overwrite If Unit Is Attacking No One"] = nil,
  ["Overwrite If Unit Is Attacking Others"] = nil,
  ["Overwrite If Unit Is Attacking You"] = nil,
  ["Overwrite If Unit Is Casting"] = nil,
  ["Pageable"] = nil,
  ["Paging Actionbar"] = nil,
  ["Panel"] = nil,
  ["Panel Border Size"] = nil,
  ["Percent"] = nil,
  ["Pet"] = nil,
  ["Pet Actionbar"] = nil,
  ["Pet Experience"] = nil,
  ["Pet-Target"] = nil,
  ["Pixel"] = nil,
  ["Player"] = nil,
  ["Player Buff Bar"] = nil,
  ["Player Castbar"] = nil,
  ["Player Debuff Bar"] = nil,
  ["Player Unitframe"] = nil,
  ["Please enter a name for the new profile."] = nil,
  ["Please enter a name for the new profile.\nExisting profiles sharing the same name will be overwritten."] = nil,
  ["Please enter a name of a character who should receive your whispers:"] = nil,
  ["Portrait Alpha"] = nil,
  ["Portrait Height"] = nil,
  ["Portrait Position"] = nil,
  ["Portrait Width"] = nil,
  ["Powerbar"] = nil,
  ["Power Bar Anchor"] = nil,
  ["Power Bar Height"] = nil,
  ["Power Bar Texture"] = nil,
  ["Power Bar Width"] = nil,
  ["Power Bar X-Offset"] = nil,
  ["Power Bar Y-Offset"] = nil,
  ["Profile"] = nil,
  ["PvP Rank"] = nil,
  ["Quest Levels"] = nil,
  ["Rage Color"] = nil,
  ["Raid"] = nil,
  ["Raid Fill Direction"] = nil,
  ["Raid Icon"] = nil,
  ["Raid Icon Position"] = nil,
  ["Raid Icon Size"] = nil,
  ["Raid Icon X-Offset"] = nil,
  ["Raid Icon Y-Offset"] = nil,
  ["Raid Layout"] = nil,
  ["Raid Mark Size"] = nil,
  ["Raid Padding"] = nil,
  ["Random"] = nil,
  ["Random Roll Announcement Rarity"] = nil,
  ["Random Rolling"] = nil,
  ["Range Based Hunter Paging"] = nil,
  ["Range Check Interval"] = nil,
  ["Rank"] = nil,
  ["Red Border On Enemy Units"] = nil,
  ["Red Name Text On Infight Units"] = nil,
  ["Regional Settings"] = nil,
  ["Remaining"] = nil,
  ["Remove button from the frame"] = nil,
  ["Removed button"] = nil,
  ["Replace Totems With Icons"] = nil,
  ["Reputation"] = nil,
  ["Reputation Bar"] = nil,
  ["Reputation Level"] = nil,
  ["Request Rolls"] = nil,
  ["Required Damage In Percent"] = nil,
  ["Reroll"] = nil,
  ["Reroll Ties"] = nil,
  ["Reset"] = nil,
  ["Reset all manually added or ignored buttons"] = nil,
  ["Reset Timer"] = nil,
  ["Resolution"] = nil,
  ["Rested"] = nil,
  ["Rested Color"] = nil,
  ["Resting"] = nil,
  ["Reveal Unexplored Areas"] = nil,
  ["Right"] = nil,
  ["Right Actionbar"] = nil,
  ["Right Anchor"] = nil,
  ["Right Chat Frame"] = nil,
  ["Right Chat Height"] = nil,
  ["Right Chat Width"] = nil,
  ["Right Click"] = nil,
  ["Right Click Auto Attack Threshold"] = nil,
  ["Right Mouse Button"] = nil,
  ["Right Panel: Center"] = nil,
  ["Right Panel: Left"] = nil,
  ["Right Panel: Right"] = nil,
  ["Right Text"] = nil,
  ["Right Text X Offset"] = nil,
  ["Right Text Y Offset"] = nil,
  ["Roll for"] = nil,
  ["Save current settings to profile"] = nil,
  ["Save profile"] = nil,
  ["Save & Reload"] = nil,
  ["Scale"] = nil,
  ["Scale Border On HiDPI Displays"] = nil,
  ["Scaling"] = nil,
  ["Scan Macros For Spells"] = nil,
  ["Screen Edge Glow Intensity"] = nil,
  ["Screen Resolution"] = nil,
  ["Screenshot"] = nil,
  ["Scrolling Combat Text Font"] = nil,
  ["Search"] = nil,
  ["Seconds Before Chat Fade"] = nil,
  ["Selected Core"] = nil,
  ["Select profile"] = nil,
  ["Self"] = nil,
  ["Self Cast: Alt Key"] = nil,
  ["Self Cast: Right Click"] = nil,
  ["Sell"] = nil,
  ["Sell Grey Items"] = nil,
  ["Seperate Weapon Buffs"] = nil,
  ["Servertime"] = nil,
  ["Set Banker"] = nil,
  ["Set Disenchanter"] = nil,
  ["Settings"] = nil,
  ["Setup All Chat Channels"] = nil,
  ["Setup Optimized Game Settings"] = nil,
  ["Shapeshift Bar"] = nil,
  ["Share"] = nil,
  ["Shift-Click"] = nil,
  ["Shift-Click Action"] = nil,
  ["Show Addon Buttons On Login"] = nil,
  ["Show Animation On Hidden Bars"] = nil,
  ["Show Background"] = nil,
  ["Show Buff Indicators"] = nil,
  ["Show Combat Text"] = nil,
  ["Show Custom Indicators"] = nil,
  ["Show Debuff Stacks"] = nil,
  ["Show Description"] = nil,
  ["Show Dispel Indicators"] = nil,
  ["Show Druid Mana Bar"] = nil,
  ["Show Duration Inside Buff"] = nil,
  ["Show Empty Buttons"] = nil,
  ["Show FPS and Latency Colors"] = nil,
  ["Show Guild Name"] = nil,
  ["Show Happiness Icon"] = nil,
  ["Show Health Points"] = nil,
  ["Show/Hide TimeManager"] = nil,
  ["Show/Hide Timer"] = nil,
  ["Show Hotkey Text"] = nil,
  ["Show Hots Indicators"] = nil,
  ["Show Hots of all Classes"] = nil,
  ["Show Inactive Tracking"] = nil,
  ["Show In Combat"] = nil,
  ["Show Incompatible Config Entries"] = nil,
  ["Show Item Count Text"] = nil,
  ["Show Item IDs"] = nil,
  ["Show Items On Mouseover"] = nil,
  ["Show Lag"] = nil,
  ["Show Leader Icon"] = nil,
  ["Show Loot Icon"] = nil,
  ["Show Macro Text"] = nil,
  ["Show Meters By Default"] = nil,
  ["Show Milliseconds When Timer Runs Out"] = nil,
  ["Show On Friendly Units"] = nil,
  ["Show On Hostile Units"] = nil,
  ["Show Procs Indicators"] = nil,
  ["Show Procs of all Classes"] = nil,
  ["Show PvP-Flag"] = nil,
  ["Show PvP Icon"] = nil,
  ["Show Raid Mark"] = nil,
  ["Show Rank"] = nil,
  ["Show Reagent Count"] = nil,
  ["Show Related Quest On Questitems"] = nil,
  ["Show Required Questitem Count"] = nil,
  ["Show Resting"] = nil,
  ["Show Self In Group Frames"] = nil,
  ["Show Spell Icon"] = nil,
  ["Show Spell Name"] = nil,
  ["Show Stacks"] = nil,
  ["Show Text"] = nil,
  ["Show Time Left"] = nil,
  ["Show Timer"] = nil,
  ["Show Timer Animation"] = nil,
  ["Show Timer Text"] = nil,
  ["Show Totems Indicators"] = nil,
  ["Shrink & Return"] = nil,
  ["Size"] = nil,
  ["Skin"] = nil,
  ["Skins"] = nil,
  ["Slow"] = nil,
  ["Small"] = nil,
  ["Some settings need to reload the UI to take effect.\nDo you want to reload now?"] = nil,
  ["Some settings need to reload the UI to take effect.\nDo you want to reloadUI now?"] = nil,
  ["Sort Order"] = nil,
  ["Soul Shard"] = nil,
  ["Soulshard Counter"] = nil,
  ["Soulshards"] = nil,
  ["Spacing"] = nil,
  ["Special Recipient"] = nil,
  ["Square"] = nil,
  ["Stance Bar 1"] = nil,
  ["Stance Bar 2"] = nil,
  ["Stance Bar 3"] = nil,
  ["Stance Bar 4"] = nil,
  ["Stance/Shapeshift Actionbar"] = nil,
  ["Standard Text Font"] = nil,
  ["Standard Text Font Size"] = nil,
  ["Status"] = nil,
  ["Status Bar Texture"] = nil,
  ["SuperWoW Settings"] = nil,
  ["Switch Pages On Alt Key Press"] = nil,
  ["Switch Pages On Ctrl Key Press"] = nil,
  ["Switch Pages On Druid Stealth"] = nil,
  ["Switch Pages On Shift Key Press"] = nil,
  ["Systeminfo"] = nil,
  ["Target"] = nil,
  ["Target Castbar"] = nil,
  ["Target Debuff Bar"] = nil,
  ["Target Nameplate Zoom Factor"] = nil,
  ["Target-Target"] = nil,
  ["Target-Target-Target"] = nil,
  ["Text"] = nil,
  ["Text Color"] = nil,
  ["Text Colors"] = nil,
  ["Texture"] = nil,
  ["The addon \"|cff33ffcc%s|r\" doesn't work with pfUI and has been disabled."] = nil,
  ["The addon selection has changed."] = nil,
  ["Thick Outline"] = nil,
  ["Thirdparty"] = nil,
  ["This Session"] = nil,
  ["Thistle Tea"] = nil,
  ["Threshold To Trust Health Estimation"] = nil,
  ["Time"] = nil,
  ["Timer"] = nil,
  ["Time Remaining"] = nil,
  ["Timer In Minutes"] = nil,
  ["Timestamp Brackets"] = nil,
  ["Timestamp Color"] = nil,
  ["Timestamp Format"] = nil,
  ["Time Threshold"] = nil,
  ["Tiny"] = nil,
  ["Tiny (PixelPerfect)"] = nil,
  ["Toggle as Tank"] = nil,
  ["Tooltip"] = nil,
  ["Tooltip Position"] = nil,
  ["Tooltip Text Font"] = nil,
  ["Tooltip Text Font Size"] = nil,
  ["Top"] = nil,
  ["Top Actionbar"] = nil,
  ["Top Left"] = nil,
  ["Top Right"] = nil,
  ["Total Gold"] = nil,
  ["Totem Direction"] = nil,
  ["Totem Icons"] = nil,
  ["Tracked Reputation"] = nil,
  ["Tracking Icon Size"] = nil,
  ["Trigger Actions On Key Down"] = nil,
  ["UI-Scale"] = nil,
  ["Unbuffed"] = nil,
  ["Unit Frame Border Size"] = nil,
  ["Unit Frame Layout"] = nil,
  ["Unit Frames"] = nil,
  ["UnitFrame Spacing"] = nil,
  ["Unit Frame Text Font"] = nil,
  ["Unit Frame Text Size"] = nil,
  ["Unit Frame Text Style"] = nil,
  ["Unit Is Attacking No One Color"] = nil,
  ["Unit Is Attacking Others Color"] = nil,
  ["Unit Is Attacking You Color"] = nil,
  ["Unit Is Casting Color"] = nil,
  ["Unit String"] = nil,
  ["Unit String (Reverse)"] = nil,
  ["Unknown"] = nil,
  ["Unknown Class Color"] = nil,
  ["Unlock"] = nil,
  ["Unlock Mode allows you to move, scale and reset frames:\n\n|cff33ffcc[Left Click]|r Drag Frame\n|cff33ffcc[Middle Click]|r Reset Frame\n|cff33ffcc[Right Click]|r Option Box\n\nHold |cff33ffcc[Shift]|r to select all frames of the same kind\nHold |cff33ffcc[Ctrl]|r to select all frames of the same group\n\n|cff33ffcc[Scroll]|r to change the scale of a frame\n\n|cff33ffcc[Click]|r on an empty area or press |cff33ffcc[Esc]|r to exit\n"] = nil,
  ["Unusable Item Color"] = nil,
  ["Use Blizzard Borders"] = nil,
  ["Use Chat Colors for Meters"] = nil,
  ["Use Custom Color Health Bar Background"] = nil,
  ["Use Custom Color On Full Health"] = nil,
  ["Use Custom Color Power Bar Background"] = nil,
  ["Use Custom Font Settings"] = nil,
  ["Use Dynamic Font Size"] = nil,
  ["Use Item Rarity Color For Loot-Roll Timer"] = nil,
  ["Use Original Game Fonts"] = nil,
  ["Use Raid Frames To Display Group Members"] = nil,
  ["Use Single Line UIErrors Frame"] = nil,
  ["Use Unit Fonts"] = nil,
  ["Version"] = nil,
  ["Vertical"] = nil,
  ["Vertical Actionbar"] = nil,
  ["Vertical Healthbar"] = nil,
  ["Vertical Offset"] = nil,
  ["Vertical Offset (|cffffaaaaExperimental|r)"] = nil,
  ["Vertical Text Offset"] = nil,
  ["Very Fast"] = nil,
  ["Very Slow"] = nil,
  ["Website"] = nil,
  ["Welcome to |cff33ffccpf|cffffffffUI|r!\n\nI'm the first run wizard that will guide you through some basic configuration. If you're lazy, feel free to hit the \"Defaults\" button. If you wish to run this dialog again, go to the settings and hit the \"Reset Firstrun\" button.\n\nVisit |cff33ffcchttp://shagu.org|r to check for the latest version."] = nil,
  ["Whitelist"] = nil,
  ["Who Search Unknown Classes"] = nil,
  ["Width"] = nil,
  ["Wobble Zoom"] = nil,
  ["Won"] = nil,
  ["World Map Scale"] = nil,
  ["XP"] = nil,
  ["XP Bar"] = nil,
  ["XP Percentage"] = nil,
  ["Yellow Border On Neutral Units"] = nil,
  ["Yes"] = nil,
  ["You gain (.+) Mana from Totemic Recall"] = nil,
  ["You got"] = nil,
  ["Your interface is now set up.\n\nFor advanced configuration, just open the |cff33ffccpf|rUI settings via the escape menu or type \"|cffffffaa/pfui|r\" into the chat.\n\n Have a nice trip!\n\n|cffaaaaaa- Shagu"] = nil,
  ["Your items have been repaired for"] = nil,
  ["Your vendor trash has been sold and you earned"] = nil,
  ["Zone Name"] = nil,
  ["Zone Text On Minimap"] = nil,
  ["Zonetime"] = nil,
  ["Zoom & Fade"] = nil,
  ["Zoom Target Nameplate"] = nil,
}
