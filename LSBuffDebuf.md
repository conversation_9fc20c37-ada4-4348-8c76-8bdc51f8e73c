List of known Buffs/Debuffs
===========================


Used with "if\[Not\]{Player,Pet,Target}Has{Buff,Debuff}" and
"if\[Not\]{Buff,Debuff}Duration{&lt;,&gt;}XXs".


Full Name = Short Name
----------------------

__Abolish Disease__ = abolishDisease

__Abolish Poison__ = abolishPoison

__Adrenaline Rush__ = adrenaline

__Amplify Curse__ = amplifyCurse

__Amplify Magic__ = amplifyMagic

__Aquatic Form__ = aquatic

__Arcane Brilliance__ = brilliance

__Arcane Intellect__ = intellect

__Aspect of the Beast__ = aspectBeast

__Aspect of the Cheetah__ = aspectCheetah

__Aspect of the Hawk__ = aspectHawk

__Aspect of the Monkey__ = aspectMonkey

__Aspect of the Pack__ = aspectPack

__Aspect of the Wild__ = aspectWild

__Banish__ = banish

__Barkskin__ = barkskin

__Bash__ = bash

__Battle Shout__ = battleShout

__Bear Form__ = bear

__Berserker Rage__ = berserkerRage

__Berserking__ = berserking

__Bestial Wrath__ = bestialWrath

__Blade Flurry__ = bladeFlurry

__Blessing of Freedom__ = blessFree

__Blessing of Kings__ = blessKings

__Blessing of Light__ = blessLight

__Blessing of Might__ = blessMight

__Blessing of Protection__ = blessProt

__Blessing of Sacrifice__ = blessSac

__Blessing of Salvation__ = blessSlv

__Blessing of Sanctuary__ = blessSnct

__Blessing of Wisdom__ = blessWisdom

__Blind__ = blind

__Blood Fury__ = bloodFury

__Bloodrage__ = bloodrage

__Brain Food__ = brainFood

__Cannibalize__ = cannibalize

__Cat Form__ = cat

__Challenging Shout__ = challengingShout

__Cheap Shot__ = cs

__Clearcasting__ = clearcasting

__Cold Blood__ = coldBlood

__Combustion__ = combustion

__Concentration Aura__ = concAura

__Concussion Blow__ = concussionBlow

__Concussive Shot__ = concussive

__Corruption__ = corruption

__Curse of Agony__ = curseAgony

__Curse of Exhaustion__ = curseExhaustion

__Curse of Recklessness__ = curseReckless

__Curse of Shadow__ = curseShadow

__Curse of Tongues__ = curseTongues

__Curse of Weakness__ = curseWeakness

__Curse of the Elements__ = curseElements

__Dampen Magic__ = dampenMagic

__Dash__ = dash

__Dazed__ = dazed

__Death Coil__ = deathCoil

__Death Wish__ = deathWish

__Demon Armor__ = demonArmor

__Demon Skin__ = demonSkin

__Demoralizing Roar__ = demoralize

__Demoralizing Shout__ = demoShout

__Detect Greater Invisibility__ = detectGreaterInvis

__Detect Invisibility__ = detectInvis

__Detect Lesser Invisibility__ = detectLesserInvis

__Devotion Aura__ = devAura

__Devouring Plague__ = devouringPlague

__Dire Bear Form__ = direBear

__Disarm__ = disarm

__Divine Favor__ = divFavor

__Divine Protection__ = divProt

__Divine Shield__ = divShield

__Divine Spirit__ = divineSpirit

__Drain Life__ = drainLife

__Drain Mana__ = drainMana

__Drain Soul__ = drainSoul

__Drink__ = drink

__Eagle Eye__ = eagleEye

__Elune's Grace__ = elunesGrace

__Enrage__ = enrage

__Entangling Roots__ = roots

__Evasion__ = evasion

__Evocation__ = evocation

__Explosive Trap Effect__ = explosiveTrap

__Expose Armor__ = expose

__Eyes of the Beast__ = eotb

__Feral Charge Effect__ = charge

__Fade__ = fade

__Faerie Fire__ = faerieFire

__Fear Ward__ = fearWard

__Fear__ = fear

__Feed Pet Effect__ = feedPet

__Feedback__ = feedback

__Feign Death__ = feign

__Fire Resistance Aura__ = fireAura

__Fire Resistance__ = fireResistTotem

__Fire Vulnerability__ = fireVulnerability

__Fire Ward__ = fireWard

__First Aid__ = firstAid

__Flame Shock__ = flameShock

__FlameTongue__ = flameTotem

__Food__ = fishFood

__Food__ = food

__Forbearance__ = forbearance

__Freezing Trap__ = freezingTrap

__Frenzied Regeneration__ = frenziedRegen

__Frost Armor__ = frostArmor

__Frost Resistance__ = frostResistTotem

__Frost Trap Aura__ = frostTrap

__Frost Ward__ = frostWard

__Frostbolt__ = frostbolt

__Furious Howl__ = furiousHowl

__Garrote__ = garrote

__Ghost Wolf__ = ghostwolf

__Ghostly Strike__ = ghostly

__Gift of the Wild__ = gotw

__Gouge__ = gouge

__Grace of Air__ = graceTotem

__Greater Blessing of Kings__ = gBlessKings

__Greater Blessing of Light__ = gBlessLight

__Greater Blessing of Might__ = gBlessMight

__Greater Blessing of Salvation__ = gBlessSlv

__Greater Blessing of Sanctuary__ = gBlessSnct

__Greater Blessing of Wisdom__ = gBlessWisdom

__Hamstring__ = hamstring

__Healing Stream__ = hsTotem

__Health Funnel__ = funnel

__Hellfire__ = hellfire

__Hemorrhage__ = hemo

__Hex of Weakness__ = hexWeakness

__Hibernate__ = hibernate

__Holy Fire__ = holyFire

__Holy Shield__ = holyShield

__Howl of Terror__ = howl

__Hunter's Mark__ = huntersMark

__Ice Armor__ = iceArmor

__Ice Barrier__ = iceBarrier

__Ice Block__ = iceBlock

__Ignite__ = ignite

__Immolate__ = immolate

__Immolation Trap Effect__ = immolationTrap

__Inner Fire__ = innerFire

__Inner Focus__ = innerFocus

__Innervate__ = innervate

__Insect Swarm__ = swarm

__Intimidating Shout__ = intimidatingShout

__Intimidation__ = intimidate

__Judgement of Justice__ = judgeJustice

__Judgement of Light__ = judgeLight

__Judgement of Wisdom__ = judgeWisdom

__Judgement of the Crusader__ = judgeCrusader

__Kidney Shot__ = ks

__Last Stand__ = lastStand

__Levitate__ = levitate

__Lightning Shield__ = lightShield

__Lightwell Renew__ = lightwellRenew

__Lightwell__ = lightwell

__Mage Armor__ = mageArmor

__Mana Shield__ = manaShield

__Mana Spring__ = msTotem

__Mana Tide__ = mtTotem

__Mark of the Wild__ = motw

__Mind Control__ = mindControl

__Mind Flay__ = mindFlay

__Mind Soothe__ = mindSoothe

__Mind Vision__ = mindVision

__Mocking Blow__ = mockingBlow

__Moonfire__ = moonfire

__Moonkin Form__ = moonkin

__Mortal Strike__ = mortalStrike

__Nature Resistance__ = natureResistTotem

__Nature's Grasp__ = grasp

__Nature's Swiftness__ = ns

__Omen of Clarity__ = ooc

__Piercing Howl__ = piercingHowl

__Polymorph: Pig__ = polymorphPig

__Polymorph: Turtle__ = polymorphTurtle

__Polymorph__ = polymorph

__Pounce Bleed__ = pounce

__Power Infusion__ = powerInfusion

__Power Word: Fortitude__ = pwf

__Power Word: Shield__ = pws

__Prayer of Fortitude__ = prf

__Prayer of Shadow Protection__ = prsp

__Prayer of Spirit__ = prs

__Prowl__ = petProwl

__Prowl__ = prowl

__Psychic Scream__ = psychicScream

__Quick Shots__ = quickShots

__Rake__ = rake

__Rapid Fire__ = rapidFire

__Recently Bandaged__ = recentlyBandaged

__Recklessness__ = recklessness

__Redoubt__ = redoubt

__Regrowth__ = regrowth

__Rejuvenation__ = rejuv

__Remorseless__ = remorseless

__Rend__ = rend

__Renew__ = renew

__Repentance__ = repentance

__Retaliation__ = retaliation

__Retribution Aura__ = retAura

__Righteous Fury__ = rightFury

__Rip__ = rip

__Rupture__ = rupture

__Sacrifice__ = sacrifice

__Sanctity Aura__ = sanctAura

__Sap__ = sap

__Scare Beast__ = scare

__Scatter Shot__ = scatter

__Scorpid Sting__ = scorpid

__Seal of Command__ = sealCommand

__Seal of Justice__ = sealJustice

__Seal of Light__ = sealLight

__Seal of Righteousness__ = sealRight

__Seal of Wisdom__ = sealWisdom

__Seal of the Crusader__ = sealCrusader

__Seduction__ = seduction

__Sense Demons__ = senseDemons

__Serpent Sting__ = serpent

__Shackle Undead__ = shackleUndead

__Shadow Protection__ = shadowProtection

__Shadow Resistance Aura__ = shadowAura

__Shadow Trance__ = shadowTrance

__Shadow Vulnerability__ = shadowVulnerability

__Shadow Ward__ = shadowWard

__Shadow Word: Pain__ = swp

__Shadowburn__ = shadowburn

__Shadowform__ = shadowform

__Shadowguard__ = shadowguard

__Shadowmeld__ = shadowmeld

__Shield Block__ = shieldBlock

__Shield Wall__ = shieldWall

__Silverwing Flag__ = silverwingFlag

__Siphon Life__ = siphon

__Slice and Dice__ = snd

__Soothe Animal__ = soothe

__Soul Link__ = soulLink

__Spirit Tap__ = spiritTap

__Starshards__ = starshards

__Stealth__ = stealth

__Stoneskin__ = skinTotem

__Strength of Earth__ = strengthTotem

__Sunder Armor__ = sunder

__Sweeping Strikes__ = sweepingStrikes

__Thorns__ = thorns

__Thunder Clap__ = thunderClap

__Tiger's Fury__ = tigersFury

__Touch of Weakness__ = touchWeakness

__Tranquil Air__ = tranquilTotem

__Tranquility__ = tranquility

__Travel Form__ = travel

__Trueshot Aura__ = trueshot

__Vampiric Embrace__ = vampiricEmbrace

__Vanish__ = vanish

__Viper Sting__ = viper

__Warsong Flag__ = warsongFlag

__Weakened Soul__ = weakenedSoul

__Well Fed__ = wellFed

__Whirlwind__ = whirlwind

__Windfury__ = wfTotem

__Windwall__ = windwallTotem

__Wing Clip__ = wingClip

__Wyvern Sting__ = wyvern

__Wyvern Sting__ = wyvernCC

__Wyvern Sting__ = wyvernDot

\

