## Interface: 11200
## Title: LazyScript
## Title-zhCN: 懒人神器 - |cffDD2222主程序|r
## Version: 1.0.4
## X-Revision: $Revision: 800 $
## X-LazyScriptCompatibility: 3
## Notes: Programmable Class Attacks.
## Notes-zhCN: 懒人神器主程序，所有职业的个性脚本均需要加载。
## OptionalDeps: MobInfo-2, Tracer
## SavedVariablesPerCharacter: LazyScriptPatchAssistTarget, lsConf

LazyScript.lua
Util.lua
Localization.lua
About.lua
Actions.lua
AutoAttack.lua
Deathstimator.lua
FormEdit.lua
ImmunityTypeTracking.lua
Interrupt.lua
MinimapMenu.lua
Minion.lua
Parse.lua
ParseGeneral.lua
ParseBuffs.lua
Tooltip.lua
LazyScript.xml
Bindings.xml
About.xml
Deathstimator.xml
FormEdit.xml
Interrupt.xml
Immunity.xml
MinimapMenu.xml
Minion.xml
Tooltip.xml
LazyScript_PatchCustom.lua