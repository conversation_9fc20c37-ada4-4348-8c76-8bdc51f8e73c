pfUI_locale["enUS"] = {}

pfUI_locale["enUS"]["class"] = {
  ["Warlock"] = "WARLOCK",
  ["Warrior"] = "WARRIOR",
  ["<PERSON>"] = "HUNTER",
  ["Mage"] = "MAGE",
  ["Priest"] = "PRIEST",
  ["Druid"] = "DRUID",
  ["Paladin"] = "PALADIN",
  ["Shaman"] = "SHAMAN",
  ["Rogue"] = "ROGUE",
}

pfUI_locale["enUS"]["healduration"] = {
  ["Rejuvenation"] = "Increases the duration of your Rejuvenation spell by 3 sec.",
  ["Renew"] = "Increases the duration of your Renew spell by 3 sec.",
}

pfUI_locale["enUS"]["bagtypes"] = {
  ["Quiver"] = "QUIVER",
  ["Soul Bag"] = "SOULBAG",
  ["Bag"] = "DEFAULT",
}

pfUI_locale["enUS"]["itemtypes"] = {
  ["INVTYPE_WAND"] = "Wand",
  ["INVTYPE_THROWN"] = "Thrown",
  ["INVTYPE_GUN"] = "Gun",
  ["INVTYPE_CROSSBOW"] = "Crossbow",
  ["INVTYPE_PROJECTILE"] = "Projectile",
}

pfUI_locale["enUS"]["hunterpaging"] = {
  ["MELEE"] = "Wing Clip",
  ["RANGED"] = "Arcane Shot",
}

pfUI_locale["enUS"]["customcast"] = {
  ["AIMEDSHOT"] = "Aimed Shot",
  ["MULTISHOT"] = "Multi-Shot",
}

pfUI_locale["enUS"]["critters"] = {
  'Adder',
  'Beetle',
  'Belfry Bat',
  'Biletoad',
  'Black Rat',
  'Brown Prairie Dog',
  'Caged Rabbit',
  'Caged Sheep',
  'Caged Squirrel',
  'Caged Toad',
  'Cat',
  'Chicken',
  'Cleo',
  'Core Rat',
  'Cow',
  'Cow',
  'Cured Deer',
  'Cured Gazelle',
  'Deeprun Rat',
  'Deer',
  'Dog',
  'Effsee',
  'Enthralled Deeprun Rat',
  'Fang',
  'Fawn',
  'Fire Beetle',
  'Fluffy',
  'Frog',
  'Gazelle',
  'Hare',
  'Horse',
  'Huge Toad',
  'Infected Deer',
  'Infected Squirrel',
  'Jungle Toad',
  'Krakle\'s Thermometer',
  'Lady',
  'Larva',
  'Lava Crab',
  'Maggot',
  'Moccasin',
  'Mouse',
  'Mr. Bigglesworth',
  'Nibbles',
  'Noarm',
  'Old Blanchy',
  'Parrot',
  'Pig',
  'Pirate treasure trigger mob',
  'Plagued Insect',
  'Plagued Maggot',
  'Plagued Rat',
  'Plagueland Termite',
  'Polymorphed Chicken',
  'Polymorphed Rat',
  'Prairie Dog',
  'Rabbit',
  'Ram',
  'Rat',
  'Riding Ram',
  'Roach',
  'Salome',
  'School of Fish',
  'Scorpion',
  'Sheep',
  'Sheep',
  'Shen\'dralar Wisp',
  'Sickly Deer',
  'Sickly Gazelle',
  'Snake',
  'Spider',
  'Spike',
  'Squirrel',
  'Swine',
  'Tainted Cockroach',
  'Tainted Rat',
  'Toad',
  'Transporter Malfunction',
  'Turtle',
  'Underfoot',
  'Voice of Elune',
  'Waypoint (Only GM can see it)',
  'Wisp',
}

pfUI_locale["enUS"]["dyndebuffs"] = {
  ["Rupture"] = "Rupture",
  ["Kidney Shot"] = "Kidney Shot",
  ["Rend"] = "Rend",
  ["Shadow Word: Pain"] = "Shadow Word: Pain",
  ["Demoralizing Shout"] = "Demoralizing Shout",
  ["Frostbolt"] = "Frostbolt",
  ["Gouge"] = "Gouge",
}

pfUI_locale["enUS"]["judgements"] = {
  ["Judgement of Justice"] = true,
  ["Judgement of Light"] = true,
  ["Judgement of Wisdom"] = true,
  ["Judgement of the Crusader"] = true,
}

pfUI_locale["enUS"]["interrupts"] = {
  ["Shield Bash"] = true,
  ["Pummel"] = true,
  ["Kick"] = true,
  ["Earth Shock"] = true,
  ["War Stomp"] = true,
  ["Concussion Blow"] = true,
  ["Charge Stun"] = true,
  ["Intercept Stun"] = true,
  ["Hammer of Justice"] = true,
  ["Cheap Shot"] = true,
  ["Gouge"] = true,
  ["Kidney Shot"] = true,
  ["Silence"] = true,
  ["Counterspell"] = true,
  ["Counterspell - Silenced"] = true,
  ["Bash"] = true,
  ["Fear"] = true,
  ["Howl of Terror"] = true,
  ["Psychic Scream"] = true,
  ["Intimidating Shout"] = true,
  ["Starfire Stun"] = true,
  ["Revenge Stun"] = true,
  ["Improved Concussive Shot"] = true,
  ["Impact"] = true,
  ["Pyroclasm"] = true,
  ["Blackout"] = true,
  ["Stun"] = true,
  ["Mace Stun Effect"] = true,
  ["Earthshaker"] = true,
  ["Repentance"] = true,
  ["Scatter Shot"] = true,
  ["Blind"] = true,
  ["Hibernate"] = true,
  ["Wyvern Sting"] = true,
  ["Rough Copper Bomb"] = true,
  ["Large Copper Bomb"] = true,
  ["Small Bronze Bomb"] = true,
  ["Big Bronze Bomb"] = true,
  ["Big Iron Bomb"] = true,
  ["Mithril Frag Bomb"] = true,
  ["Hi-Explosive Bomb"] = true,
  ["Dark Iron Bomb"] = true,
  ["Iron Grenade"] = true,
  ["M73 Frag Grenade"] = true,
  ["Thorium Grenade"] = true,
  ["Goblin Mortar"] = true,
}

pfUI_locale["enUS"]["resurrections"] = {
  ["Resurrection"] = true,
  ["Rebirth"] = true,
  ["Redemption"] = true,
  ["Ancestral Spirit"] = true,
}

pfUI_locale["enUS"]["spells"] = {
  ['"Plucky" Resumes Chicken Form']={t=1000,icon='Ability_Racial_BearForm'},
  ['Abomination Spit']={t=2500,icon='Spell_Nature_CorrosiveBreath'},
  ['Acid Spit']={t=3000,icon='Spell_Nature_Acid_01'},
  ['Acid Splash']={t=1000,icon='INV_Drink_06'},
  ['Acid Spray']={t=2000,icon='Spell_Nature_Acid_01'},
  ['Acid of Hakkar']={t=1000,icon='Spell_Nature_Acid_01'},
  ['Activating Defenses']={t=5000,icon='Temp'},
  ['Adjust Attitude']={t=2000,icon='INV_Gizmo_01'},
  ['Aimed Shot']={t=3000,icon='INV_Spear_07'},
  ['Amplify Damage']={t=2000,icon='Spell_Nature_AbolishMagic'},
  ['Amplify Flames']={t=1000,icon='Spell_Fire_Fireball'},
  ['Ancestral Spirit']={t=10000,icon='Spell_Nature_Regenerate'},
  ['Anti-Magic Shield']={t=2000,icon='Spell_Shadow_AntiMagicShell'},
  ['Apply Salve']={t=1300,icon='Temp'},
  ['Apply Seduction Gland']={t=2500,icon='INV_Misc_Bowl_01'},
  ['Applying the Lure']={t=4000,icon='Temp'},
  ['Aquadynamic Fish Attractor']={t=5000,icon='INV_Misc_Orb_03'},
  ['Aquadynamic Fish Lens']={t=5000,icon='INV_Misc_Spyglass_01'},
  ['Aqual Quintessence - Dowse Molten Core Rune']={t=1000,icon='Temp'},
  ['Arcane Bolt']={t=1000,icon='Spell_Arcane_StarFire'},
  ['Arcane Bomb']={t=1500,icon='Spell_Holy_Silence'},
  ['Arcane Explosion']={t=1500,icon='Spell_Nature_WispSplode'},
  ['Arcane Spirit II']={t=1000,icon='Spell_Holy_MagicalSentry'},
  ['Arcane Spirit III']={t=1000,icon='Spell_Holy_MagicalSentry'},
  ['Arcane Spirit IV']={t=1000,icon='Spell_Holy_MagicalSentry'},
  ['Arcane Spirit V']={t=1000,icon='Spell_Holy_MagicalSentry'},
  ['Arcane Weakness']={t=5000,icon='INV_Misc_QirajiCrystal_01'},
  ['Arcanite Skeleton Key']={t=5000,icon='Temp'},
  ['Archaedas Awaken Visual (DND)']={t=1500,icon='Spell_Nature_Earthquake'},
  ['Arctic Wolf']={t=3000,icon='Ability_Mount_WhiteDireWolf'},
  ['Area Burn']={t=3000,icon='Spell_Fire_SelfDestruct'},
  ['Arugal spawn-in spell']={t=2000,icon='Temp'},
  ['Arugal\'s Gift']={t=2500,icon='Spell_Shadow_ChillTouch'},
  ['Arygos\'s Vengeance']={t=2000,icon='Temp'},
  ['Ashcrombe\'s Teleport']={t=2000,icon='Spell_Fire_SelfDestruct'},
  ['Ashcrombe\'s Unlock']={t=4000,icon='Spell_Nature_MoonKey'},
  ['Aspect of Neptulon']={t=1000,icon='Temp'},
  ['Astral Recall']={t=10000,icon='Spell_Nature_AstralRecal'},
  ['Atal\'ai Altar Light Visual (DND)']={t=1000,icon='Temp'},
  ['Attuned Dampener']={t=2000,icon='Spell_Holy_SearingLight'},
  ['Awaken Kerlonian']={t=4500,icon='Temp'},
  ['Awaken Vault Warder']={t=5000,icon='Spell_Nature_Earthquake'},
  ['Awaken the Soulflayer']={t=5000,icon='Temp'},
  ['Aynasha\'s Arrow']={t=500,icon='Temp'},
  ['Backhand']={t=1000,icon='Spell_Shadow_LifeDrain'},
  ['Balance of Nature']={t=3000,icon='Temp'},
  ['Balance of Nature Failure']={t=10000,icon='Temp'},
  ['Ball Lightning']={t=1000,icon='Spell_Lightning_LightningBolt01'},
  ['Banish']={t=1500,icon='Spell_Shadow_Cripple'},
  ['Banish Burning Exile']={t=1000,icon='Spell_Shadow_LifeDrain'},
  ['Banish Cresting Exile']={t=1000,icon='Spell_Shadow_LifeDrain'},
  ['Banish Thundering Exile']={t=1000,icon='Spell_Shadow_LifeDrain'},
  ['Banshee Curse']={t=2000,icon='Spell_Nature_Drowsy'},
  ['Banshee Wail']={t=1500,icon='Spell_Shadow_ShadowBolt'},
  ['Barrier of Light']={t=2000,icon='Temp'},
  ['Basic Campfire']={t=10000,icon='Spell_Fire_Fire'},
  ['Beast Claws']={t=1000,icon='Spell_Nature_Regeneration'},
  ['Beast Claws II']={t=1000,icon='Spell_Nature_Regeneration'},
  ['Beast Claws III']={t=1000,icon='Spell_Nature_Regeneration'},
  ['Bellowing Roar']={t=1500,icon='Spell_Fire_Fire'},
  ['Bending Shinbone']={t=1500,icon='Temp'},
  ['Big Bronze Bomb']={t=1000,icon='Spell_Fire_SelfDestruct'},
  ['Big Iron Bomb']={t=1000,icon='Spell_Fire_SelfDestruct'},
  ['Birth']={t=2000,icon='Temp'},
  ['Black Arrow']={t=2000,icon='Ability_TheBlackArrow'},
  ['Black Battlestrider']={t=3000,icon='Ability_Mount_MechaStrider'},
  ['Black Ram']={t=3000,icon='Ability_Mount_MountainRam'},
  ['Black Sludge']={t=3000,icon='Spell_Shadow_CallofBone'},
  ['Black Stallion']={t=3000,icon='Ability_Mount_NightmareHorse'},
  ['Black War Kodo']={t=3000,icon='Ability_Mount_Kodo_01'},
  ['Black War Ram']={t=3000,icon='Ability_Mount_MountainRam'},
  ['Black War Raptor']={t=3000,icon='Ability_Mount_Raptor'},
  ['Black War Steed']={t=3000,icon='Ability_Mount_NightmareHorse'},
  ['Black War Tiger']={t=3000,icon='Ability_Mount_BlackPanther'},
  ['Black War Wolf']={t=3000,icon='Ability_Mount_BlackDireWolf'},
  ['Black Wolf']={t=3000,icon='Ability_Mount_BlackDireWolf'},
  ['Blessed Wizard Oil']={t=3000,icon='Temp'},
  ['Blessing of Shahram']={t=1000,icon='Spell_Holy_LayOnHands'},
  ['Blizzard']={t=2000,icon='Spell_Frost_IceStorm'},
  ['Blood Howl']={t=1000,icon='Spell_Shadow_LifeDrain'},
  ['Blue Dragon Transform DND']={t=1000,icon='Temp'},
  ['Blue Mechanostrider']={t=3000,icon='Ability_Mount_MechaStrider'},
  ['Blue Ram']={t=3000,icon='Ability_Mount_MountainRam'},
  ['Blue Skeletal Horse']={t=3000,icon='Ability_Mount_Undeadhorse'},
  ['Bly\'s Band\'s Escape']={t=10000,icon='INV_Misc_Rune_01'},
  ['Bomb']={t=2000,icon='Spell_Fire_SelfDestruct'},
  ['Bombard']={t=3000,icon='Ability_GolemStormBolt'},
  ['Bombard II']={t=3000,icon='Ability_GolemStormBolt'},
  ['Bombard Slime']={t=1000,icon='Temp'},
  ['Bone Shards']={t=500,icon='Spell_Shadow_ScourgeBuild'},
  ['Boulder']={t=2000,icon='Ability_Throw'},
  ['Break Big Stuff']={t=2000,icon='Spell_Shadow_CurseOfAchimonde'},
  ['Break Stuff']={t=2000,icon='Spell_Shadow_CurseOfAchimonde'},
  ['Breath']={t=5000,icon='Spell_Fire_Fire'},
  ['Breath of Sargeras']={t=2000,icon='Spell_Shadow_Metamorphosis'},
  ['Bright Baubles']={t=5000,icon='INV_Misc_Orb_03'},
  ['Bright Campfire']={t=10000,icon='Spell_Fire_Fire'},
  ['Brilliant Mana Oil']={t=3000,icon='Temp'},
  ['Brilliant Wizard Oil']={t=3000,icon='Temp'},
  ['Brood of Nozdormu Factoin +1000']={t=1000,icon='Temp'},
  ['Brown Horse']={t=3000,icon='Ability_Mount_RidingHorse'},
  ['Brown Kodo']={t=3000,icon='Ability_Mount_Kodo_03'},
  ['Brown Ram']={t=3000,icon='Ability_Mount_MountainRam'},
  ['Brown Skeletal Horse']={t=3000,icon='Ability_Mount_Undeadhorse'},
  ['Brown Wolf']={t=3000,icon='Ability_Mount_BlackDireWolf'},
  ['Burning Winds']={t=1000,icon='Spell_Nature_Cyclone'},
  ['Burrow']={t=1000,icon='Ability_Vanish'},
  ['Bury Samuel\'s Remains']={t=2000,icon='Temp'},
  ['Buttermilk Delight']={t=1000,icon='INV_ValentinesChocolate01'},
  ['CHU\'s QUEST SPELL']={t=4000,icon='Spell_Shadow_LifeDrain'},
  ['Call Anathema']={t=1000,icon='Temp'},
  ['Call Ancients']={t=7000,icon='Temp'},
  ['Call Benediction']={t=1000,icon='Temp'},
  ['Call Bleak Worg']={t=1300,icon='Spell_Shadow_ChillTouch'},
  ['Call Glyphs of Warding']={t=3000,icon='Temp'},
  ['Call Lupine Horror']={t=1300,icon='Spell_Shadow_ChillTouch'},
  ['Call Prismatic Barrier']={t=10000,icon='Temp'},
  ['Call Slavering Worg']={t=1300,icon='Spell_Shadow_ChillTouch'},
  ['Call of Thund']={t=1500,icon='Spell_Frost_Wisp'},
  ['Call of the Grave']={t=2000,icon='Spell_Shadow_ChillTouch'},
  ['Call of the Nether']={t=10000,icon='Temp'},
  ['Call of the Void']={t=3000,icon='Spell_Shadow_DeathCoil'},
  ['Call to Ivus']={t=10000,icon='Temp'},
  ['Cannon Fire']={t=1000,icon='Spell_Fire_FireBolt02'},
  ['Cantation of Manifestation']={t=2000,icon='Spell_Magic_LesserInvisibilty'},
  ['Capture Grark']={t=3000,icon='Temp'},
  ['Capture Worg Pup']={t=2500,icon='Temp'},
  ['Capturing Termites']={t=5000,icon='Temp'},
  ['Cascade of Roses']={t=500,icon='INV_Misc_Dust_04'},
  ['Chain Bolt']={t=2500,icon='Spell_Nature_ChainLightning'},
  ['Chain Burn']={t=3000,icon='Spell_Shadow_ManaBurn'},
  ['Chain Heal']={t=2500,icon='Spell_Nature_HealingWaveGreater'},
  ['Chain Lightning']={t=2500,icon='Spell_Nature_ChainLightning'},
  ['Chained Bolt']={t=1800,icon='Spell_Nature_ChainLightning'},
  ['Chains of Ice']={t=1300,icon='Spell_Frost_ChainsOfIce'},
  ['Charged Arcane Bolt']={t=7000,icon='Spell_Arcane_StarFire'},
  ['Charging']={t=5000,icon='Spell_Shadow_EvilEye'},
  ['Chestnut Mare']={t=3000,icon='Ability_Mount_RidingHorse'},
  ['Chilling Breath']={t=1000,icon='Spell_Frost_Wisp'},
  ['Chromatic Mount']={t=3000,icon='INV_Misc_Head_Dragon_Black'},
  ['Clean Up Stink Bomb']={t=5000,icon='Temp'},
  ['Cleanse Thunderhorn Well']={t=10000,icon='Temp'},
  ['Cleanse Wildmane Well']={t=10000,icon='Temp'},
  ['Cleanse Winterhoof Well']={t=10000,icon='Temp'},
  ['Cleave']={t=2500,icon='Ability_Warrior_Cleave'},
  ['Clone']={t=2500,icon='Spell_Shadow_BlackPlague'},
  ['Closing']={t=1000,icon='Temp'},
  ['Coarse Dynamite']={t=1000,icon='Spell_Fire_SelfDestruct'},
  ['Collect Rookery Egg']={t=500,icon='Temp'},
  ['Collecting Fallout']={t=4000,icon='Temp'},
  ['Colossal Smash']={t=5000,icon='Temp'},
  ['Conjure Altar of Summoning']={t=10000,icon='Temp'},
  ['Conjure Circle of Calling']={t=10000,icon='Temp'},
  ['Conjure Dream Rift']={t=10000,icon='Temp'},
  ['Conjure Furis Felsteed DUMMY DND']={t=5000,icon='Temp'},
  ['Consecrated Weapon']={t=3000,icon='Temp'},
  ['Cookie\'s Cooking']={t=2000,icon='Spell_Holy_Heal'},
  ['Corrosive Acid']={t=1500,icon='Spell_Nature_Acid_01'},
  ['Corrosive Acid Spit']={t=3000,icon='Spell_Nature_Acid_01'},
  ['Corrosive Poison']={t=1500,icon='Spell_Nature_CorrosiveBreath'},
  ['Corrosive Venom Spit']={t=2500,icon='Spell_Nature_CorrosiveBreath'},
  ['Corrupt Redpath']={t=2000,icon='Temp'},
  ['Corruption']={t=2000,icon='Spell_Shadow_AbominationExplosion'},
  ['Crash of Waves']={t=2000,icon='Spell_Frost_FrostNova'},
  ['Create Cleansing Totem']={t=5000,icon='Spell_Shadow_LifeDrain'},
  ['Create Cluster Rocket Launcher']={t=2000,icon='INV_Misc_EngGizmos_03'},
  ['Create Containment Coffer']={t=500,icon='Temp'},
  ['Create Filled Containment Coffer']={t=2000,icon='Temp'},
  ['Create Firework Rocket Launcher']={t=2000,icon='INV_Musket_04'},
  ['Create Healthstone']={t=3000,icon='INV_Stone_04'},
  ['Create Healthstone (Greater)']={t=3000,icon='INV_Stone_04'},
  ['Create Healthstone (Lesser)']={t=3000,icon='INV_Stone_04'},
  ['Create Healthstone (Major)']={t=3000,icon='INV_Stone_04'},
  ['Create Healthstone (Minor)']={t=3000,icon='INV_Stone_04'},
  ['Create Item Visual (DND)']={t=5000,icon='Spell_Shadow_SoulGem'},
  ['Create Mage\'s Orb']={t=4000,icon='Temp'},
  ['Create Mage\'s Robe']={t=4000,icon='Temp'},
  ['Create PX83-Enigmatron']={t=2000,icon='INV_Misc_Bowl_01'},
  ['Create Relic Bundle']={t=1000,icon='Temp'},
  ['Create Rift']={t=3000,icon='Temp'},
  ['Create Sapta']={t=3000,icon='INV_Misc_Food_09'},
  ['Create Scroll']={t=5000,icon='INV_Scroll_05'},
  ['Create Scrying Bowl']={t=2500,icon='INV_Misc_Bowl_01'},
  ['Create Shredder']={t=1000,icon='INV_Misc_Gear_01'},
  ['Create Water of the Seers']={t=5000,icon='Spell_Shadow_LifeDrain'},
  ['Create Witherbark Totem Bundle']={t=2000,icon='Spell_Lightning_LightningBolt01'},
  ['CreatureSpecial']={t=2000,icon='Temp'},
  ['Creeper Venom']={t=2000,icon='Spell_Nature_NullifyPoison'},
  ['Creeping Mold']={t=3000,icon='Spell_Shadow_CreepingPlague'},
  ['Crest of Retribution']={t=1000,icon='INV_Shield_19'},
  ['Cripple']={t=3000,icon='Spell_Shadow_Cripple'},
  ['Crippling Poison']={t=3000,icon='Ability_PoisonSting'},
  ['Crypt Scarabs']={t=1500,icon='Spell_Shadow_CarrionSwarm'},
  ['Crystal Flash']={t=2000,icon='Spell_Shadow_Teleport'},
  ['Crystal Gaze']={t=2000,icon='Ability_GolemThunderClap'},
  ['Crystalline Slumber']={t=2000,icon='Spell_Nature_Sleep'},
  ['Cupid\'s Arrow']={t=1000,icon='Temp'},
  ['Cure Disease']={t=2500,icon='Spell_Holy_NullifyDisease'},
  ['Curse of Blood']={t=2000,icon='Spell_Shadow_RitualOfSacrifice'},
  ['Curse of Hakkar']={t=2500,icon='Spell_Shadow_GatherShadows'},
  ['Curse of Mending']={t=1000,icon='Spell_Shadow_AntiShadow'},
  ['Curse of Shahram']={t=1000,icon='Spell_Magic_LesserInvisibilty'},
  ['Curse of Stalvan']={t=1000,icon='Spell_Shadow_ShadowPact'},
  ['Curse of Thorns']={t=2000,icon='Spell_Shadow_AntiShadow'},
  ['Curse of Thule']={t=2000,icon='Spell_Shadow_ShadowPact'},
  ['Curse of Timmy']={t=1000,icon='Spell_Shadow_ShadowPact'},
  ['Curse of Weakness']={t=1000,icon='Spell_Shadow_CurseOfMannoroth'},
  ['Curse of the Darkmaster']={t=2000,icon='Spell_Shadow_AntiShadow'},
  ['Curse of the Deadwood']={t=2000,icon='Spell_Shadow_GatherShadows'},
  ['Curse of the Fallen Magram']={t=2000,icon='Spell_Shadow_UnholyFrenzy'},
  ['Curse of the Firebrand']={t=2000,icon='Ability_Creature_Cursed_03'},
  ['Curse of the Plague Rat']={t=1500,icon='Spell_Shadow_UnholyFrenzy'},
  ['Curse of the Tribes']={t=2000,icon='Spell_Shadow_CurseOfMannoroth'},
  ['Dalaran Wizard Disguise']={t=3000,icon='Ability_Rogue_Disguise'},
  ['Damage Car']={t=2000,icon='Spell_Fire_Fire'},
  ['Dark Desire']={t=1000,icon='INV_ValentinesChocolate04'},
  ['Dark Iron Bomb']={t=1000,icon='Spell_Fire_SelfDestruct'},
  ['Dark Iron Dwarf Disguise']={t=3000,icon='Ability_Rogue_Disguise'},
  ['Dark Iron Land Mine']={t=1000,icon='Spell_Shadow_Metamorphosis'},
  ['Dark Mending']={t=3500,icon='Spell_Shadow_ChillTouch'},
  ['Dark Restore']={t=2000,icon='Ability_Hunter_MendPet'},
  ['Dark Sludge']={t=5000,icon='Spell_Shadow_CreepingPlague'},
  ['Dark Whispers']={t=3000,icon='Spell_Shadow_Haunting'},
  ['Darken Vision']={t=2000,icon='Spell_Shadow_Fumble'},
  ['Daunting Growl']={t=1000,icon='Ability_Racial_Cannibalize'},
  ['Dawn\'s Gambit']={t=1500,icon='Temp'},
  ['Deadly Poison']={t=3000,icon='Ability_Rogue_DualWeild'},
  ['Deadly Poison II']={t=3000,icon='Ability_Rogue_DualWeild'},
  ['Deadly Poison III']={t=3000,icon='Ability_Rogue_DualWeild'},
  ['Deadly Poison IV']={t=3000,icon='Ability_Rogue_DualWeild'},
  ['Deadly Poison V']={t=3000,icon='Ability_Rogue_DualWeild'},
  ['Deadmines Dynamite']={t=2000,icon='Spell_Fire_SelfDestruct'},
  ['Death & Decay']={t=2000,icon='Spell_Shadow_DeathAndDecay'},
  ['Death Bed']={t=2000,icon='Spell_Shadow_Twilight'},
  ['Deathcharger']={t=3000,icon='Ability_Mount_Undeadhorse'},
  ['Decayed Agility']={t=2000,icon='Spell_Holy_HarmUndeadAura'},
  ['Decayed Strength']={t=2000,icon='Spell_Holy_HarmUndeadAura'},
  ['Deep Slumber']={t=1000,icon='Spell_Shadow_Cripple'},
  ['Defias Disguise']={t=3000,icon='Ability_Rogue_Disguise'},
  ['Defibrillate']={t=4000,icon='Spell_Nature_Purge'},
  ['Delete Me']={t=4000,icon='INV_Scroll_02'},
  ['Demon Pick']={t=5000,icon='Temp'},
  ['Demon Portal']={t=500,icon='Spell_Arcane_TeleportOrgrimmar'},
  ['Demon Summoning Torch']={t=3000,icon='Temp'},
  ['Dense Dynamite']={t=1000,icon='Spell_Fire_SelfDestruct'},
  ['Destroy Egg']={t=3000,icon='INV_Misc_MonsterClaw_02'},
  ['Destroy Ghost Magnet']={t=10000,icon='Temp'},
  ['Destroy Tent']={t=2500,icon='Temp'},
  ['Detonate']={t=2000,icon='Temp'},
  ['Detonation']={t=5000,icon='Spell_Fire_SelfDestruct'},
  ['Digging for Cobalt']={t=1500,icon='Temp'},
  ['Dimensional Portal']={t=2000,icon='Temp'},
  ['Dire Growl']={t=1000,icon='Ability_Racial_Cannibalize'},
  ['Dire Wolf']={t=3000,icon='Ability_Mount_WhiteDireWolf'},
  ['Disarm Trap']={t=2000,icon='Spell_Shadow_GrimWard'},
  ['Disease Buffet']={t=1500,icon='Spell_Nature_EarthBind'},
  ['Diseased Shot']={t=2000,icon='Spell_Shadow_CallofBone'},
  ['Diseased Slime']={t=2000,icon='Spell_Shadow_CreepingPlague'},
  ['Diseased Spit']={t=3000,icon='Spell_Shadow_CreepingPlague'},
  ['Disenchant']={t=3000,icon='Spell_Holy_RemoveCurse'},
  ['Dismiss Pet']={t=5000,icon='Spell_Nature_SpiritWolf'},
  ['Dispel']={t=1000,icon='Spell_Holy_DispelMagic'},
  ['Dispel Poison']={t=2000,icon='Spell_Holy_Purify'},
  ['Displacing Temporal Rift']={t=5000,icon='Temp'},
  ['Disruption']={t=3000,icon='Temp'},
  ['Disturb Rookery Egg']={t=1000,icon='Temp'},
  ['Divining Trance']={t=5000,icon='Temp'},
  ['Dominate Mind']={t=2000,icon='Spell_Shadow_ShadowWordDominate'},
  ['Domination']={t=1000,icon='Spell_Shadow_ShadowWordDominate'},
  ['Dominion of Soul']={t=3000,icon='Spell_Shadow_ShadowWordDominate'},
  ['Dousing']={t=5000,icon='Temp'},
  ['Dowse Eternal Flame']={t=1000,icon='Temp'},
  ['Draco-Incarcinatrix 900']={t=2000,icon='Temp'},
  ['Draw Ancient Glyphs']={t=10000,icon='Temp'},
  ['Draw of Thistlenettle']={t=2000,icon='Spell_Shadow_Haunting'},
  ['Drawing Kit']={t=7000,icon='Temp'},
  ['Drink Minor Potion']={t=3000,icon='Spell_Holy_Heal'},
  ['Drink Potion']={t=3000,icon='Spell_Holy_Heal'},
  ['Drive Nimboya\'s Laden Pike']={t=2000,icon='Temp'},
  ['Druid\'s Slumber']={t=2500,icon='Spell_Nature_Sleep'},
  ['Drunken Pit Crew']={t=2000,icon='Temp'},
  ['Dummy NPC Summon']={t=20000,icon='Spell_Shadow_UnsummonBuilding'},
  ['Dummy Nuke']={t=2000,icon='Temp'},
  ['Dust Cloud']={t=1500,icon='Ability_Hibernation'},
  ['Dynamite']={t=1000,icon='Spell_Fire_SelfDestruct'},
  ['Earth Elemental']={t=3000,icon='Ability_Temp'},
  ['Earthgrab Totem']={t=500,icon='Spell_Nature_NatureTouchDecay'},
  ['Editor Test Spell']={t=250,icon='Temp'},
  ['Elderberry Pie']={t=1000,icon='INV_Misc_Food_10'},
  ['Electrified Net']={t=500,icon='Ability_Ensnare'},
  ['Elemental Armor']={t=1000,icon='Spell_Frost_Frost'},
  ['Elemental Fire']={t=500,icon='Spell_Fire_Fireball02'},
  ['Elemental Fury']={t=1000,icon='Spell_Fire_FireArmor'},
  ['Elune\'s Candle']={t=500,icon='Temp'},
  ['Emberseer Start']={t=5000,icon='Spell_Nature_EarthBindTotem'},
  ['Emerald Raptor']={t=3000,icon='Ability_Mount_Raptor'},
  ['Empower Pet']={t=500,icon='Spell_Shadow_DeathPact'},
  ['Empty Festive Mug']={t=2000,icon='Temp'},
  ['Encage']={t=2000,icon='Spell_Shadow_Teleport'},
  ['Encasing Webs']={t=2000,icon='Spell_Nature_EarthBind'},
  ['Enchant Annals of Darrowshire']={t=3500,icon='Temp'},
  ['Enchanted Gaea Seed']={t=5000,icon='Temp'},
  ['Enchanted Quickness']={t=3000,icon='Spell_Nature_Invisibilty'},
  ['Enchanted Resonite Crystal']={t=5000,icon='Temp'},
  ['Enchanting Lullaby']={t=1000,icon='Spell_Shadow_SoothingKiss'},
  ['Energy Siphon']={t=1500,icon='Spell_Shadow_Cripple'},
  ['Enervate']={t=1500,icon='Ability_Creature_Poison_03'},
  ['Enfeeble']={t=2000,icon='Spell_Shadow_CurseOfMannoroth'},
  ['Engulfing Shadows']={t=1500,icon='Spell_Shadow_LifeDrain02'},
  ['Enhance Blunt Weapon']={t=3000,icon='Temp'},
  ['Enhance Blunt Weapon II']={t=3000,icon='Temp'},
  ['Enhance Blunt Weapon III']={t=3000,icon='Temp'},
  ['Enhance Blunt Weapon IV']={t=3000,icon='Temp'},
  ['Enhance Blunt Weapon V']={t=3000,icon='Temp'},
  ['Enlarge']={t=2000,icon='Spell_Nature_Strength'},
  ['Enlightenment']={t=2000,icon='Spell_Shadow_Fumble'},
  ['Enslave Demon']={t=3000,icon='Spell_Shadow_EnslaveDemon'},
  ['Entangling Roots']={t=1500,icon='Spell_Nature_StrangleVines'},
  ['Enveloping Winds']={t=2000,icon='Spell_Nature_Cyclone'},
  ['Escape Artist']={t=500,icon='Ability_Rogue_Trip'},
  ['Everlook Transporter']={t=10000,icon='Spell_Fire_SelfDestruct'},
  ['Evil Eye']={t=1500,icon='Spell_Shadow_Charm'},
  ['Evil God Counterspell']={t=300000,icon='Temp'},
  ['Exorcise Atiesh']={t=20000,icon='Temp'},
  ['Exorcise Spirits']={t=4000,icon='Temp'},
  ['Exploding Shot']={t=1000,icon='Spell_Fire_Fireball02'},
  ['Explosive Sheep']={t=2000,icon='Ability_Repair'},
  ['Explosive Shot']={t=1000,icon='Spell_Fire_Fireball02'},
  ['Extinguish']={t=2000,icon='Temp'},
  ['Eye Beam']={t=2000,icon='Spell_Nature_CallStorm'},
  ['Eye of Immol\'thar']={t=2000,icon='Spell_Shadow_AntiMagicShell'},
  ['Eye of Kilrogg']={t=5000,icon='Spell_Shadow_EvilEye'},
  ['Eye of Yesmur (PT)']={t=2000,icon='Temp'},
  ['Eyes of the Beast']={t=2000,icon='Ability_EyeOfTheOwl'},
  ['Ez-Thro Dynamite']={t=1000,icon='Spell_Fire_SelfDestruct'},
  ['Faerie Fire']={t=2000,icon='Spell_Nature_FaerieFire'},
  ['Fake Shot']={t=2000,icon='Ability_Marksmanship'},
  ['Fanatic Blade']={t=1000,icon='Spell_Fire_Immolation'},
  ['Far Sight']={t=2000,icon='Spell_Nature_FarSight'},
  ['Far Sight (PT)']={t=2000,icon='Temp'},
  ['Fear']={t=1500,icon='Spell_Shadow_Possession'},
  ['Fear (NYI)']={t=3000,icon='Spell_Shadow_Possession'},
  ['Feeblemind']={t=1000,icon='Spell_Shadow_MindSteal'},
  ['Feeblemind II']={t=1000,icon='Spell_Shadow_MindSteal'},
  ['Feeblemind III']={t=1000,icon='Spell_Shadow_MindSteal'},
  ['Fel Curse']={t=4000,icon='Temp'},
  ['Felstrom Resurrection']={t=3000,icon='Spell_Totem_WardOfDraining'},
  ['Feral Spirit']={t=3000,icon='Spell_Nature_SpiritWolf'},
  ['Feral Spirit II']={t=3000,icon='Spell_Nature_SpiritWolf'},
  ['Fevered Fatigue']={t=3000,icon='Spell_Nature_NullifyDisease'},
  ['Fevered Plague']={t=4500,icon='Spell_Nature_NullifyDisease'},
  ['Fiery Blaze']={t=3000,icon='Spell_Fire_SelfDestruct'},
  ['Fiery Burst']={t=1500,icon='Spell_Fire_FireBolt'},
  ['Fill Phial']={t=5000,icon='Temp'},
  ['Filling']={t=3000,icon='Temp'},
  ['Find Relic Fragment']={t=5000,icon='Temp'},
  ['Fire Cannon']={t=2000,icon='Temp'},
  ['Fire Elemental']={t=3000,icon='Ability_Temp'},
  ['Fire Shield']={t=1000,icon='Spell_Fire_Immolation'},
  ['Fire Shield II']={t=1000,icon='Spell_Fire_Immolation'},
  ['Fire Shield III']={t=1000,icon='Spell_Fire_Immolation'},
  ['Fire Shield IV']={t=1000,icon='Spell_Fire_Immolation'},
  ['Fire Storm']={t=2000,icon='Spell_Fire_SelfDestruct'},
  ['Fire Weakness']={t=5000,icon='INV_Misc_QirajiCrystal_02'},
  ['Fire-toasted Bun']={t=1000,icon='INV_Misc_Food_11'},
  ['Fireball']={t=3500,icon='Spell_Fire_FlameBolt'},
  ['Fireball Volley']={t=3000,icon='Spell_Fire_FlameBolt'},
  ['Firebolt']={t=2000,icon='Spell_Fire_FireBolt'},
  ['Firebolt II']={t=3000,icon='Spell_Fire_FireBolt02'},
  ['Firebolt III']={t=3000,icon='Spell_Fire_FireBolt02'},
  ['Firebolt IV']={t=3000,icon='Spell_Fire_FireBolt02'},
  ['Firework']={t=500,icon='Temp'},
  ['First Aid']={t=3000,icon='Spell_Holy_GreaterHeal'},
  ['Fist of Shahram']={t=1000,icon='Ability_Whirlwind'},
  ['Fix Ritual Bell (DND)']={t=3000,icon='Temp'},
  ['Fix Ritual Candle (DND)']={t=3000,icon='Temp'},
  ['Fix Ritual Node']={t=3000,icon='Temp'},
  ['Flame Blast']={t=7000,icon='Spell_Fire_SelfDestruct'},
  ['Flame Breath']={t=1700,icon='Spell_Fire_Fire'},
  ['Flame Buffet']={t=2200,icon='Spell_Fire_Fireball'},
  ['Flame Burst']={t=3000,icon='Spell_Fire_SelfDestruct'},
  ['Flame Cannon']={t=1500,icon='Spell_Fire_FlameBolt'},
  ['Flame Lash']={t=1000,icon='Spell_Fire_Fireball'},
  ['Flame Spike']={t=3000,icon='Spell_Fire_SelfDestruct'},
  ['Flame Spray']={t=1700,icon='Spell_Fire_Fire'},
  ['Flamecrack']={t=2500,icon='Spell_Fire_Fire'},
  ['Flames of Chaos']={t=1000,icon='Spell_Fire_WindsofWoe'},
  ['Flames of Retribution']={t=3000,icon='Temp'},
  ['Flames of Shahram']={t=1000,icon='Spell_Fire_SelfDestruct'},
  ['Flamespit']={t=3000,icon='Spell_Fire_FlameBolt'},
  ['Flamestrike']={t=3000,icon='Spell_Fire_SelfDestruct'},
  ['Flash Heal']={t=1500,icon='Spell_Holy_FlashHeal'},
  ['Flash of Light']={t=1500,icon='Spell_Holy_FlashHeal'},
  ['Flesh Eating Worm']={t=5000,icon='INV_Misc_Orb_03'},
  ['Fling Torch']={t=1000,icon='Spell_Fire_Flare'},
  ['Fluorescent Green Mechanostrider']={t=3000,icon='Ability_Mount_MechaStrider'},
  ['Focusing']={t=5000,icon='Temp'},
  ['Force Punch']={t=1000,icon='INV_Gauntlets_31'},
  ['Forge Verigan\'s Fist']={t=600,icon='Spell_Holy_RighteousFury'},
  ['Forgiveness']={t=1000,icon='Temp'},
  ['Forked Lightning']={t=2000,icon='Spell_Nature_ChainLightning'},
  ['Form of the Moonstalker (no invis)']={t=1000,icon='Ability_Hibernation'},
  ['Forsaken Skills']={t=2500,icon='Spell_Shadow_AntiShadow'},
  ['Freeze']={t=4000,icon='Spell_Frost_Glacier'},
  ['Freeze Rookery Egg']={t=500,icon='Temp'},
  ['Freeze Rookery Egg - Prototype']={t=500,icon='Temp'},
  ['Freeze Solid']={t=2500,icon='Spell_Frost_Glacier'},
  ['Frightalon']={t=1000,icon='Spell_Shadow_ShadowPact'},
  ['Frost Breath']={t=250,icon='Spell_Frost_FrostNova'},
  ['Frost Burn']={t=2000,icon='Spell_Frost_ChillingBlast'},
  ['Frost Oil']={t=3000,icon='Temp'},
  ['Frost Ram']={t=3000,icon='Ability_Mount_MountainRam'},
  ['Frost Resistance']={t=1000,icon='Spell_Frost_WizardMark'},
  ['Frost Weakness']={t=5000,icon='INV_Misc_QirajiCrystal_04'},
  ['Frostbolt']={t=3000,icon='Spell_Frost_FrostBolt02'},
  ['Frostbolt Volley']={t=2000,icon='Spell_Frost_FrostBolt02'},
  ['Frostmane Strength']={t=1000,icon='Spell_Nature_UndyingStrength'},
  ['Frostsaber']={t=3000,icon='Ability_Mount_WhiteTiger'},
  ['Frostwolf Howler']={t=3000,icon='Ability_Mount_WhiteDireWolf'},
  ['Full Heal']={t=1000,icon='Temp'},
  ['Fumble']={t=1000,icon='Spell_Shadow_Fumble'},
  ['Fumble II']={t=1000,icon='Spell_Shadow_Fumble'},
  ['Fumble III']={t=1000,icon='Spell_Shadow_Fumble'},
  ['Furbolg Form']={t=2000,icon='INV_Misc_MonsterClaw_04'},
  ['Gargoyle Strike']={t=1500,icon='Spell_Shadow_ShadowBolt'},
  ['Gas Bomb']={t=1000,icon='INV_Misc_Ammo_Bullet_01'},
  ['Gem of the Serpent']={t=5000,icon='Temp'},
  ['Ghost Wolf']={t=3000,icon='Spell_Nature_SpiritWolf'},
  ['Gift of the Xavian']={t=5000,icon='Spell_Holy_FlashHeal'},
  ['Glacial Roar']={t=1000,icon='Spell_Frost_FrostNova'},
  ['Glare']={t=500,icon='Temp'},
  ['Gnome Camera Connection']={t=3000,icon='Temp'},
  ['Gnomish Transporter']={t=10000,icon='Temp'},
  ['Goblin Camera Connection']={t=3000,icon='Temp'},
  ['Goblin Land Mine']={t=1000,icon='Spell_Shadow_Metamorphosis'},
  ['Goblin Mortar']={t=1000,icon='Spell_Fire_SelfDestruct'},
  ['Golden Sabercat']={t=3000,icon='Ability_Mount_JungleTiger'},
  ['Golden Skeleton Key']={t=5000,icon='Temp'},
  ['Goldshire Portal']={t=5000,icon='Temp'},
  ['Gong']={t=500,icon='Temp'},
  ['Gong Zul\'Farrak Gong']={t=500,icon='Temp'},
  ['Gordok Green Grog']={t=1000,icon='INV_Drink_03'},
  ['Grasping Vines']={t=1000,icon='Spell_Nature_Earthquake'},
  ['Gray Kodo']={t=3000,icon='Ability_Mount_Kodo_01'},
  ['Gray Ram']={t=3000,icon='Ability_Mount_MountainRam'},
  ['Gray Wolf']={t=3000,icon='Ability_Mount_WhiteDireWolf'},
  ['Great Brown Kodo']={t=3000,icon='Ability_Mount_Kodo_03'},
  ['Great Gray Kodo']={t=3000,icon='Ability_Mount_Kodo_01'},
  ['Great Heal']={t=4000,icon='Spell_Holy_Heal'},
  ['Great White Kodo']={t=3000,icon='Ability_Mount_Kodo_01'},
  ['Greater Dispel']={t=4000,icon='Spell_Arcane_StarFire'},
  ['Greater Heal']={t=3000,icon='Spell_Holy_GreaterHeal'},
  ['Greater Invisibility']={t=3000,icon='Spell_Nature_Invisibilty'},
  ['Green Dragon Transform DND']={t=1000,icon='Temp'},
  ['Green Kodo']={t=3000,icon='Ability_Mount_Kodo_02'},
  ['Green Mechanostrider']={t=3000,icon='Ability_Mount_MechaStrider'},
  ['Green Skeletal Warhorse']={t=3000,icon='Ability_Mount_Undeadhorse'},
  ['Grom\'s Tribute']={t=2000,icon='Temp'},
  ['Guile of the Raptor']={t=3000,icon='INV_Misc_MonsterClaw_02'},
  ['Gust of Wind']={t=2000,icon='Spell_Nature_EarthBind'},
  ['Hammer of Wrath']={t=1000,icon='Ability_ThunderClap'},
  ['Hand of Iruxos']={t=5000,icon='Temp'},
  ['Harvest Silithid Egg']={t=5000,icon='Temp'},
  ['Harvest Swarm']={t=3000,icon='Spell_Holy_Dizzy'},
  ['Haunting Phantoms']={t=2000,icon='Spell_Shadow_BlackPlague'},
  ['Haunting Spirits']={t=2000,icon='Spell_Shadow_BlackPlague'},
  ['Heal']={t=3000,icon='Spell_Holy_Heal02'},
  ['Heal Visual (DND)']={t=3500,icon='Spell_Holy_Heal'},
  ['Heal Vylestem Vine']={t=500,icon='Temp'},
  ['Healing Circle']={t=3000,icon='Spell_Holy_BlessingOfProtection'},
  ['Healing Tongue']={t=1000,icon='Spell_Holy_Heal'},
  ['Healing Tongue II']={t=1000,icon='Spell_Holy_Heal'},
  ['Healing Touch']={t=3500,icon='Spell_Nature_HealingTouch'},
  ['Healing Ward']={t=2000,icon='Spell_Holy_LayOnHands'},
  ['Healing Wave']={t=3000,icon='Spell_Nature_MagicImmunity'},
  ['Healing Wave of Antu\'sul']={t=1000,icon='Spell_Holy_Heal02'},
  ['Heart of Hakkar - Molthor chucks the heart']={t=2000,icon='Temp'},
  ['Hearthstone']={t=10000,icon='INV_Misc_Rune_01'},
  ['Heavy Dynamite']={t=1000,icon='Spell_Fire_SelfDestruct'},
  ['Heightened Senses']={t=1000,icon='Temp'},
  ['Hellfire']={t=3000,icon='Spell_Fire_SelfDestruct'},
  ['Hellfire II']={t=3000,icon='Spell_Fire_SelfDestruct'},
  ['Hellfire III']={t=3000,icon='Spell_Fire_SelfDestruct'},
  ['Herb Gathering']={t=5000,icon='Spell_Nature_NatureTouchGrow'},
  ['Hex']={t=2000,icon='Spell_Nature_Polymorph'},
  ['Hex of Ravenclaw']={t=2000,icon='Spell_Shadow_Charm'},
  ['Hi-Explosive Bomb']={t=1000,icon='Spell_Fire_SelfDestruct'},
  ['Hibernate']={t=1500,icon='Spell_Nature_Sleep'},
  ['Holy Fire']={t=3500,icon='Spell_Holy_SearingLight'},
  ['Holy Light']={t=2500,icon='Spell_Holy_HolyBolt'},
  ['Holy Smite']={t=2500,icon='Spell_Holy_HolySmite'},
  ['Holy Wrath']={t=2000,icon='Spell_Holy_Excorcism'},
  ['Honor Points +138']={t=1000,icon='Temp'},
  ['Honor Points +228']={t=1000,icon='Temp'},
  ['Honor Points +2388']={t=1000,icon='INV_BannerPVP_02'},
  ['Honor Points +378']={t=1000,icon='Temp'},
  ['Honor Points +398']={t=1000,icon='Temp'},
  ['Honor Points +50']={t=1000,icon='Temp'},
  ['Honor Points +82']={t=1000,icon='Temp'},
  ['Hooked Net']={t=500,icon='Ability_Ensnare'},
  ['Howl of Terror']={t=2000,icon='Spell_Shadow_DeathScream'},
  ['Howling Rage']={t=5000,icon='Ability_BullRush'},
  ['Ice Tomb']={t=1500,icon='Spell_Frost_Glacier'},
  ['Icebolt']={t=2000,icon='Spell_Frost_FrostBolt02'},
  ['Icicle']={t=1500,icon='Spell_Frost_FrostBolt02'},
  ['Icy Blue Mechanostrider']={t=3000,icon='Ability_Mount_MechaStrider'},
  ['Identify Brood']={t=10000,icon='Spell_Lightning_LightningBolt01'},
  ['Ignite Flesh']={t=2000,icon='Spell_Fire_Fire'},
  ['Igniting Kroshius']={t=3000,icon='Temp'},
  ['Ilkrud\'s Guardians']={t=5000,icon='Spell_Shadow_SummonVoidWalker'},
  ['Imbue Chest - Absorb']={t=5000,icon='Spell_Holy_GreaterHeal'},
  ['Imbue Chest - Lesser Absorb']={t=5000,icon='Spell_Holy_GreaterHeal'},
  ['Imbue Chest - Lesser Spirit']={t=5000,icon='Spell_Holy_GreaterHeal'},
  ['Imbue Chest - Minor Spirit']={t=5000,icon='Spell_Holy_GreaterHeal'},
  ['Imbue Cloak - Lesser Protection']={t=5000,icon='Spell_Holy_GreaterHeal'},
  ['Imbue Cloak - Minor Resistance']={t=5000,icon='Spell_Holy_GreaterHeal'},
  ['Imbue Weapon - Beastslayer']={t=5000,icon='Spell_Holy_GreaterHeal'},
  ['Immolate']={t=2000,icon='Spell_Fire_Immolation'},
  ['Implosion']={t=10000,icon='Spell_Fire_SelfDestruct'},
  ['Incendia Powder']={t=5000,icon='Temp'},
  ['Incinerate']={t=2000,icon='Spell_Shadow_ChillTouch'},
  ['Incineration']={t=5000,icon='Temp'},
  ['Increase Reputation']={t=1000,icon='Temp'},
  ['Inducing Vision']={t=30000,icon='Spell_Shadow_LifeDrain'},
  ['Inferno']={t=2000,icon='Spell_Shadow_SummonInfernal'},
  ['Inferno Shell']={t=3000,icon='Spell_Fire_SelfDestruct'},
  ['Ink Spray']={t=1000,icon='Spell_Nature_Sleep'},
  ['Instant Poison']={t=3000,icon='Ability_Poisons'},
  ['Instant Poison II']={t=3000,icon='Ability_Poisons'},
  ['Instant Poison III']={t=3000,icon='Ability_Poisons'},
  ['Instant Poison IV']={t=3000,icon='Ability_Poisons'},
  ['Instant Poison V']={t=3000,icon='Ability_Poisons'},
  ['Instant Poison VI']={t=3000,icon='Ability_Poisons'},
  ['Instant Toxin']={t=3000,icon='INV_Potion_19'},
  ['Instill Lord Valthalak\'s Spirit DND']={t=5000,icon='Temp'},
  ['Intense Pain']={t=1000,icon='Spell_Shadow_ShadowWordPain'},
  ['Intimidating Growl']={t=1000,icon='Ability_Racial_Cannibalize'},
  ['Invis Placing Bear Trap']={t=2000,icon='Temp'},
  ['Invisibility']={t=3000,icon='Spell_Nature_Invisibilty'},
  ['Iron Grenade']={t=1000,icon='Spell_Fire_SelfDestruct'},
  ['Ivory Raptor']={t=3000,icon='Ability_Mount_Raptor'},
  ['Ivus Teleport Visual DND']={t=1000,icon='Temp'},
  ['J\'eevee summons object']={t=2000,icon='Temp'},
  ['Jarkal\'s Translation']={t=4500,icon='Spell_Holy_Restoration'},
  ['Julie\'s Blessing']={t=2000,icon='Spell_Holy_Renew'},
  ['Jumping Lightning']={t=3000,icon='Spell_Nature_Lightning'},
  ['Kadrak\'s Flag']={t=2000,icon='INV_Banner_03'},
  ['Kalaran Conjures Torch']={t=1000,icon='Temp'},
  ['Kev']={t=3000,icon='Spell_Fire_FireBolt'},
  ['Khadgar\'s Unlocking']={t=10000,icon='INV_Misc_Key_14'},
  ['King of the Gordok']={t=1000,icon='INV_Crown_02'},
  ['Kodo Kombobulator']={t=5000,icon='Trade_Fishing'},
  ['Kreeg\'s Stout Beatdown']={t=1000,icon='INV_Drink_05'},
  ['Large Copper Bomb']={t=1000,icon='Spell_Fire_SelfDestruct'},
  ['Large Seaforium Charge']={t=5000,icon='Temp'},
  ['Large Timber Wolf']={t=3000,icon='Ability_Mount_BlackDireWolf'},
  ['Leopard']={t=3000,icon='Ability_Mount_JungleTiger'},
  ['Leper Cure!']={t=2000,icon='Spell_Holy_FlashHeal'},
  ['Lesser Heal']={t=2500,icon='Spell_Holy_LesserHeal'},
  ['Lesser Healing Wave']={t=1500,icon='Spell_Nature_HealingWaveLesser'},
  ['Lesser Invisibility']={t=3000,icon='Spell_Magic_LesserInvisibilty'},
  ['Lesser Mana Oil']={t=3000,icon='Temp'},
  ['Lesser Wizard Oil']={t=3000,icon='Temp'},
  ['Lethal Toxin']={t=3000,icon='Spell_Nature_CorrosiveBreath'},
  ['Life Harvest']={t=1000,icon='Spell_Shadow_Requiem'},
  ['Life Steal']={t=1500,icon='Spell_Shadow_LifeDrain02'},
  ['Lift Seal']={t=2000,icon='Temp'},
  ['Lightning Blast']={t=3200,icon='Spell_Nature_Lightning'},
  ['Lightning Bolt']={t=3000,icon='Spell_Nature_Lightning'},
  ['Lightning Breath']={t=2000,icon='Spell_Nature_Lightning'},
  ['Lightning Cloud']={t=3000,icon='Spell_Nature_CallStorm'},
  ['Lightning Totem']={t=500,icon='Spell_Nature_Lightning'},
  ['Lightwell']={t=1500,icon='Spell_Holy_SummonLightwell'},
  ['Linken\'s Boomerang']={t=500,icon='INV_Weapon_ShortBlade_10'},
  ['Lizard Bolt']={t=2000,icon='Spell_Nature_Lightning'},
  ['Locust Swarm']={t=3000,icon='Spell_Nature_InsectSwarm'},
  ['Longshot II']={t=4000,icon='Ability_Marksmanship'},
  ['Longshot III']={t=4000,icon='Ability_Marksmanship'},
  ['Longsight']={t=1000,icon='Ability_TownWatch'},
  ['Lunar Invititation']={t=5000,icon='Spell_Arcane_TeleportMoonglade'},
  ['Machine Gun']={t=500,icon='Ability_Marksmanship'},
  ['Magatha Incendia Powder']={t=1300,icon='Temp'},
  ['Mage Sight']={t=3000,icon='Temp'},
  ['Magma Blast']={t=1000,icon='Spell_Fire_FlameShock'},
  ['Majordomo Teleport Visual']={t=1000,icon='Spell_Arcane_Blink'},
  ['Mana Burn']={t=3000,icon='Spell_Shadow_ManaBurn'},
  ['Manastorm']={t=2000,icon='Spell_Frost_IceStorm'},
  ['Manifest Spirit']={t=5000,icon='Spell_Totem_WardOfDraining'},
  ['Manifestation Cleansing']={t=4000,icon='Temp'},
  ['Mark of Flames']={t=1000,icon='Spell_Fire_Fireball'},
  ['Marksman Hit']={t=2000,icon='Ability_Marksmanship'},
  ['Mass Dispell']={t=1000,icon='Spell_Shadow_Teleport'},
  ['Mass Healing']={t=1000,icon='Spell_Holy_GreaterHeal'},
  ['Massive Geyser']={t=1500,icon='Spell_Frost_SummonWaterElemental'},
  ['Massive Mortar']={t=3000,icon='Temp'},
  ['Maypole']={t=10000,icon='Spell_Shadow_Twilight'},
  ['Mebok Smart Drink']={t=3000,icon='Temp'},
  ['Mechanical Patch Kit']={t=2000,icon='INV_Gizmo_03'},
  ['Mechanical Squirrel']={t=1000,icon='Spell_Shadow_Metamorphosis'},
  ['Megavolt']={t=2000,icon='Spell_Nature_ChainLightning'},
  ['Melodious Rapture']={t=1000,icon='Temp'},
  ['Melt Ore']={t=1500,icon='Spell_Fire_SelfDestruct'},
  ['Merging Oozes']={t=500,icon='INV_Potion_12'},
  ['Merithra\'s Wake']={t=2000,icon='Temp'},
  ['Miblon\'s Bait']={t=2000,icon='INV_Misc_Food_50'},
  ['Midsummer Sausage']={t=1000,icon='INV_Misc_Food_53'},
  ['Might of Ragnaros']={t=500,icon='Spell_Fire_SelfDestruct'},
  ['Might of Shahram']={t=1000,icon='Spell_Nature_WispSplode'},
  ['Mind Blast']={t=1500,icon='Spell_Shadow_UnholyFrenzy'},
  ['Mind Control']={t=3000,icon='Spell_Shadow_ShadowWordDominate'},
  ['Mind Rot']={t=2000,icon='Spell_Shadow_MindRot'},
  ['Mind Tremor']={t=2000,icon='Spell_Nature_Earthquake'},
  ['Mind-numbing Poison']={t=3000,icon='Spell_Nature_NullifyDisease'},
  ['Mind-numbing Poison II']={t=3000,icon='Spell_Nature_NullifyDisease'},
  ['Mind-numbing Poison III']={t=3000,icon='Spell_Nature_NullifyDisease'},
  ['Minigun']={t=100,icon='INV_Musket_04'},
  ['Mining']={t=3200,icon='Trade_Mining'},
  ['Minion of Morganth']={t=2500,icon='Spell_Totem_WardOfDraining'},
  ['Minions of Malathrom']={t=1000,icon='Spell_Shadow_CorpseExplode'},
  ['Minor Mana Oil']={t=3000,icon='Temp'},
  ['Minor Wizard Oil']={t=3000,icon='Temp'},
  ['Miring Mud']={t=1000,icon='Spell_Nature_StrangleVines'},
  ['Mirkfallon Fungus']={t=3000,icon='Spell_Holy_HarmUndeadAura'},
  ['Mistletoe']={t=1000,icon='INV_Misc_Branch_01'},
  ['Mithril Frag Bomb']={t=1000,icon='Spell_Fire_SelfDestruct'},
  ['Molten Blast']={t=2000,icon='Spell_Fire_Fire'},
  ['Molten Metal']={t=2000,icon='Spell_Fire_Fireball'},
  ['Molten Rain']={t=2000,icon='Temp'},
  ['Mor\'rogal Enchant']={t=1300,icon='Temp'},
  ['Mottled Red Raptor']={t=3000,icon='Ability_Mount_Raptor'},
  ['Nagmara\'s Love Potion']={t=1000,icon='Temp'},
  ['Narain!']={t=3000,icon='INV_Misc_Head_Gnome_01'},
  ['Naralex\'s Nightmare']={t=2000,icon='Spell_Nature_Sleep'},
  ['Nature Weakness']={t=5000,icon='INV_Misc_QirajiCrystal_03'},
  ['Nefarius Attack 001']={t=1000,icon='Temp'},
  ['Nether Gem']={t=3000,icon='Temp'},
  ['New Magic Missile (Test)']={t=2000,icon='Temp'},
  ['Nightcrawlers']={t=5000,icon='Trade_Fishing'},
  ['Nightsaber']={t=3000,icon='Ability_Mount_BlackPanther'},
  ['Nostalgia']={t=4000,icon='Spell_Shadow_LifeDrain'},
  ['Nullify Mana']={t=2000,icon='Spell_Shadow_DarkRitual'},
  ['Numbing Pain']={t=1500,icon='Spell_Nature_CorrosiveBreath'},
  ['Obsidian Raptor']={t=3000,icon='Ability_Mount_Raptor'},
  ['Opening']={t=5000,icon='Temp'},
  ['Opening - No Text']={t=5000,icon='Temp'},
  ['Opening Bar Door']={t=5000,icon='Temp'},
  ['Opening Benedict\'s Chest']={t=5000,icon='Temp'},
  ['Opening Booty Chest']={t=5000,icon='Temp'},
  ['Opening Cage']={t=5000,icon='Temp'},
  ['Opening Chest']={t=5000,icon='Temp'},
  ['Opening Dark Coffer']={t=5000,icon='Temp'},
  ['Opening Greater Scarab Coffer']={t=5000,icon='Temp'},
  ['Opening Relic Coffer']={t=5000,icon='Temp'},
  ['Opening Safe']={t=5000,icon='Temp'},
  ['Opening Scarab Coffer']={t=5000,icon='Temp'},
  ['Opening Secret Safe']={t=5000,icon='Temp'},
  ['Opening Secure Safe']={t=5000,icon='Temp'},
  ['Opening Stratholme Postbox']={t=5000,icon='Temp'},
  ['Opening Strongbox']={t=5000,icon='Temp'},
  ['Opening Termite Barrel']={t=5000,icon='Temp'},
  ['Ouro Submerge Visual']={t=1500,icon='Spell_Fire_Volcano'},
  ['Owl Form']={t=5000,icon='Spell_Nature_RavenForm'},
  ['Ozzie Explodes']={t=2000,icon='Temp'},
  ['Palamino Stallion']={t=3000,icon='Ability_Mount_RidingHorse'},
  ['Palomino Stallion']={t=3000,icon='Ability_Mount_RidingHorse'},
  ['Panther']={t=3000,icon='Ability_Mount_BlackPanther'},
  ['Panther Cage Key']={t=5000,icon='Temp'},
  ['Parasite']={t=2000,icon='Ability_Poisons'},
  ['Party Fever']={t=1000,icon='Spell_Shadow_DarkSummoning'},
  ['Peasant Disguise']={t=3000,icon='Ability_Rogue_Disguise'},
  ['Peon Disguise']={t=3000,icon='Ability_Rogue_Disguise'},
  ['Perm. Illusion Bishop Tyriona']={t=3000,icon='Ability_Rogue_Disguise'},
  ['Perm. Illusion Tyrion']={t=3000,icon='Ability_Rogue_Disguise'},
  ['Pick Lock']={t=5000,icon='Spell_Nature_MoonKey'},
  ['Pickpocket (PT)']={t=5000,icon='Temp'},
  ['Piercing Shadow']={t=2000,icon='Spell_Shadow_ChillTouch'},
  ['Pillar Delving']={t=5000,icon='Temp'},
  ['Pinto Horse']={t=3000,icon='Ability_Mount_RidingHorse'},
  ['Place Arcanite Buoy']={t=2000,icon='Temp'},
  ['Place Ghost Magnet']={t=1500,icon='Temp'},
  ['Place Lion Carcass']={t=2500,icon='INV_Misc_Bowl_01'},
  ['Place Scryer']={t=3000,icon='Temp'},
  ['Place Threshadon Carcass']={t=2500,icon='INV_Misc_Bowl_01'},
  ['Place Toxic Fogger']={t=3000,icon='INV_Cask_01'},
  ['Place Unfired Blade']={t=1000,icon='Temp'},
  ['Place Unforged Seal']={t=500,icon='Temp'},
  ['Placing Beacon Torch']={t=2300,icon='Temp'},
  ['Placing Bear Trap']={t=2000,icon='Temp'},
  ['Placing Pendant']={t=5000,icon='Temp'},
  ['Placing Smokey\'s Explosives']={t=3000,icon='Temp'},
  ['Plague Cloud']={t=2000,icon='Spell_Shadow_CallofBone'},
  ['Plague Mind']={t=4000,icon='Spell_Shadow_CallofBone'},
  ['Plant Gor\'tesh Head']={t=5000,icon='Temp'},
  ['Plant Magic Beans']={t=2000,icon='INV_Misc_Food_Wheat_02'},
  ['Plant Seeds']={t=5000,icon='Temp'},
  ['Planting Banner']={t=2300,icon='Temp'},
  ['Planting Guse\'s Beacon']={t=5000,icon='Temp'},
  ['Planting Ichman\'s Beacon']={t=5000,icon='Temp'},
  ['Planting Jeztor\'s Beacon']={t=5000,icon='Temp'},
  ['Planting Mulverick\'s Beacon']={t=5000,icon='Temp'},
  ['Planting Ryson\'s Beacon']={t=5000,icon='Temp'},
  ['Planting Slidore\'s Beacon']={t=5000,icon='Temp'},
  ['Planting Vipore\'s Beacon']={t=5000,icon='Temp'},
  ['Pointy Spike']={t=500,icon='Ability_ImpalingBolt'},
  ['Poison Bolt']={t=2500,icon='Spell_Nature_CorrosiveBreath'},
  ['Poison Cloud']={t=1000,icon='Spell_Nature_Regenerate'},
  ['Poison Stinger']={t=2000,icon='INV_Misc_MonsterTail_03'},
  ['Poisoned Harpoon']={t=2000,icon='Ability_Poisons'},
  ['Poisoned Shot']={t=2000,icon='Ability_Poisons'},
  ['Poisonous Spit']={t=2000,icon='Spell_Nature_CorrosiveBreath'},
  ['Polarity Shift']={t=3000,icon='Spell_Nature_Lightning'},
  ['Polymorph']={t=1500,icon='Spell_Nature_Polymorph'},
  ['Polymorph: Cow']={t=1500,icon='Spell_Nature_Polymorph_Cow'},
  ['Polymorph: Pig']={t=1500,icon='Spell_Magic_PolymorphPig'},
  ['Polymorph: Turtle']={t=1500,icon='Ability_Hunter_Pet_Turtle'},
  ['Portal: Darnassus']={t=10000,icon='Spell_Arcane_PortalDarnassus'},
  ['Portal: Ironforge']={t=10000,icon='Spell_Arcane_PortalIronForge'},
  ['Portal: Karazhan']={t=10000,icon='Spell_Arcane_PortalUnderCity'},
  ['Portal: Orgrimmar']={t=10000,icon='Spell_Arcane_PortalOrgrimmar'},
  ['Portal: Stormwind']={t=10000,icon='Spell_Arcane_PortalStormWind'},
  ['Portal: Thunder Bluff']={t=10000,icon='Spell_Arcane_PortalThunderBluff'},
  ['Portal: Undercity']={t=10000,icon='Spell_Arcane_PortalUnderCity'},
  ['Potion Toss']={t=2000,icon='Spell_Misc_Drink'},
  ['Powerful Seaforium Charge']={t=5000,icon='Temp'},
  ['Powerful Smelling Salts']={t=2000,icon='INV_Misc_Ammo_Gunpowder_01'},
  ['Prayer of Elune']={t=1000,icon='Spell_Holy_Resurrection'},
  ['Prayer of Healing']={t=3000,icon='Spell_Holy_PrayerOfHealing02'},
  ['Presence of Death']={t=1000,icon='Spell_Shadow_ShadeTrueSight'},
  ['Primal Leopard']={t=3000,icon='Ability_Mount_JungleTiger'},
  ['Princess Summons Portal']={t=10000,icon='Spell_Arcane_PortalIronForge'},
  ['Proudmoore\'s Defense']={t=3000,icon='Spell_Holy_BlessingOfProtection'},
  ['Psychometry']={t=5000,icon='Spell_Holy_Restoration'},
  ['Purify and Place Food']={t=5000,icon='INV_Misc_Bowl_01'},
  ['Purple Hands']={t=4000,icon='Spell_Shadow_SiphonMana'},
  ['Purple Mechanostrider']={t=3000,icon='Ability_Mount_MechaStrider'},
  ['Purple Skeletal Warhorse']={t=3000,icon='Ability_Mount_Undeadhorse'},
  ['Pyroblast']={t=6000,icon='Spell_Fire_Fireball02'},
  ['Quest - Sergra Darkthorn Spell']={t=3000,icon='Temp'},
  ['Quest - Summon Treant']={t=3000,icon='Spell_Nature_NatureTouchGrow'},
  ['Quest - Teleport Spawn-out']={t=1000,icon='Temp'},
  ['Quest - Troll Hero Summon Visual']={t=30000,icon='Temp'},
  ['Quick Bloodlust']={t=2000,icon='Spell_Nature_BloodLust'},
  ['Quick Flame Ward']={t=1500,icon='Spell_Fire_SealOfFire'},
  ['Quick Frost Ward']={t=1500,icon='Spell_Fire_SealOfFire'},
  ['Radiation Bolt']={t=3000,icon='Spell_Shadow_CorpseExplode'},
  ['Rage of Thule']={t=1500,icon='Spell_Shadow_UnholyFrenzy'},
  ['Ragnaros Emerge']={t=2900,icon='Spell_Fire_LavaSpawn'},
  ['Rain of Fire']={t=3000,icon='Spell_Shadow_RainOfFire'},
  ['Raise Dead']={t=1000,icon='Spell_Shadow_RaiseDead'},
  ['Raise Undead Scarab']={t=1000,icon='Spell_Shadow_Contagion'},
  ['Raptor Feather']={t=5000,icon='Temp'},
  ['Razor Mane']={t=1000,icon='Spell_Nature_Thorns'},
  ['Rebirth']={t=2000,icon='Spell_Nature_Reincarnation'},
  ['Recall']={t=10000,icon='Temp'},
  ['Recite Words of Celebras']={t=3000,icon='Temp'},
  ['Reconstruction']={t=3000,icon='Temp'},
  ['Red & Blue Mechanostrider']={t=3000,icon='Ability_Mount_MechaStrider'},
  ['Red Dragon Transform DND']={t=1000,icon='Temp'},
  ['Red Mechanostrider']={t=3000,icon='Ability_Mount_MechaStrider'},
  ['Red Skeletal Horse']={t=3000,icon='Ability_Mount_Undeadhorse'},
  ['Red Skeletal Warhorse']={t=3000,icon='Ability_Mount_Undeadhorse'},
  ['Red Wolf']={t=3000,icon='Ability_Mount_BlackDireWolf'},
  ['Redemption']={t=10000,icon='Spell_Holy_Resurrection'},
  ['Regrowth']={t=2000,icon='Spell_Nature_ResistNature'},
  ['Reindeer Dust Effect']={t=8000,icon='Temp'},
  ['Release Imp']={t=2000,icon='Temp'},
  ['Release J\'eevee']={t=2000,icon='Temp'},
  ['Release Rageclaw']={t=10000,icon='Temp'},
  ['Release Umi\'s Yeti']={t=2000,icon='Temp'},
  ['Release Winna\'s Kitten']={t=1000,icon='Ability_Seal'},
  ['Release the Hounds']={t=2000,icon='Temp'},
  ['Releasing Corrupt Ooze']={t=5000,icon='INV_Potion_19'},
  ['Remote Detonate']={t=1000,icon='INV_Misc_StoneTablet_04'},
  ['Remove Insignia']={t=1000,icon='Temp'},
  ['Renew']={t=2000,icon='Spell_Holy_Renew'},
  ['Replenish Spirit']={t=3000,icon='Spell_Nature_MoonGlow'},
  ['Replenish Spirit II']={t=3000,icon='Spell_Nature_MoonGlow'},
  ['Reputation - Ahn\'Qiraj Temple Boss']={t=1000,icon='Temp'},
  ['Reputation - Booty Bay +500']={t=1000,icon='Temp'},
  ['Reputation - Everlook +500']={t=1000,icon='Temp'},
  ['Reputation - Gadgetzan +500']={t=1000,icon='Temp'},
  ['Reputation - Ratchet +500']={t=1000,icon='Temp'},
  ['Resupply']={t=2000,icon='Spell_Misc_Drink'},
  ['Resurrection']={t=10000,icon='Spell_Holy_Resurrection'},
  ['Retching Plague']={t=2000,icon='Spell_Shadow_CallofBone'},
  ['Revive Dig Rat']={t=3000,icon='Spell_Holy_Resurrection'},
  ['Revive Pet']={t=10000,icon='Ability_Hunter_BeastSoothe'},
  ['Revive Ringo']={t=2500,icon='Temp'},
  ['Riding Kodo']={t=3000,icon='INV_Misc_Head_Tauren_02'},
  ['Riding Turtle']={t=3000,icon='Ability_Hunter_Pet_Turtle'},
  ['Rift Beacon']={t=2000,icon='Spell_Nature_AbolishMagic'},
  ['Righteous Flame On']={t=4000,icon='Spell_Holy_InnerFire'},
  ['Rimblat Grows Flower DND']={t=2000,icon='Temp'},
  ['Ritual of Doom']={t=10000,icon='Spell_Shadow_AntiMagicShell'},
  ['Ritual of Doom Effect']={t=10000,icon='Spell_Arcane_PortalDarnassus'},
  ['Ritual of Summoning']={t=5000,icon='Spell_Shadow_Twilight'},
  ['Ritual of Summoning Effect']={t=5000,icon='Temp'},
  ['Rocket Blast']={t=3000,icon='Temp'},
  ['Rookery Whelp Spawn-in Spell']={t=500,icon='Temp'},
  ['Rotate Trigger']={t=3000,icon='Temp'},
  ['Rough Copper Bomb']={t=1000,icon='Spell_Fire_SelfDestruct'},
  ['Rough Dynamite']={t=1000,icon='Spell_Fire_SelfDestruct'},
  ['Rumsey Rum']={t=1000,icon='INV_Drink_03'},
  ['Rumsey Rum Black Label']={t=1000,icon='INV_Drink_04'},
  ['Rumsey Rum Dark']={t=1000,icon='INV_Drink_04'},
  ['Rumsey Rum Light']={t=1000,icon='INV_Drink_08'},
  ['Rune of Opening']={t=5000,icon='Temp'},
  ['Ruul Snowhoof Shapechange (DND)']={t=1000,icon='Temp'},
  ['Ryson\'s All Seeing Eye']={t=1000,icon='Ability_Ambush'},
  ['Ryson\'s Eye in the Sky']={t=1000,icon='Ability_Hunter_EagleEye'},
  ['Sacrifice']={t=1000,icon='Spell_Holy_DivineIntervention'},
  ['Sacrifice Spinneret']={t=10000,icon='Temp'},
  ['Sand Blast']={t=2000,icon='Spell_Nature_Cyclone'},
  ['Sand Breath']={t=2000,icon='Spell_Fire_WindsofWoe'},
  ['Sapper Explode']={t=5000,icon='Spell_Fire_SelfDestruct'},
  ['Sapphiron DND']={t=20000,icon='Temp'},
  ['Sarilus\'s Elementals']={t=3000,icon='Spell_Shadow_RaiseDead'},
  ['Scare Beast']={t=1500,icon='Ability_Druid_Cower'},
  ['Scarlet Resurrection']={t=2000,icon='Spell_Holy_Resurrection'},
  ['Scarshield Portal']={t=1500,icon='Spell_Arcane_TeleportOrgrimmar'},
  ['Scorch']={t=1500,icon='Spell_Fire_SoulBurn'},
  ['Searing Flames']={t=2000,icon='Spell_Fire_Immolation'},
  ['Searing Pain']={t=1500,icon='Spell_Fire_SoulBurn'},
  ['Seduction']={t=1500,icon='Spell_Shadow_MindSteal'},
  ['Seeping Willow']={t=1000,icon='Spell_Nature_CorrosiveBreath'},
  ['Segra Darkthorn Effect']={t=3000,icon='Temp'},
  ['Self Destruct']={t=7000,icon='Spell_Fire_SelfDestruct'},
  ['Self Detonation']={t=7000,icon='Spell_Fire_SelfDestruct'},
  ['Self Resurrection']={t=5000,icon='Temp'},
  ['Serpentine Cleansing']={t=30000,icon='Spell_Shadow_LifeDrain'},
  ['Set NG-5 Charge (Blue)']={t=5000,icon='INV_Misc_Bomb_05'},
  ['Set NG-5 Charge (Red)']={t=5000,icon='INV_Misc_Bomb_05'},
  ['Shackle Undead']={t=1500,icon='Spell_Nature_Slow'},
  ['Shadow Bolt']={t=3000,icon='Spell_Shadow_ShadowBolt'},
  ['Shadow Bolt Misfire']={t=2000,icon='Spell_Shadow_ShadowBolt'},
  ['Shadow Bolt Volley']={t=3000,icon='Spell_Shadow_ShadowBolt'},
  ['Shadow Flame']={t=2000,icon='Spell_Fire_Incinerate'},
  ['Shadow Nova II']={t=3000,icon='Spell_Shadow_ShadeTrueSight'},
  ['Shadow Oil']={t=3000,icon='Temp'},
  ['Shadow Port']={t=250,icon='Spell_Shadow_AntiShadow'},
  ['Shadow Portal']={t=1000,icon='Spell_Shadow_SealOfKings'},
  ['Shadow Resistance']={t=1000,icon='Spell_Frost_WizardMark'},
  ['Shadow Shell']={t=1000,icon='Spell_Shadow_AntiShadow'},
  ['Shadow Weakness']={t=5000,icon='INV_Misc_QirajiCrystal_05'},
  ['Shadowblink']={t=500,icon='Spell_Shadow_DetectLesserInvisibility'},
  ['Shared Bonds']={t=1500,icon='Spell_Shadow_UnsummonBuilding'},
  ['Sharpen Blade']={t=3000,icon='Temp'},
  ['Sharpen Blade II']={t=3000,icon='Temp'},
  ['Sharpen Blade III']={t=3000,icon='Temp'},
  ['Sharpen Blade IV']={t=3000,icon='Temp'},
  ['Sharpen Blade V']={t=3000,icon='Temp'},
  ['Sharpen Weapon - Critical']={t=3000,icon='Temp'},
  ['Shay\'s Bell']={t=4500,icon='Temp'},
  ['Shield of Reflection']={t=1000,icon='Spell_Shadow_Teleport'},
  ['Shiny Bauble']={t=5000,icon='INV_Misc_Orb_03'},
  ['Shock']={t=1000,icon='Temp'},
  ['Shockwave']={t=2000,icon='Ability_Whirlwind'},
  ['Shoot Bow']={t=1000,icon='Ability_Marksmanship'},
  ['Shoot Crossbow']={t=1000,icon='Ability_Marksmanship'},
  ['Shoot Gun']={t=1000,icon='Ability_Marksmanship'},
  ['Shoot Missile']={t=3000,icon='INV_Ammo_Bullet_03'},
  ['Shoot Rocket']={t=3000,icon='INV_Ammo_Bullet_03'},
  ['Shredder Armor Melt']={t=2000,icon='Spell_Fire_Incinerate'},
  ['Shrink']={t=3000,icon='Spell_Shadow_AntiShadow'},
  ['Silence']={t=1500,icon='Spell_Holy_Silence'},
  ['Silithid Pox']={t=2000,icon='Spell_Nature_NullifyDisease'},
  ['Silver Skeleton Key']={t=5000,icon='Temp'},
  ['Simple Teleport']={t=1000,icon='Spell_Magic_LesserInvisibilty'},
  ['Simple Teleport Group']={t=2000,icon='Spell_Magic_LesserInvisibilty'},
  ['Simple Teleport Other']={t=1000,icon='Spell_Magic_LesserInvisibilty'},
  ['Skeletal Horse']={t=3000,icon='Ability_Mount_Undeadhorse'},
  ['Skeletal Miner Explode']={t=5000,icon='Spell_Fire_SelfDestruct'},
  ['Skeletal Steed']={t=3000,icon='Ability_Mount_Undeadhorse'},
  ['Skinning']={t=2000,icon='INV_Misc_Pelt_Wolf_01'},
  ['Slam']={t=1500,icon='Ability_Warrior_DecisiveStrike'},
  ['Slave Drain']={t=1000,icon='Spell_Shadow_ChillTouch'},
  ['Sleep']={t=1500,icon='Spell_Nature_Sleep'},
  ['Slime Bolt']={t=2500,icon='Spell_Nature_CorrosiveBreath'},
  ['Sling Dirt']={t=1000,icon='Spell_Nature_Sleep'},
  ['Sling Mud']={t=1000,icon='Spell_Nature_Sleep'},
  ['Slow Poison']={t=1000,icon='Spell_Nature_SlowPoison'},
  ['Slow Poison II']={t=1000,icon='Spell_Nature_SlowPoison'},
  ['Slowing Poison']={t=1000,icon='Spell_Nature_SlowPoison'},
  ['Sludge Toxin']={t=2000,icon='Spell_Nature_Regenerate'},
  ['Small Bronze Bomb']={t=1000,icon='Spell_Fire_SelfDestruct'},
  ['Small Seaforium Charge']={t=5000,icon='Temp'},
  ['Smite']={t=2500,icon='Spell_Holy_HolySmite'},
  ['Smitten']={t=1000,icon='INV_Ammo_Arrow_02'},
  ['Snowman']={t=1500,icon='INV_Ammo_Snowball'},
  ['Snufflenose Command']={t=1500,icon='Spell_Shadow_LifeDrain'},
  ['Sol H']={t=3000,icon='Spell_Frost_ManaRecharge'},
  ['Sol L']={t=3000,icon='Spell_Frost_ManaRecharge'},
  ['Sol M']={t=3000,icon='Spell_Frost_ManaRecharge'},
  ['Sol U']={t=3000,icon='Spell_Frost_ManaRecharge'},
  ['Solid Dynamite']={t=1000,icon='Spell_Fire_SelfDestruct'},
  ['Soothe Animal']={t=1500,icon='Ability_Hunter_BeastSoothe'},
  ['Soul Bite']={t=2000,icon='Spell_Shadow_SiphonMana'},
  ['Soul Breaker']={t=2000,icon='Spell_Shadow_Haunting'},
  ['Soul Consumption']={t=4000,icon='Ability_Racial_Cannibalize'},
  ['Soul Drain']={t=2000,icon='Spell_Shadow_LifeDrain02'},
  ['Soul Fire']={t=6000,icon='Spell_Fire_Fireball02'},
  ['Soul Shatter']={t=2000,icon='Spell_Fire_Fire'},
  ['Soulstone Resurrection']={t=3000,icon='Spell_Shadow_SoulGem'},
  ['South Seas Pirate Disguise']={t=3000,icon='Ability_Rogue_Disguise'},
  ['Southsea Cannon Fire']={t=5000,icon='Spell_Fire_FireBolt02'},
  ['Spark']={t=2000,icon='Spell_Nature_Lightning'},
  ['Spawn Challenge to Urok']={t=2000,icon='Temp'},
  ['Speak with Heads']={t=5000,icon='Spell_Shadow_LifeDrain'},
  ['Spell Deflection (NYI)']={t=1000,icon='Temp'},
  ['Spice Mortar']={t=500,icon='Spell_Fire_Fireball02'},
  ['Spike Volley']={t=500,icon='Ability_ImpalingBolt'},
  ['Spirit Decay']={t=2000,icon='Spell_Holy_HarmUndeadAura'},
  ['Spirit Spawn-out']={t=500,icon='Temp'},
  ['Spirit Steal']={t=2000,icon='Spell_Shadow_Possession'},
  ['Spotted Frostsaber']={t=3000,icon='Ability_Mount_WhiteTiger'},
  ['Spotted Panther']={t=3000,icon='Ability_Mount_BlackPanther'},
  ['Sprinkling Purified Water']={t=6000,icon='Temp'},
  ['Starfire']={t=3500,icon='Spell_Arcane_StarFire'},
  ['Steel Mechanostrider']={t=3000,icon='Ability_Mount_MechaStrider'},
  ['Stone Dwarf Awaken Visual']={t=1500,icon='Spell_Nature_Earthquake'},
  ['Stoned - Channel Cast Visual']={t=3000,icon='Spell_Nature_Cyclone'},
  ['Stoneskin']={t=6000,icon='Spell_Nature_EnchantArmor'},
  ['Stonesplinter Trogg Disguise']={t=3000,icon='Ability_Rogue_Disguise'},
  ['Storm Bolt']={t=1000,icon='INV_Hammer_01'},
  ['Stormpike Battle Charger']={t=3000,icon='Ability_Mount_MountainRam'},
  ['Strength of Arko\'narin']={t=4000,icon='Spell_Nature_AstralRecal'},
  ['Strength of the Ages']={t=2000,icon='Spell_Shadow_Requiem'},
  ['Strike']={t=2000,icon='Temp'},
  ['Striped Frostsaber']={t=3000,icon='Ability_Mount_WhiteTiger'},
  ['Striped Nightsaber']={t=3000,icon='Ability_Mount_BlackPanther'},
  ['Stuck']={t=10000,icon='Spell_Shadow_Teleport'},
  ['Stun Bomb']={t=2000,icon='Spell_Fire_SelfDestruct'},
  ['Stun Bomb Attack']={t=2000,icon='Spell_Fire_SelfDestruct'},
  ['Submerge Visual']={t=1500,icon='Spell_Fire_Volcano'},
  ['Summon']={t=1000,icon='Spell_Arcane_Blink'},
  ['Summon Alarm-o-Bot']={t=250,icon='INV_Gizmo_08'},
  ['Summon Albino Snake']={t=1000,icon='Ability_Seal'},
  ['Summon Albino Snapjaw']={t=1000,icon='Ability_Seal'},
  ['Summon Ancient Spirits']={t=2000,icon='Temp'},
  ['Summon Ancona']={t=1000,icon='Ability_Seal'},
  ['Summon Aquementas']={t=2000,icon='Temp'},
  ['Summon Ar\'lia']={t=2500,icon='Spell_Nature_GroundingTotem'},
  ['Summon Atal\'ai Skeleton']={t=1000,icon='Spell_Shadow_RaiseDead'},
  ['Summon Azure Whelpling']={t=1000,icon='Ability_Seal'},
  ['Summon Baby Shark']={t=1000,icon='Ability_Seal'},
  ['Summon Black Kingsnake']={t=1000,icon='Ability_Seal'},
  ['Summon Black Qiraji Battle Tank']={t=3000,icon='INV_Misc_QirajiCrystal_05'},
  ['Summon Blackhand Dreadweaver']={t=5000,icon='Spell_Nature_Purge'},
  ['Summon Blackhand Veteran']={t=5000,icon='Spell_Nature_Purge'},
  ['Summon Blood Parrot']={t=1000,icon='Ability_Seal'},
  ['Summon Bloodpetal Mini Pests']={t=2000,icon='Spell_Shadow_DarkSummoning'},
  ['Summon Blue Qiraji Battle Tank']={t=3000,icon='INV_Misc_QirajiCrystal_04'},
  ['Summon Blue Racer']={t=1000,icon='Ability_Seal'},
  ['Summon Boar Spirit']={t=1500,icon='Spell_Magic_PolymorphPig'},
  ['Summon Bomb']={t=1000,icon='Ability_Seal'},
  ['Summon Bombay']={t=1000,icon='Ability_Seal'},
  ['Summon Bronze Whelpling']={t=1000,icon='Ability_Seal'},
  ['Summon Brown Snake']={t=1000,icon='Ability_Seal'},
  ['Summon Carrion Scarab']={t=2000,icon='Spell_Shadow_CarrionSwarm'},
  ['Summon Charger']={t=3000,icon='Ability_Mount_Charger'},
  ['Summon Cockatiel']={t=1000,icon='Ability_Seal'},
  ['Summon Cockatoo']={t=1000,icon='Ability_Seal'},
  ['Summon Cockroach']={t=1000,icon='Ability_Seal'},
  ['Summon Common Kitten']={t=2000,icon='INV_Box_PetCarrier_01'},
  ['Summon Cornish Rex']={t=1000,icon='Ability_Seal'},
  ['Summon Corrupted Kitten']={t=1000,icon='Ability_Seal'},
  ['Summon Cottontail Rabbit']={t=1000,icon='Ability_Seal'},
  ['Summon Crimson Snake']={t=1000,icon='Ability_Seal'},
  ['Summon Crimson Whelpling']={t=1000,icon='Ability_Seal'},
  ['Summon Cyclonian']={t=10000,icon='Spell_Nature_EarthBind'},
  ['Summon Dagun']={t=5000,icon='Temp'},
  ['Summon Dark Whelpling']={t=1000,icon='Ability_Seal'},
  ['Summon Dart Frog']={t=1000,icon='Ability_Seal'},
  ['Summon Demon of the Orb']={t=5000,icon='Temp'},
  ['Summon Diablo']={t=1000,icon='Ability_Seal'},
  ['Summon Disgusting Oozeling']={t=1000,icon='Ability_Seal'},
  ['Summon Dreadsteed']={t=3000,icon='Ability_Mount_Dreadsteed'},
  ['Summon Eagle Owl']={t=1000,icon='Ability_Seal'},
  ['Summon Echeyakee']={t=500,icon='Spell_Shadow_LifeDrain'},
  ['Summon Edana Hatetalon']={t=4000,icon='Temp'},
  ['Summon Effect']={t=5000,icon='Spell_Shadow_AnimateDead'},
  ['Summon Elven Wisp']={t=1000,icon='Ability_Seal'},
  ['Summon Embers']={t=2000,icon='Spell_Fire_Fire'},
  ['Summon Emerald Whelpling']={t=1000,icon='Ability_Seal'},
  ['Summon Faeling']={t=1000,icon='Ability_Seal'},
  ['Summon Farm Chicken']={t=1000,icon='Ability_Seal'},
  ['Summon Felhunter']={t=10000,icon='Spell_Shadow_SummonFelHunter'},
  ['Summon Felsteed']={t=3000,icon='Spell_Nature_Swiftness'},
  ['Summon Frail Skeleton']={t=10000,icon='Spell_Shadow_RaiseDead'},
  ['Summon Gnashjaw']={t=3000,icon='Temp'},
  ['Summon Goblin Bomb']={t=250,icon='Ability_Repair'},
  ['Summon Great Horned Owl']={t=1000,icon='Ability_Seal'},
  ['Summon Green Qiraji Battle Tank']={t=3000,icon='INV_Misc_QirajiCrystal_03'},
  ['Summon Green Water Snake']={t=1000,icon='Ability_Seal'},
  ['Summon Green Wing Macaw']={t=1000,icon='Ability_Seal'},
  ['Summon Gunther\'s Visage']={t=2000,icon='Temp'},
  ['Summon Gurky']={t=1000,icon='Ability_Seal'},
  ['Summon Hawk Owl']={t=1000,icon='Ability_Seal'},
  ['Summon Hawksbill Snapjaw']={t=1000,icon='Ability_Seal'},
  ['Summon Helcular\'s Puppets']={t=3000,icon='Spell_Shadow_Haunting'},
  ['Summon Hippogryph Hatchling']={t=1000,icon='Ability_Seal'},
  ['Summon Hyacinth Macaw']={t=1000,icon='Ability_Seal'},
  ['Summon Illusionary Dreamwatchers']={t=1000,icon='Spell_Shadow_Teleport'},
  ['Summon Illusionary Nightmare']={t=2000,icon='Spell_Fire_SealOfFire'},
  ['Summon Illusionary Phantasm']={t=2000,icon='Spell_Fire_SealOfFire'},
  ['Summon Illusory Wraith']={t=1000,icon='Spell_Shadow_Teleport'},
  ['Summon Imp']={t=10000,icon='Spell_Shadow_SummonImp'},
  ['Summon Infernal Servant']={t=2000,icon='Spell_Shadow_SummonInfernal'},
  ['Summon Ishamuhale']={t=2000,icon='Spell_Shadow_LifeDrain'},
  ['Summon Island Frog']={t=1000,icon='Ability_Seal'},
  ['Summon Jubling']={t=1000,icon='Ability_Seal'},
  ['Summon Karang\'s Banner']={t=500,icon='Temp'},
  ['Summon Leatherback Snapjaw']={t=1000,icon='Ability_Seal'},
  ['Summon Lifelike Toad']={t=1000,icon='Ability_Seal'},
  ['Summon Living Flame']={t=2000,icon='Spell_Fire_Fire'},
  ['Summon Loggerhead Snapjaw']={t=1000,icon='Ability_Seal'},
  ['Summon Lunaclaw']={t=3000,icon='Temp'},
  ['Summon Lupine Delusions']={t=1000,icon='Spell_Shadow_Teleport'},
  ['Summon Magic Staff']={t=2000,icon='INV_Staff_26'},
  ['Summon Magram Ravager']={t=4000,icon='Spell_Shadow_RaiseDead'},
  ['Summon Maine Coon']={t=1000,icon='Ability_Seal'},
  ['Summon Mechanical Chicken']={t=1000,icon='Ability_Seal'},
  ['Summon Minion']={t=500,icon='Temp'},
  ['Summon Mr. Wiggles']={t=1000,icon='Ability_Seal'},
  ['Summon Murki']={t=1000,icon='Ability_Seal'},
  ['Summon Murky']={t=1000,icon='Ability_Seal'},
  ['Summon Myzrael']={t=10000,icon='Spell_Shadow_LifeDrain'},
  ['Summon Netherwalker']={t=4000,icon='Spell_Shadow_GatherShadows'},
  ['Summon Olive Snapjaw']={t=1000,icon='Ability_Seal'},
  ['Summon Onyxia Whelp']={t=2000,icon='Temp'},
  ['Summon Orange Tabby']={t=1000,icon='Ability_Seal'},
  ['Summon Orphan']={t=1000,icon='Ability_Seal'},
  ['Summon Panda']={t=1000,icon='Ability_Seal'},
  ['Summon Poley']={t=1000,icon='Ability_Seal'},
  ['Summon Prairie Chicken']={t=1000,icon='Ability_Seal'},
  ['Summon Prairie Dog']={t=1000,icon='Ability_Seal'},
  ['Summon Ragnaros']={t=10000,icon='Spell_Fire_LavaSpawn'},
  ['Summon Razelikh']={t=10000,icon='Temp'},
  ['Summon Red Qiraji Battle Tank']={t=3000,icon='INV_Misc_QirajiCrystal_02'},
  ['Summon Remote-Controlled Golem']={t=3000,icon='Ability_Repair'},
  ['Summon Ribbon Snake']={t=1000,icon='Ability_Seal'},
  ['Summon Riding Gryphon']={t=3000,icon='Ability_BullRush'},
  ['Summon Risen Lackey']={t=2000,icon='Spell_Shadow_RaiseDead'},
  ['Summon Robot']={t=1000,icon='Ability_Seal'},
  ['Summon Rockwing Gargoyles']={t=10000,icon='Spell_Shadow_UnsummonBuilding'},
  ['Summon Rookery Whelp']={t=2000,icon='Temp'},
  ['Summon Scarlet Snake']={t=1000,icon='Ability_Seal'},
  ['Summon Screecher Spirit']={t=2000,icon='Temp'},
  ['Summon Senegal']={t=1000,icon='Ability_Seal'},
  ['Summon Shadowcaster']={t=5000,icon='Spell_Shadow_RaiseDead'},
  ['Summon Shadowstrike']={t=10000,icon='Temp'},
  ['Summon Shield Guard']={t=5000,icon='Spell_Nature_Purge'},
  ['Summon Shy-Rotam']={t=2500,icon='Temp'},
  ['Summon Siamese']={t=1000,icon='Ability_Seal'},
  ['Summon Silver Tabby']={t=1000,icon='Ability_Seal'},
  ['Summon Skeleton']={t=2000,icon='Spell_Shadow_RaiseDead'},
  ['Summon Smolderweb']={t=1000,icon='Ability_Seal'},
  ['Summon Snowshoe Rabbit']={t=1000,icon='Ability_Seal'},
  ['Summon Snowy Owl']={t=1000,icon='Ability_Seal'},
  ['Summon Snufflenose']={t=2000,icon='Spell_Nature_ProtectionformNature'},
  ['Summon Spawn of Bael\'Gar']={t=4000,icon='Spell_Fire_LavaSpawn'},
  ['Summon Speedy']={t=1000,icon='Ability_Seal'},
  ['Summon Spell Guard']={t=5000,icon='Spell_Nature_Purge'},
  ['Summon Spider God']={t=10000,icon='Spell_Shadow_LifeDrain'},
  ['Summon Spirit of Old']={t=4000,icon='Spell_Nature_Purge'},
  ['Summon Spotted Rabbit']={t=1000,icon='Ability_Seal'},
  ['Summon Sprite Darter Hatchling']={t=1000,icon='Ability_Seal'},
  ['Summon Succubus']={t=10000,icon='Spell_Shadow_SummonSuccubus'},
  ['Summon Swamp Ooze']={t=2500,icon='Spell_Shadow_BlackPlague'},
  ['Summon Swamp Spirit']={t=1500,icon='Spell_Nature_AbolishMagic'},
  ['Summon Syndicate Spectre']={t=1000,icon='Spell_Shadow_Twilight'},
  ['Summon Terky']={t=1000,icon='Ability_Seal'},
  ['Summon Tervosh\'s Minion']={t=4000,icon='Spell_Frost_Wisp'},
  ['Summon Thelrin DND']={t=1000,icon='Temp'},
  ['Summon Theurgist']={t=5000,icon='Spell_Nature_Purge'},
  ['Summon Thunderstrike']={t=10000,icon='Temp'},
  ['Summon Timberling']={t=3000,icon='Spell_Nature_ProtectionformNature'},
  ['Summon Tiny Green Dragon']={t=1000,icon='Ability_Seal'},
  ['Summon Tiny Red Dragon']={t=1000,icon='Ability_Seal'},
  ['Summon Tranquil Mechanical Yeti']={t=1000,icon='Ability_Seal'},
  ['Summon Treant Allies']={t=1500,icon='Spell_Nature_ForceOfNature'},
  ['Summon Treasure Horde']={t=1500,icon='Temp'},
  ['Summon Treasure Horde Visual']={t=1500,icon='Temp'},
  ['Summon Tree Frog']={t=1000,icon='Ability_Seal'},
  ['Summon Viper']={t=1500,icon='Spell_Nature_ResistMagic'},
  ['Summon Voidwalker']={t=10000,icon='Spell_Shadow_SummonVoidWalker'},
  ['Summon Warhorse']={t=3000,icon='Spell_Nature_Swiftness'},
  ['Summon Water Elemental']={t=2000,icon='Spell_Shadow_SealOfKings'},
  ['Summon Whiskers']={t=1000,icon='Ability_Seal'},
  ['Summon White Kitten']={t=1000,icon='Ability_Seal'},
  ['Summon White Plymouth Rock']={t=1000,icon='Ability_Seal'},
  ['Summon White Tiger Cub']={t=1000,icon='Ability_Seal'},
  ['Summon Witherbark Felhunter']={t=3000,icon='Spell_Shadow_SummonFelHunter'},
  ['Summon Wood Frog']={t=1000,icon='Ability_Seal'},
  ['Summon Worg Pup']={t=1000,icon='Ability_Seal'},
  ['Summon Xorothian Dreadsteed']={t=5000,icon='Temp'},
  ['Summon Yellow Qiraji Battle Tank']={t=3000,icon='INV_Misc_QirajiCrystal_01'},
  ['Summon Zergling']={t=1000,icon='Ability_Seal'},
  ['Summon Zombie']={t=2000,icon='Spell_Shadow_RaiseDead'},
  ['Summoned Urok']={t=1000,icon='Temp'},
  ['Super Crystal']={t=6000,icon='Temp'},
  ['Superior Healing Ward']={t=2000,icon='Spell_Holy_LayOnHands'},
  ['Sweep']={t=1500,icon='Spell_Nature_Thorns'},
  ['Sweet Surprise']={t=1000,icon='INV_ValentinesChocolate03'},
  ['Swift Blue Raptor']={t=3000,icon='Ability_Mount_Raptor'},
  ['Swift Brown Ram']={t=3000,icon='Ability_Mount_MountainRam'},
  ['Swift Brown Steed']={t=3000,icon='Ability_Mount_RidingHorse'},
  ['Swift Brown Wolf']={t=3000,icon='Ability_Mount_BlackDireWolf'},
  ['Swift Dawnsaber']={t=3000,icon='Ability_Mount_JungleTiger'},
  ['Swift Frostsaber']={t=3000,icon='Ability_Mount_WhiteTiger'},
  ['Swift Gray Ram']={t=3000,icon='Ability_Mount_MountainRam'},
  ['Swift Gray Wolf']={t=3000,icon='Ability_Mount_WhiteDireWolf'},
  ['Swift Green Mechanostrider']={t=3000,icon='Ability_Mount_MechaStrider'},
  ['Swift Mistsaber']={t=3000,icon='Ability_Mount_BlackPanther'},
  ['Swift Olive Raptor']={t=3000,icon='Ability_Mount_Raptor'},
  ['Swift Orange Raptor']={t=3000,icon='Ability_Mount_Raptor'},
  ['Swift Palomino']={t=3000,icon='Ability_Mount_RidingHorse'},
  ['Swift Razzashi Raptor']={t=3000,icon='Ability_Mount_Raptor'},
  ['Swift Stormsaber']={t=3000,icon='Ability_Mount_BlackPanther'},
  ['Swift Timber Wolf']={t=3000,icon='Ability_Mount_WhiteDireWolf'},
  ['Swift White Mechanostrider']={t=3000,icon='Ability_Mount_MechaStrider'},
  ['Swift White Ram']={t=3000,icon='Ability_Mount_MountainRam'},
  ['Swift White Steed']={t=3000,icon='Ability_Mount_RidingHorse'},
  ['Swift Yellow Mechanostrider']={t=3000,icon='Ability_Mount_MechaStrider'},
  ['Swift Zulian Tiger']={t=3000,icon='Ability_Mount_JungleTiger'},
  ['Symbol of Life']={t=10000,icon='Spell_Holy_Resurrection'},
  ['Syndicate Bomb']={t=3000,icon='Spell_Shadow_MindBomb'},
  ['Syndicate Disguise']={t=3000,icon='Ability_Rogue_Disguise'},
  ['Syndicate Tracker (MURP) DND']={t=1000,icon='Spell_Arcane_Blink'},
  ['Taelan Death']={t=2000,icon='Temp'},
  ['Talvash\'s Necklace Repair']={t=4500,icon='Spell_Holy_Restoration'},
  ['Tammra Sapling']={t=1300,icon='Temp'},
  ['Tawny Sabercat']={t=3000,icon='Ability_Mount_JungleTiger'},
  ['Teach Bark of Doom']={t=1000,icon='Temp'},
  ['Teal Kodo']={t=3000,icon='Ability_Mount_Kodo_02'},
  ['Teleport']={t=1000,icon='Spell_Magic_LesserInvisibilty'},
  ['Teleport Altar of the Tides']={t=2000,icon='Temp'},
  ['Teleport Anvilmar']={t=2000,icon='Temp'},
  ['Teleport Barracks']={t=2000,icon='Temp'},
  ['Teleport Cemetary']={t=2000,icon='Temp'},
  ['Teleport Darkshire']={t=2000,icon='Temp'},
  ['Teleport Duskwood']={t=2000,icon='Temp'},
  ['Teleport Elwynn']={t=2000,icon='Temp'},
  ['Teleport Goldshire']={t=2000,icon='Temp'},
  ['Teleport Lighthouse']={t=2000,icon='Temp'},
  ['Teleport Monastery']={t=2000,icon='Temp'},
  ['Teleport Moonbrook']={t=2000,icon='Temp'},
  ['Teleport Northshire Abbey']={t=2000,icon='Temp'},
  ['Teleport Treant']={t=2000,icon='Temp'},
  ['Teleport Westfall']={t=2000,icon='Temp'},
  ['Teleport from Azshara Tower']={t=1000,icon='Spell_Nature_EarthBind'},
  ['Teleport to Azshara Tower']={t=1000,icon='Spell_Nature_AstralRecalGroup'},
  ['Teleport to Darnassus - Event']={t=1000,icon='Temp'},
  ['Teleport: Darnassus']={t=10000,icon='Spell_Arcane_TeleportDarnassus'},
  ['Teleport: Ironforge']={t=10000,icon='Spell_Arcane_TeleportIronForge'},
  ['Teleport: Moonglade']={t=10000,icon='Spell_Arcane_TeleportMoonglade'},
  ['Teleport: Orgrimmar']={t=10000,icon='Spell_Arcane_TeleportOrgrimmar'},
  ['Teleport: Stormwind']={t=10000,icon='Spell_Arcane_TeleportStormWind'},
  ['Teleport: Thunder Bluff']={t=10000,icon='Spell_Arcane_TeleportThunderBluff'},
  ['Teleport: Undercity']={t=10000,icon='Spell_Arcane_TeleportUnderCity'},
  ['Tell Joke']={t=2000,icon='Spell_Shadow_LifeDrain'},
  ['Temperature Reading']={t=2000,icon='Temp'},
  ['Test Sharpen Blade']={t=3000,icon='Temp'},
  ['Test of Lore']={t=2000,icon='Spell_Nature_EarthBind'},
  ['Tharnariun Cure 1']={t=750,icon='Spell_Nature_RemoveDisease'},
  ['Tharnariun\'s Heal']={t=500,icon='Spell_Nature_MagicImmunity'},
  ['The Big One']={t=3000,icon='Spell_Fire_SelfDestruct'},
  ['Thorium Grenade']={t=1000,icon='Spell_Fire_SelfDestruct'},
  ['Thousand Blades']={t=250,icon='INV-Sword_53'},
  ['Threatening Gaze']={t=2000,icon='Spell_Shadow_Charm'},
  ['Threatening Growl']={t=1000,icon='Ability_Racial_Cannibalize'},
  ['Throw Axe']={t=1000,icon='INV_Axe_08'},
  ['Throw Cupid\'s Dart']={t=1000,icon='Temp'},
  ['Throw Dark Iron Ale']={t=500,icon='Temp'},
  ['Throw Dynamite']={t=2000,icon='Spell_Fire_SelfDestruct'},
  ['Throw Nightmare Object']={t=2000,icon='Spell_Fire_SelfDestruct'},
  ['Throw Rock']={t=3000,icon='Ability_GolemStormBolt'},
  ['Throw Rock II']={t=3000,icon='Ability_GolemStormBolt'},
  ['Tiger']={t=3000,icon='Ability_Mount_JungleTiger'},
  ['Time Lapse']={t=2000,icon='Spell_Arcane_PortalOrgrimmar'},
  ['Time Stop']={t=3000,icon='Temp'},
  ['Time Stop Visual DND']={t=3000,icon='Temp'},
  ['Toasted Smorc']={t=1000,icon='INV_SummerFest_Smorc'},
  ['Torch Combine']={t=5000,icon='Temp'},
  ['Torch Toss']={t=3000,icon='Spell_Fire_Fireball02'},
  ['Toss Fuel on Bonfire']={t=500,icon='Temp'},
  ['Toss Stink Bomb']={t=2000,icon='INV_Misc_Bowl_01'},
  ['Touch of Death']={t=3000,icon='Spell_Shadow_UnsummonBuilding'},
  ['Touch of Ravenclaw']={t=1500,icon='Spell_Shadow_Requiem'},
  ['Toxic Bolt']={t=2500,icon='Spell_Nature_CorrosiveBreath'},
  ['Toxic Saliva']={t=500,icon='Spell_Nature_CorrosiveBreath'},
  ['Toxic Spit']={t=2500,icon='Spell_Nature_CorrosiveBreath'},
  ['Transform Victim']={t=2000,icon='Spell_Magic_LesserInvisibilty'},
  ['Trelane\'s Freezing Touch']={t=3000,icon='Spell_Shadow_UnsummonBuilding'},
  ['Triage']={t=7000,icon='Temp'},
  ['True Fulfillment']={t=500,icon='Spell_Shadow_Charm'},
  ['Truesilver Skeleton Key']={t=5000,icon='Temp'},
  ['Tune Up']={t=7000,icon='INV_Gizmo_02'},
  ['Turn Undead']={t=1500,icon='Spell_Holy_TurnUndead'},
  ['Turquoise Raptor']={t=3000,icon='Ability_Mount_Raptor'},
  ['Uldaman Boss Agro']={t=5000,icon='Spell_Nature_EarthBindTotem'},
  ['Uldaman Key Staff']={t=5000,icon='Temp'},
  ['Uldaman Sub-Boss Agro']={t=5000,icon='Spell_Nature_EarthBindTotem'},
  ['Unholy Curse']={t=1000,icon='Spell_Shadow_CurseOfMannoroth'},
  ['Unlock Maury\'s Foot']={t=5000,icon='Temp'},
  ['Unlocking']={t=5000,icon='Temp'},
  ['Unpainted Mechanostrider']={t=3000,icon='Ability_Mount_MechaStrider'},
  ['Unstable Concoction']={t=3000,icon='Spell_Fire_Incinerate'},
  ['Urok Minions Vanish']={t=2000,icon='Temp'},
  ['Use Bauble']={t=3000,icon='Temp'},
  ['Uther\'s Tribute']={t=2000,icon='Temp'},
  ['Veil of Shadow']={t=1500,icon='Spell_Shadow_GatherShadows'},
  ['Venom Spit']={t=2500,icon='Spell_Nature_CorrosiveBreath'},
  ['Venom\'s Bane']={t=2000,icon='Temp'},
  ['Very Berry Cream']={t=1000,icon='INV_ValentinesChocolate02'},
  ['Viewing Room Student Transform - Effect']={t=1000,icon='Temp'},
  ['Violet Raptor']={t=3000,icon='Ability_Mount_Raptor'},
  ['Vipore Cat Form DND']={t=1000,icon='Temp'},
  ['Void Bolt']={t=3000,icon='Spell_Shadow_ShadowBolt'},
  ['Voidwalker Guardian']={t=3000,icon='Spell_Shadow_SummonVoidWalker'},
  ['Volatile Infection']={t=2000,icon='Spell_Holy_HarmUndeadAura'},
  ['Volley']={t=3000,icon='Ability_TheBlackArrow'},
  ['Volley II']={t=3000,icon='Ability_TheBlackArrow'},
  ['Voodoo']={t=1000,icon='Spell_Shadow_AntiShadow'},
  ['Voodoo Hex']={t=1000,icon='Spell_Shadow_CurseOfMannoroth'},
  ['Wail of the Banshee']={t=2000,icon='Spell_Shadow_Possession'},
  ['Wandering Plague']={t=2000,icon='Spell_Shadow_CallofBone'},
  ['War Stomp']={t=500,icon='Ability_WarStomp'},
  ['Warosh\'s Transform']={t=1000,icon='Temp'},
  ['Water Bubble']={t=1000,icon='Spell_Frost_Wisp'},
  ['Weak Frostbolt']={t=2200,icon='Spell_Frost_FrostBolt02'},
  ['Whirling Barrage']={t=1500,icon='INV_Spear_05'},
  ['White Mechanostrider']={t=3000,icon='Ability_Mount_MechaStrider'},
  ['White Ram']={t=3000,icon='Ability_Mount_MountainRam'},
  ['White Stallion']={t=3000,icon='Ability_Mount_RidingHorse'},
  ['Wide Sweep']={t=4000,icon='Ability_Whirlwind'},
  ['Widow\'s Embrace']={t=500,icon='Spell_Arcane_Blink'},
  ['Wild Regeneration']={t=3000,icon='Spell_Nature_Rejuvenation'},
  ['Will of Shahram']={t=1000,icon='Spell_Holy_MindVision'},
  ['Windsor Death DND']={t=1500,icon='Temp'},
  ['Windsor Reading Tablets DND']={t=10000,icon='Temp'},
  ['Wing Buffet']={t=1000,icon='INV_Misc_MonsterScales_14'},
  ['Winter Wolf']={t=3000,icon='Ability_Mount_WhiteDireWolf'},
  ['Winterax Wisdom']={t=1000,icon='Ability_Ambush'},
  ['Winterspring Frostsaber']={t=3000,icon='Ability_Mount_PinkTiger'},
  ['Wither']={t=1500,icon='Spell_Nature_NullifyDisease'},
  ['Wither Touch']={t=2000,icon='Spell_Nature_Drowsy'},
  ['Wizard Oil']={t=3000,icon='Temp'},
  ['Word of Thawing']={t=5000,icon='Temp'},
  ['Worm Sweep']={t=1000,icon='INV_Misc_MonsterScales_05'},
  ['Wound Poison']={t=3000,icon='INV_Misc_Herb_16'},
  ['Wrath']={t=2000,icon='Spell_Nature_AbolishMagic'},
  ['Yenniku\'s Release']={t=4000,icon='Spell_Shadow_LifeDrain'},
  ['[PH] Buttress Activator']={t=5000,icon='Temp'},
  ['[PH] Cystal Bazooka']={t=1000,icon='Temp'},
  ['[PH] Teleport to Auberdine']={t=2000,icon='Temp'},
  ['[PH] Teleport to Balthule']={t=2000,icon='Temp'},
  ['[PH] Teleport to Booty Bay']={t=2000,icon='Temp'},
  ['[PH] Teleport to Felwood']={t=2000,icon='Temp'},
  ['[PH] Teleport to Grom\'Gol']={t=2000,icon='Temp'},
  ['[PH] Teleport to Menethil Harbor']={t=2000,icon='Temp'},
  ['[PH] Teleport to Orgrimmar']={t=2000,icon='Temp'},
  ['[PH] Teleport to Ratchet']={t=2000,icon='Temp'},
  ['[PH] Teleport to Theramore']={t=2000,icon='Temp'},
  ['[PH] Teleport to Undercity']={t=2000,icon='Temp'},
}

pfUI_locale["enUS"]["debuffs"] = {
  ['AE Charm']={[0]=300.0,},
  ['Abomination Spit']={[0]=10.0,},
  ['Acid Slime']={[0]=30.0,},
  ['Acid Spit']={[0]=30.0,},
  ['Acid Splash']={[0]=30.0,},
  ['Acid Spray']={[0]=10.0,},
  ['Acid Volley']={[0]=25.0,},
  ['Acid of Hakkar']={[0]=60.0,},
  ['Advanced Target Dummy Spawn Effect']={[0]=10.0,},
  ['Aftermath']={[0]=5.0,},
  ['Agility VIII']={[0]=3600.0,},
  ['Agonizing Pain']={[0]=15.0,},
  ['Air Bubbles']={[0]=10.0,},
  ['Amplify Damage']={[0]=10.0,},
  ['Amplify Flames']={[0]=30.0,},
  ['Ancient Despair']={[0]=5.0,},
  ['Ancient Dread']={[0]=900.0,},
  ['Ancient Hysteria']={[0]=900.0,},
  ['Anub\'Rekhan\'s Aura']={[0]=5.0,},
  ['Aquatic Miasma']={[0]=3600.0,},
  ['Arantir\'s Rage']={[0]=6.0,},
  ['Arcane Burst']={[0]=8.0,},
  ['Arcane Intellect']={[0]=1800.0,},
  ['Arcane Missiles']={[1]=3.0,[2]=4.0,[3]=5.0,[4]=5.0,[5]=5.0,[6]=5.0,[7]=5.0,[8]=5.0,[0]=5.0,},
  ['Arcane Weakness']={[0]=45.0,},
  ['Armor IV']={[0]=3600.0,},
  ['Armor Shatter']={[0]=45.0,},
  ['Arugal\'s Curse']={[0]=10.0,},
  ['Arugal\'s Gift']={[0]=300.0,},
  ['Aspect of Arlokk']={[0]=2.0,},
  ['Aspect of Jeklik']={[0]=5.0,},
  ['Aspect of Mar\'li']={[0]=6.0,},
  ['Aspect of Venoxis']={[0]=10.0,},
  ['Atal\'ai Poison']={[0]=30.0,},
  ['Attack Order']={[0]=10.0,},
  ['Attract Rager']={[0]=2.0,},
  ['Aura Leech (PT)']={[0]=15.0,},
  ['Aura of Agony']={[0]=8.0,},
  ['Aura of Battle']={[0]=10.0,},
  ['Aura of Command']={[0]=30.0,},
  ['Aura of Fear']={[0]=3.0,},
  ['Aural Shock']={[0]=300.0,},
  ['Awaken Earthen Guardians']={[0]=5.0,},
  ['Axe Flurry']={[0]=2.0,},
  ['Axe Toss']={[0]=3.0,},
  ['Azrethoc\'s Stomp']={[0]=5.0,},
  ['Backhand']={[0]=2.0,},
  ['Balnazzar Transform Stun']={[0]=5.0,},
  ['Baneful Poison']={[0]=120.0,},
  ['Banish']={[1]=20.0,[2]=30.0,[0]=30.0,},
  ['Banshee Curse']={[0]=12.0,},
  ['Banshee Shriek']={[0]=5.0,},
  ['Barbed Sting']={[0]=300.0,},
  ['Barrel Explode']={[0]=3.0,},
  ['Bash']={[1]=2.0,[2]=3.0,[3]=4.0,[0]=4.0,},
  ['Battle Command']={[0]=6.0,},
  ['Battle Net']={[0]=10.0,},
  ['Battle Shout']={[0]=900.0,},
  ['Battle Standard']={[0]=3.0,},
  ['Battlegear of Might']={[0]=6.0,},
  ['Befuddlement']={[0]=15.0,},
  ['Bellowing Roar']={[0]=3.0,},
  ['Berserk']={[0]=30.0,},
  ['Biletoad Infection']={[0]=180.0,},
  ['Black Arrow']={[0]=30.0,},
  ['Black March Blessing']={[0]=6.0,},
  ['Black Rot']={[0]=1800.0,},
  ['Black Sludge']={[0]=120.0,},
  ['Blackout']={[0]=3.0,},
  ['Blast Wave']={[0]=6.0,},
  ['Blaze']={[0]=30.0,},
  ['Bleakwood Curse']={[0]=60.0,},
  ['Blessing of Nordrassil']={[0]=10.0,},
  ['Blight']={[0]=60.0,},
  ['Blind']={[0]=10.0,},
  ['Blink']={[0]=1.0,},
  ['Blood Howl']={[0]=15.0,},
  ['Blood Siphon']={[0]=8.0,},
  ['Bloodfang']={[0]=6.0,},
  ['Bloodlord\'s Aura']={[0]=5.0,},
  ['Bloodlust']={[0]=15.0,},
  ['Bloodpetal Poison']={[0]=30.0,},
  ['Boar Charge']={[0]=1.0,},
  ['Bone Smelt']={[0]=20.0,},
  ['Bottle of Poison']={[0]=30.0,},
  ['Boulder']={[0]=10.0,},
  ['Brain Damage']={[0]=30.0,},
  ['Brain Hacker']={[0]=30.0,},
  ['Breath of Sargeras']={[0]=90.0,},
  ['Brood Affliction: Black']={[0]=600.0,},
  ['Brood Affliction: Blue']={[0]=600.0,},
  ['Brood Affliction: Bronze']={[0]=600.0,},
  ['Brood Affliction: Green']={[0]=600.0,},
  ['Brood Affliction: Red']={[0]=600.0,},
  ['Brood Power: Blue']={[0]=6.0,},
  ['Brood Power: Bronze']={[0]=5.0,},
  ['Brood Power: Green']={[0]=6.0,},
  ['Brood Power: Red']={[0]=5.0,},
  ['Bruising Blow']={[0]=5.0,},
  ['Burning Adrenaline']={[0]=20.0,},
  ['Burning Winds']={[0]=8.0,},
  ['Cadaver Worms']={[0]=600.0,},
  ['Call of the Grave']={[0]=60.0,},
  ['Cantation of Manifestation']={[0]=600.0,},
  ['Capture Felhound Spirit']={[0]=9.0,},
  ['Capture Infernal Spirit']={[0]=9.0,},
  ['Capture Spirit']={[0]=9.0,},
  ['Capture Treant']={[0]=5.0,},
  ['Cause Insanity']={[0]=6.0,},
  ['Cauterizing Flames']={[0]=900.0,},
  ['Chains of Ice']={[1]=15.0,[2]=20.0,[0]=20.0,},
  ['Chains of Kel\'Thuzad']={[0]=20.0,},
  ['Challenging Roar']={[0]=6.0,},
  ['Challenging Shout']={[0]=6.0,},
  ['Charge']={[0]=4.0,},
  ['Charge Stun']={[0]=1.0,},
  ['Cheap Shot']={[0]=4.0,},
  ['Chemical Peel']={[0]=10.0,},
  ['Chill']={[0]=30.0,},
  ['Chill Nova']={[0]=10.0,},
  ['Chilled']={[0]=1.5,},
  ['Chilling Breath']={[0]=12.0,},
  ['Chilling Touch']={[0]=8.0,},
  ['Chromatic Mutation']={[0]=300.0,},
  ['Claw Grasp']={[0]=4.0,},
  ['Clear All Shackels']={[0]=1.0,},
  ['Cleave Armor']={[0]=15.0,},
  ['Clenched Pinchers']={[0]=15.0,},
  ['Cold Eye']={[0]=15.0,},
  ['Concussion Blow']={[0]=5.0,},
  ['Concussive Shot']={[0]=4.0,},
  ['Conflagration']={[0]=10.0,},
  ['Consume']={[0]=15.0,},
  ['Contagion of Rot']={[0]=240.0,},
  ['Control Machine']={[0]=60.0,},
  ['Corrosive Acid']={[0]=300.0,},
  ['Corrosive Acid Spit']={[0]=10.0,},
  ['Corrosive Ooze']={[0]=60.0,},
  ['Corrosive Poison']={[0]=30.0,},
  ['Corrosive Venom Spit']={[0]=10.0,},
  ['Corrupted Agility']={[0]=4.0,},
  ['Corrupted Fear']={[0]=2.0,},
  ['Corrupted Healing']={[0]=30.0,},
  ['Corrupted Intellect']={[0]=4.0,},
  ['Corrupted Stamina']={[0]=4.0,},
  ['Corrupted Strength']={[0]=4.0,},
  ['Corrupted Totems']={[0]=30.0,},
  ['Corruption']={[1]=12.0,[2]=15.0,[3]=18.0,[4]=18.0,[5]=18.0,[6]=18.0,[7]=18.0,[0]=18.0,},
  ['Corruption of the Earth']={[0]=10.0,},
  ['Counterattack']={[0]=5.0,},
  ['Counterspell']={[0]=10.0,},
  ['Counterspell - Silenced']={[0]=4.0,},
  ['Cowering Roar']={[0]=5.0,},
  ['Cozy Fire']={[0]=60.0,},
  ['Crash of Waves']={[0]=10.0,},
  ['Create Heart of Hakkar Explosion']={[0]=30.0,},
  ['Create Heart of Hakkar Rift']={[0]=30.0,},
  ['Create Heart of Hakkar Summon Circle']={[0]=30.0,},
  ['Create Zul\'s Aura']={[0]=30.0,},
  ['Creature of Nightmare']={[0]=30.0,},
  ['Creeper Venom']={[0]=300.0,},
  ['Creeping Mold']={[0]=60.0,},
  ['Creeping Plague']={[0]=20.0,},
  ['Cripple']={[0]=20.0,},
  ['Crippling Poison']={[0]=12.0,},
  ['Crowd Pummel']={[0]=5.0,},
  ['Crusader Strike']={[0]=30.0,},
  ['Crusader\'s Hammer']={[0]=4.0,},
  ['Crush Armor']={[0]=30.0,},
  ['Crystal Gaze']={[0]=6.0,},
  ['Crystal Yield']={[0]=120.0,},
  ['Crystalline Slumber']={[0]=15.0,},
  ['Curse of Agony']={[0]=24.0,},
  ['Curse of Blood']={[0]=600.0,},
  ['Curse of Doom']={[0]=60.0,},
  ['Curse of Exhaustion']={[0]=12.0,},
  ['Curse of Hakkar']={[0]=120.0,},
  ['Curse of Idiocy']={[0]=120.0,},
  ['Curse of Impotence']={[0]=120.0,},
  ['Curse of Mending']={[0]=180.0,},
  ['Curse of Recklessness']={[0]=120.0,},
  ['Curse of Shadow']={[0]=300.0,},
  ['Curse of Stalvan']={[0]=600.0,},
  ['Curse of Thorns']={[0]=180.0,},
  ['Curse of Thule']={[0]=240.0,},
  ['Curse of Timmy']={[0]=60.0,},
  ['Curse of Tongues']={[0]=30.0,},
  ['Curse of Tuten\'kash']={[0]=900.0,},
  ['Curse of Vengeance']={[0]=900.0,},
  ['Curse of Weakness']={[0]=120.0,},
  ['Curse of the Bleakheart']={[0]=180.0,},
  ['Curse of the Darkmaster']={[0]=60.0,},
  ['Curse of the Deadwood']={[0]=120.0,},
  ['Curse of the Dreadmaul']={[0]=60.0,},
  ['Curse of the Elemental Lord']={[0]=60.0,},
  ['Curse of the Elements']={[0]=300.0,},
  ['Curse of the Eye']={[0]=120.0,},
  ['Curse of the Fallen Magram']={[0]=900.0,},
  ['Curse of the Firebrand']={[0]=300.0,},
  ['Curse of the Plague Rat']={[0]=14.0,},
  ['Curse of the Plaguebringer']={[0]=10.0,},
  ['Curse of the Shadowhorn']={[0]=300.0,},
  ['Curse of the Tribes']={[0]=1800.0,},
  ['Cursed Blade']={[0]=20.0,},
  ['Cursed Blood']={[0]=600.0,},
  ['Damage Shield']={[0]=10.0,},
  ['Dark Energy']={[0]=300.0,},
  ['Dark Iron Taskmaster Death']={[0]=6.0,},
  ['Dark Plague']={[0]=90.0,},
  ['Dark Sludge']={[0]=300.0,},
  ['Darken Vision']={[0]=12.0,},
  ['Daunting Growl']={[0]=30.0,},
  ['Dazed']={[0]=4.0,},
  ['Deadly Leech Poison']={[0]=45.0,},
  ['Deadly Poison']={[0]=12.0,},
  ['Deadly Poison II']={[0]=12.0,},
  ['Deadly Poison III']={[0]=12.0,},
  ['Deadly Poison IV']={[0]=12.0,},
  ['Deadly Poison V']={[0]=12.0,},
  ['Deadly Toxin']={[0]=12.0,},
  ['Deadly Toxin II']={[0]=12.0,},
  ['Deadly Toxin III']={[0]=12.0,},
  ['Deadly Toxin IV']={[0]=12.0,},
  ['Deafening Screech']={[0]=8.0,},
  ['Death Bed']={[0]=10.0,},
  ['Death Coil']={[0]=3.0,},
  ['Debilitate']={[0]=15.0,},
  ['Debilitating Charge']={[0]=8.0,},
  ['Debilitating Touch']={[0]=120.0,},
  ['Decayed Agility']={[0]=300.0,},
  ['Decayed Strength']={[0]=300.0,},
  ['Decimate']={[0]=3.0,},
  ['Decrepit Fever']={[0]=21.0,},
  ['Deep Sleep']={[0]=10.0,},
  ['Deep Slumber']={[0]=15.0,},
  ['Deep Wound']={[0]=12.0,},
  ['Defile']={[0]=10.0,},
  ['Defiling Aura']={[0]=5.0,},
  ['Delusions of Jin\'do']={[0]=20.0,},
  ['Demonfork']={[0]=25.0,},
  ['Demoralize']={[0]=30.0,},
  ['Demoralizing Roar']={[0]=30.0,},
  ['Demoralizing Shout']={[0]=30.0,},
  ['Detect Greater Invisibility']={[0]=600.0,},
  ['Detect Magic']={[0]=120.0,},
  ['Detonate Mana']={[0]=5.0,},
  ['Devilsaur Barb']={[0]=10.0,},
  ['Devouring Plague']={[0]=24.0,},
  ['Dire Growl']={[0]=15.0,},
  ['Dirk\'s Rememberin\' Poison']={[0]=30.0,},
  ['Disarm']={[0]=10.0,},
  ['Discombobulate']={[0]=12.0,},
  ['Disease Buffet']={[0]=20.0,},
  ['Diseased Shot']={[0]=300.0,},
  ['Diseased Slime']={[0]=120.0,},
  ['Diseased Spit']={[0]=10.0,},
  ['Disjunction']={[0]=300.0,},
  ['Dismember']={[0]=10.0,},
  ['Dismounting Shot']={[0]=2.0,},
  ['Dissolve Armor']={[0]=20.0,},
  ['Distract Move']={[0]=7.0,},
  ['Distracting Pain']={[0]=15.0,},
  ['Distracting Spit']={[0]=15.0,},
  ['Divine Spirit']={[0]=1800.0,},
  ['Dominate Mind']={[0]=10.0,},
  ['Domination']={[0]=15.0,},
  ['Dominion of Soul']={[0]=60.0,},
  ['Drain Life']={[0]=5.0,},
  ['Drain Mana']={[0]=5.0,},
  ['Drain Soul']={[0]=15.0,},
  ['Draw Spirit']={[0]=5.0,},
  ['Draw of Thistlenettle']={[0]=8.0,},
  ['Dreadful Fright']={[0]=5.0,},
  ['Dredge Sickness']={[0]=300.0,},
  ['Drowning Death']={[0]=300.0,},
  ['Druid\'s Slumber']={[0]=15.0,},
  ['Dust Cloud']={[0]=12.0,},
  ['Eagle Claw']={[0]=15.0,},
  ['Earth Shock']={[0]=2.0,},
  ['Earthbind']={[0]=5.0,},
  ['Earthborer Acid']={[0]=30.0,},
  ['Earthgrab']={[0]=4.0,},
  ['Echoing Roar']={[0]=20.0,},
  ['Ectoplasmic Distiller']={[0]=3.0,},
  ['Egg Explosion']={[0]=3.0,},
  ['Electrified Net']={[0]=10.0,},
  ['Elemental Fire']={[0]=8.0,},
  ['Elemental Vulnerability']={[0]=30.0,},
  ['Elixir of the Giants']={[0]=1200.0,},
  ['Emeriss Aura']={[0]=10.0,},
  ['Encage']={[0]=30.0,},
  ['Encasing Webs']={[0]=6.0,},
  ['Enchanting Lullaby']={[0]=10.0,},
  ['Encouragement']={[0]=1800.0,},
  ['Enervate']={[0]=10.0,},
  ['Enfeeble']={[0]=120.0,},
  ['Engulfing Flames']={[0]=6.0,},
  ['Enraging Bite']={[0]=6.0,},
  ['Enslave']={[0]=15.0,},
  ['Enslave Demon']={[0]=300.0,},
  ['Entangle']={[0]=8.0,},
  ['Entangling Roots']={[1]=12.0,[2]=15.0,[3]=18.0,[4]=21.0,[5]=24.0,[6]=27.0,[0]=27.0,},
  ['Entrapment']={[0]=5.0,},
  ['Enveloping Web']={[0]=8.0,},
  ['Enveloping Webs']={[0]=8.0,},
  ['Enveloping Winds']={[0]=10.0,},
  ['Eskhandar\'s Rake']={[0]=30.0,},
  ['Essence of the Red']={[0]=180.0,},
  ['Explode']={[0]=2.5,},
  ['Explode Bug']={[0]=4.0,},
  ['Explosive Trap Effect']={[0]=20.0,},
  ['Expose Armor']={[0]=30.0,},
  ['Expose Weakness']={[0]=5.0,},
  ['Extract Essence']={[0]=12.0,},
  ['Eye Peck']={[0]=12.0,},
  ['Eye of Immol\'thar']={[0]=4.0,},
  ['Faerie Fire']={[0]=40.0,},
  ['Faerie Fire (Feral)']={[0]=40.0,},
  ['Fang of the Crystal Spider']={[0]=10.0,},
  ['Fatal Sting']={[0]=12.0,},
  ['Fear']={[1]=10.0,[2]=15.0,[3]=20.0,[0]=20.0,},
  ['Fear (NYI)']={[0]=15.0,},
  ['Feeblemind']={[0]=30.0,},
  ['Feeblemind II']={[0]=30.0,},
  ['Feeblemind III']={[0]=30.0,},
  ['Fel Stomp']={[0]=3.0,},
  ['Feral Charge Effect']={[0]=4.0,},
  ['Festering Bites']={[0]=1800.0,},
  ['Festering Rash']={[0]=1800.0,},
  ['Festival Fortune!']={[0]=1800.0,},
  ['Fevered Fatigue']={[0]=1800.0,},
  ['Fevered Plague']={[0]=180.0,},
  ['Fire Festival Fortitude']={[0]=3600.0,},
  ['Fire Festival Fury']={[0]=3600.0,},
  ['Fire Protection']={[0]=3600.0,},
  ['Fire Vulnerability']={[0]=30.0,},
  ['Fire Weakness']={[0]=45.0,},
  ['Fireball']={[1]=4.0,[2]=6.0,[3]=6.0,[4]=8.0,[5]=8.0,[6]=8.0,[7]=8.0,[8]=8.0,[9]=8.0,[10]=8.0,[11]=8.0,[12]=8.0,[0]=8.0,},
  ['Fist of Ragnaros']={[0]=5.0,},
  ['Five Fat Finger Exploding Heart Technique']={[0]=30.0,},
  ['Fixate']={[0]=10.0,},
  ['Flame Buffet']={[0]=45.0,},
  ['Flame Lash']={[0]=45.0,},
  ['Flame Shock']={[0]=12.0,},
  ['Flameshocker\'s Revenge']={[0]=2.0,},
  ['Flameshocker\'s Touch']={[0]=3.0,},
  ['Flash Freeze']={[0]=5.0,},
  ['Flesh Rot']={[0]=10.0,},
  ['Forsaken Skills']={[0]=300.0,},
  ['Foul Chill']={[0]=120.0,},
  ['Frailty']={[0]=60.0,},
  ['Freeze']={[0]=15.0,},
  ['Freeze III PROC']={[0]=5.0,},
  ['Freeze Solid']={[0]=10.0,},
  ['Freezing Claw']={[0]=5.0,},
  ['Freezing Trap Effect']={[1]=10.0,[2]=15.0,[3]=20.0,[0]=20.0,},
  ['Frenzied Command']={[0]=10.0,},
  ['Frenzied Dive']={[0]=2.0,},
  ['Frightalon']={[0]=60.0,},
  ['Frightening Shriek']={[0]=6.0,},
  ['Frost']={[0]=10.0,},
  ['Frost Aura']={[0]=5.0,},
  ['Frost Burn']={[0]=15.0,},
  ['Frost Hold']={[0]=10.0,},
  ['Frost Nova']={[0]=8.0,},
  ['Frost Protection ']={[0]=3600.0,},
  ['Frost Shock']={[0]=8.0,},
  ['Frost Shot']={[0]=10.0,},
  ['Frost Weakness']={[0]=45.0,},
  ['Frostbite']={[0]=5.0,},
  ['Frostbolt']={[1]=5.0,[2]=6.0,[3]=6.0,[4]=7.0,[5]=7.0,[6]=8.0,[7]=8.0,[8]=9.0,[9]=9.0,[10]=9.0,[11]=9.0,[0]=9.0,},
  ['Frostbolt Volley']={[0]=8.0,},
  ['Frostbrand Attack']={[0]=8.0,},
  ['Fumble']={[0]=30.0,},
  ['Fumble II']={[0]=30.0,},
  ['Fumble III']={[0]=30.0,},
  ['Fungal Bloom']={[0]=90.0,},
  ['Garrote']={[0]=18.0,},
  ['Gehennas\' Curse']={[0]=300.0,},
  ['Geyser']={[0]=5.0,},
  ['Ghostly Strike']={[0]=7.0,},
  ['Ghoul Plague']={[0]=1800.0,},
  ['Ghoul Rot']={[0]=600.0,},
  ['Gift of Arthas']={[0]=180.0,},
  ['Gizlock\'s Dummy Taunt Effect']={[0]=5.0,},
  ['Glacial Roar']={[0]=3.0,},
  ['Glimpse of Madness']={[0]=3.0,},
  ['Gnarlpine Vengeance']={[0]=6.0,},
  ['Gnomish Death Ray']={[0]=4.0,},
  ['Gnomish Mind Control Cap']={[0]=20.0,},
  ['Golemagg\'s Trust']={[0]=2.0,},
  ['Gore']={[0]=15.0,},
  ['Gouge']={[0]=4.0,},
  ['Grab Weapon']={[0]=15.0,},
  ['Grasping Vines']={[0]=10.0,},
  ['Greater Arcane Elixir']={[0]=1800.0,},
  ['Greater Polymorph']={[0]=20.0,},
  ['Grip of Command']={[0]=10.0,},
  ['Ground Smash']={[0]=3.0,},
  ['Ground Stomp']={[0]=5.0,},
  ['Ground Tremor']={[0]=2.0,},
  ['Growl']={[0]=3.0,},
  ['Gust of Wind']={[0]=4.0,},
  ['Gutgore Ripper']={[0]=30.0,},
  ['Hail Storm']={[0]=3.0,},
  ['Hallow\'s End Fright']={[0]=6.0,},
  ['Hammer of Justice']={[1]=3.0,[2]=4.0,[3]=5.0,[4]=6.0,[0]=6.0,},
  ['Hamstring']={[0]=15.0,},
  ['Hand Snap']={[0]=8.0,},
  ['Hand of Ragnaros']={[0]=2.0,},
  ['Hand of Thaurissan']={[0]=5.0,},
  ['Harass']={[1]=10.0,[2]=20.0,[3]=30.0,[0]=30.0,},
  ['Harsh Winds']={[0]=1.0,},
  ['Harvest Soul']={[0]=60.0,},
  ['Hate to Zero']={[0]=1.7,},
  ['Haunting Phantoms']={[0]=300.0,},
  ['Haunting Spirits']={[0]=300.0,},
  ['Head Butt']={[0]=2.0,},
  ['Head Crack']={[0]=20.0,},
  ['Head Smash']={[0]=2.0,},
  ['Health II']={[0]=3600.0,},
  ['Hemorrhage']={[0]=15.0,},
  ['Hex']={[0]=10.0,},
  ['Hex of Jammal\'an']={[0]=10.0,},
  ['Hex of Ravenclaw']={[0]=30.0,},
  ['Hex of Weakness']={[0]=120.0,},
  ['Hibernate']={[1]=20.0,[2]=30.0,[3]=40.0,[0]=40.0,},
  ['Highlord\'s Justice']={[0]=5.0,},
  ['Hive\'Zara Catalyst']={[0]=30.0,},
  ['Holy Blast']={[0]=4.0,},
  ['Holy Fire']={[0]=10.0,},
  ['Holy Protection ']={[0]=3600.0,},
  ['Holy Sunder']={[0]=60.0,},
  ['Holy Word: Fortitude']={[0]=1800.0,},
  ['Hooked Net']={[0]=10.0,},
  ['Howl of Terror']={[1]=10.0,[2]=15.0,[0]=15.0,},
  ['Howling Blade']={[0]=30.0,},
  ['Howling Rage']={[0]=300.0,},
  ['Hunter Epic Anti-Cheat DND']={[0]=60.0,},
  ['Hunter\'s Mark']={[0]=120.0,},
  ['Ice Blast']={[0]=10.0,},
  ['Ice Bolt Prot']={[0]=5.0,},
  ['Ice Claw']={[0]=6.0,},
  ['Ice Nova']={[0]=2.0,},
  ['Ice Tomb']={[0]=10.0,},
  ['Icebolt']={[0]=2.0,},
  ['Icicle']={[0]=10.0,},
  ['Icy Grasp']={[0]=5.0,},
  ['Ignite']={[0]=4.0,},
  ['Ignite Flesh']={[0]=60.0,},
  ['Ignite Mana']={[0]=300.0,},
  ['Immolate']={[0]=15.0,},
  ['Immolation Trap Effect']={[0]=15.0,},
  ['Immunity']={[0]=1800.0,},
  ['Impact']={[0]=2.0,},
  ['Impale']={[0]=9.0,},
  ['Impending Doom']={[0]=10.0,},
  ['Improved Concussive Shot']={[0]=3.0,},
  ['Improved Hamstring']={[0]=5.0,},
  ['Improved Scorpid Sting']={[0]=20.0,},
  ['Improved Wing Clip']={[0]=5.0,},
  ['Incapacitating Shout']={[0]=60.0,},
  ['Incinerate']={[0]=60.0,},
  ['Incite Flames']={[0]=60.0,},
  ['Inevitable Doom']={[0]=10.0,},
  ['Infected Bite']={[0]=180.0,},
  ['Infected Spine']={[0]=300.0,},
  ['Infected Wound']={[0]=300.0,},
  ['Inferno Effect']={[0]=2.0,},
  ['Inferno Shell']={[0]=10.0,},
  ['Ink Spray']={[0]=15.0,},
  ['Insect Swarm']={[0]=12.0,},
  ['Intellect IX']={[0]=3600.0,},
  ['Intercept Stun']={[0]=3.0,},
  ['Interrupt (PT)']={[0]=30.0,},
  ['Intimidating Growl']={[0]=5.0,},
  ['Intimidating Roar']={[0]=8.0,},
  ['Intimidating Shout']={[0]=8.0,},
  ['Intimidation']={[0]=3.0,},
  ['Intoxicating Venom']={[0]=120.0,},
  ['Involuntary Transformation']={[0]=30.0,},
  ['Irradiated']={[0]=60.0,},
  ['Itch']={[0]=8.0,},
  ['Jeff Dummy 1']={[0]=30.0,},
  ['Jeff Dummy 2']={[0]=30.0,},
  ['Judge\'s Gavel']={[0]=10.0,},
  ['Judgement of Justice']={[0]=10.0,},
  ['Judgement of Light']={[0]=10.0,},
  ['Judgement of Wisdom']={[0]=10.0,},
  ['Judgement of the Crusader']={[0]=10.0,},
  ['Keg Trap']={[0]=2.0,},
  ['Kick']={[0]=5.0,},
  ['Kick - Silenced']={[0]=2.0,},
  ['Kidney Shot']={[1]=0,[2]=1.0,},
  ['Knockdown']={[0]=2.0,},
  ['Knockout']={[0]=6.0,},
  ['Kodo Stomp']={[0]=3.0,},
  ['Lacerate']={[0]=8.0,},
  ['Lacerations']={[0]=60.0,},
  ['Lag']={[0]=10.0,},
  ['Larva Goo']={[0]=6.0,},
  ['Lash']={[0]=2.0,},
  ['Leech Poison']={[0]=40.0,},
  ['Lethal Toxin']={[0]=180.0,},
  ['Life Drain']={[0]=12.0,},
  ['Lightwell']={[0]=180.0,},
  ['Living Bomb']={[0]=8.0,},
  ['Localized Toxin']={[0]=60.0,},
  ['Locust Swarm']={[0]=6.0,},
  ['Long Daze']={[0]=6.0,},
  ['Low Swipe']={[0]=12.0,},
  ['Lucifron\'s Curse']={[0]=300.0,},
  ['Lunaclaw Spirit']={[0]=120.0,},
  ['Lunar Fortune']={[0]=1800.0,},
  ['Mace Stun Effect']={[0]=3.0,},
  ['Magenta Cap Sickness']={[0]=1200.0,},
  ['Maggot Goo']={[0]=6.0,},
  ['Maggot Slime']={[0]=1800.0,},
  ['Magic Reflection']={[0]=10.0,},
  ['Magma Shackles']={[0]=15.0,},
  ['Magma Spit']={[0]=30.0,},
  ['Magma Splash']={[0]=30.0,},
  ['Malown\'s Slam']={[0]=2.0,},
  ['Mana Burn']={[0]=8.0,},
  ['Mangle']={[0]=2.0,},
  ['Marduk\'s Curse']={[0]=5.0,},
  ['Mark of Arlokk']={[0]=120.0,},
  ['Mark of Blaumeux']={[0]=75.0,},
  ['Mark of Detonation']={[0]=30.0,},
  ['Mark of Flames']={[0]=120.0,},
  ['Mark of Frost']={[0]=12.0,},
  ['Mark of Kazzak']={[0]=60.0,},
  ['Mark of Korth\'azz']={[0]=75.0,},
  ['Mark of Mograine']={[0]=75.0,},
  ['Mark of Nature']={[0]=12.0,},
  ['Mark of Zeliek']={[0]=75.0,},
  ['Mark of the Wild']={[0]=1800.0,},
  ['Mass Healing']={[0]=12.0,},
  ['Massive Tremor']={[0]=2.0,},
  ['Maul']={[0]=2.0,},
  ['Melt Armor']={[0]=60.0,},
  ['Melt Ore']={[0]=20.0,},
  ['Mental Domination']={[0]=120.0,},
  ['Might of Shahram']={[0]=5.0,},
  ['Mind Control']={[0]=60.0,},
  ['Mind Flay']={[0]=3.0,},
  ['Mind Rot']={[0]=30.0,},
  ['Mind Shatter']={[0]=3.0,},
  ['Mind Soothe']={[0]=15.0,},
  ['Mind Tremor']={[0]=600.0,},
  ['Mind-numbing Poison']={[0]=10.0,},
  ['Mind-numbing Poison II']={[0]=12.0,},
  ['Mind-numbing Poison III']={[0]=14.0,},
  ['Minor Scorpion Venom Effect']={[0]=60.0,},
  ['Mirefin Fungus']={[0]=8.0,},
  ['Miring Mud']={[0]=5.0,},
  ['Mirkfallon Fungus']={[0]=2700.0,},
  ['Mobility Malfunction']={[0]=20.0,},
  ['Mocking Blow']={[0]=6.0,},
  ['Molten Metal']={[0]=15.0,},
  ['Moonfire']={[1]=9.0,[2]=12.0,[3]=12.0,[4]=12.0,[5]=12.0,[6]=12.0,[7]=12.0,[8]=12.0,[9]=12.0,[10]=12.0,[0]=12.0,},
  ['Mortal Cleave']={[0]=5.0,},
  ['Mortal Strike']={[0]=10.0,},
  ['Mortal Wound']={[0]=15.0,},
  ['Moss Covered Feet']={[0]=180.0,},
  ['Moss Covered Hands']={[0]=180.0,},
  ['Muculent Fever']={[0]=600.0,},
  ['Muscle Tear']={[0]=5.0,},
  ['Mutate Bug']={[0]=240.0,},
  ['Mutating Injection']={[0]=10.0,},
  ['Naralex\'s Nightmare']={[0]=15.0,},
  ['Naraxis Web']={[0]=30.0,},
  ['Nature Protection ']={[0]=3600.0,},
  ['Nature Weakness']={[0]=45.0,},
  ['Necrotic Poison']={[0]=30.0,},
  ['Net']={[0]=10.0,},
  ['Net Guard']={[0]=20.0,},
  ['Net-o-Matic']={[0]=10.0,},
  ['Noxious Catalyst']={[0]=120.0,},
  ['Nullify']={[0]=8.0,},
  ['Numbing Pain']={[0]=10.0,},
  ['Oops!']={[0]=10.0,},
  ['Open Wound Effect']={[0]=8.0,},
  ['Overseer\'s Poison']={[0]=60.0,},
  ['Overwhelming Stench']={[0]=6.0,},
  ['Pacify']={[0]=10.0,},
  ['Panic']={[0]=8.0,},
  ['Paralyze']={[0]=30.0,},
  ['Paralyzing Poison']={[0]=8.0,},
  ['Parasite']={[0]=75.0,},
  ['Parasitic Serpent']={[0]=10.0,},
  ['Pester Effect']={[0]=3.0,},
  ['Petrify']={[0]=8.0,},
  ['Phantom Strike']={[0]=20.0,},
  ['Pierce Ankle']={[0]=3.0,},
  ['Pierce Armor']={[0]=20.0,},
  ['Piercing Howl']={[0]=6.0,},
  ['Piercing Shadow']={[0]=1800.0,},
  ['Piercing Shot']={[0]=15.0,},
  ['Piercing Shriek']={[0]=6.0,},
  ['Plague']={[0]=40.0,},
  ['Plague Cloud']={[0]=240.0,},
  ['Plague Mind']={[0]=600.0,},
  ['Plague Mist']={[0]=8.0,},
  ['Planted']={[0]=3.0,},
  ['Poison']={[0]=30.0,},
  ['Poison Aura']={[0]=12.0,},
  ['Poison Bolt']={[0]=10.0,},
  ['Poison Bolt Volley']={[0]=10.0,},
  ['Poison Charge']={[0]=9.0,},
  ['Poison Cloud']={[0]=45.0,},
  ['Poison Mind']={[0]=15.0,},
  ['Poison Stinger']={[0]=10.0,},
  ['Poisoned Harpoon']={[0]=60.0,},
  ['Poisoned Shot']={[0]=75.0,},
  ['Poisonous Spit']={[0]=15.0,},
  ['Poisonous Stab']={[0]=15.0,},
  ['Polymorph']={[1]=20.0,[2]=30.0,[3]=40.0,[4]=50.0,[0]=50.0,},
  ['Polymorph Backfire']={[0]=8.0,},
  ['Polymorph: Chicken']={[0]=10.0,},
  ['Polymorph: Cow']={[0]=50.0,},
  ['Polymorph: Pig']={[0]=50.0,},
  ['Polymorph: Sheep']={[0]=10.0,},
  ['Polymorph: Turtle']={[0]=50.0,},
  ['Polymorphic Ray']={[0]=4.0,},
  ['Possess']={[0]=120.0,},
  ['Pounce']={[0]=2.0,},
  ['Pounce Bleed']={[0]=18.0,},
  ['Premeditation']={[0]=10.0,},
  ['Psychic Scream']={[0]=8.0,},
  ['Pummel']={[0]=4.0,},
  ['Puncture']={[0]=10.0,},
  ['Puncture Armor']={[0]=30.0,},
  ['Purge']={[0]=2.0,},
  ['Putrid Bile']={[0]=45.0,},
  ['Putrid Bite']={[0]=30.0,},
  ['Putrid Breath']={[0]=30.0,},
  ['Putrid Enzyme']={[0]=300.0,},
  ['Putrid Stench']={[0]=10.0,},
  ['Pyroblast']={[0]=12.0,},
  ['Pyroclasm']={[0]=3.0,},
  ['Rabid Maw']={[0]=30.0,},
  ['Rabies']={[0]=600.0,},
  ['Radiation Poisoning']={[0]=25.0,},
  ['Rage of Thule']={[0]=120.0,},
  ['Rake']={[0]=9.0,},
  ['Rallying Cry of the Dragonslayer']={[0]=7200.0,},
  ['Rampage']={[0]=2.5,},
  ['Rat Nova']={[0]=10.0,},
  ['Ravage']={[0]=2.0,},
  ['Reckless Charge']={[0]=30.0,},
  ['Reflection Field']={[0]=5.0,},
  ['Regeneration IV']={[0]=3600.0,},
  ['Rend']={[1]=9.0,[2]=12.0,[3]=15.0,[4]=18.0,[5]=21.0,[6]=21.0,[7]=21.0,[0]=21.0,},
  ['Rend Flesh']={[0]=12.0,},
  ['Repentance']={[0]=6.0,},
  ['Repulsive Gaze']={[0]=8.0,},
  ['Retching Plague']={[0]=300.0,},
  ['Revenge Stun']={[0]=3.0,},
  ['Rhahk\'Zor Slam']={[0]=3.0,},
  ['Rift Beacon']={[0]=60.0,},
  ['Righteous Fire']={[0]=8.0,},
  ['Rip']={[0]=12.0,},
  ['Riposte']={[0]=6.0,},
  ['Riptide']={[0]=4.0,},
  ['Ritual Candle Aura']={[0]=6.0,},
  ['Rupture']={[0]=6.0,},
  ['Sacrifice']={[0]=8.0,},
  ['Sap']={[1]=25.0,[2]=35.0,[3]=45.0,[0]=45.0,},
  ['Sap Might']={[0]=300.0,},
  ['Savage Assault']={[0]=30.0,},
  ['Savage Assault II']={[0]=30.0,},
  ['Savage Assault III']={[0]=30.0,},
  ['Savage Assault IV']={[0]=30.0,},
  ['Savage Assault V']={[0]=30.0,},
  ['Savage Pummel']={[0]=5.0,},
  ['Scald']={[0]=4.0,},
  ['Scare Beast']={[1]=10.0,[2]=15.0,[3]=20.0,[0]=20.0,},
  ['Scatter Shot']={[0]=4.0,},
  ['Scorpid Poison']={[0]=10.0,},
  ['Scorpid Sting']={[0]=20.0,},
  ['Screams of the Past']={[0]=5.0,},
  ['Screech']={[0]=4.0,},
  ['Searing Blast']={[0]=30.0,},
  ['Searing Flames']={[0]=9.0,},
  ['Seduction']={[0]=15.0,},
  ['Seeping Willow']={[0]=30.0,},
  ['Serious Wound']={[0]=10.0,},
  ['Serpent Sting']={[0]=15.0,},
  ['Serrated Bite']={[0]=30.0,},
  ['Shackle Undead']={[1]=30.0,[2]=40.0,[3]=50.0,[0]=50.0,},
  ['Shadow Bolt']={[0]=6.0,},
  ['Shadow Command']={[0]=15.0,},
  ['Shadow Flame']={[0]=10.0,},
  ['Shadow Mark']={[0]=15.0,},
  ['Shadow Protection ']={[0]=3600.0,},
  ['Shadow Vulnerability']={[0]=15.0,},
  ['Shadow Weakness']={[0]=45.0,},
  ['Shadow Word: Pain']={[0]=18.0,},
  ['Shadow of Ebonroc']={[0]=8.0,},
  ['Shadowburn']={[0]=5.0,},
  ['Shadowstalker Slash']={[0]=5.0,},
  ['Shadowstalker Stab']={[0]=5.0,},
  ['Shared Bonds']={[0]=4.0,},
  ['Shazzrah\'s Curse']={[0]=300.0,},
  ['Shield Bash']={[0]=6.0,},
  ['Shield Bash - Silenced']={[0]=3.0,},
  ['Shield Slam']={[0]=2.0,},
  ['Shockwave']={[0]=2.0,},
  ['Shred']={[0]=12.0,},
  ['Shrink']={[0]=120.0,},
  ['Shrink Ray']={[0]=20.0,},
  ['Silence']={[0]=5.0,},
  ['Silithid Pox']={[0]=1800.0,},
  ['Siphon Blessing']={[0]=30.0,},
  ['Siphon Health']={[0]=15.0,},
  ['Siphon Life']={[0]=30.0,},
  ['Skull Crack']={[0]=2.0,},
  ['Skullforge Brand']={[0]=30.0,},
  ['Slam']={[0]=2.0,},
  ['Slavedriver\'s Cane']={[0]=30.0,},
  ['Sleep']={[1]=20.0,[2]=30.0,[0]=30.0,},
  ['Sleepwalk']={[0]=10.0,},
  ['Slice and Dice']={[0]=6.0,},
  ['Slime Bolt']={[0]=6.0,},
  ['Slime Burst']={[0]=5.0,},
  ['Slime Dysentery']={[0]=1800.0,},
  ['Slime Stream']={[0]=3.0,},
  ['Sling Dirt']={[0]=10.0,},
  ['Sling Mud']={[0]=15.0,},
  ['Sloth Effect']={[0]=3.0,},
  ['Slow']={[1]=10.0,[2]=15.0,[0]=15.0,},
  ['Slow Poison']={[0]=30.0,},
  ['Slow Poison II']={[0]=30.0,},
  ['Slowing Ooze']={[0]=10.0,},
  ['Slowing Poison']={[0]=25.0,},
  ['Sludge']={[0]=3.0,},
  ['Sludge Toxin']={[0]=45.0,},
  ['Smite Demon']={[0]=5.0,},
  ['Smite Slam']={[0]=3.0,},
  ['Smite Stomp']={[0]=10.0,},
  ['Smoke Bomb']={[0]=4.0,},
  ['Smoke Cloud']={[0]=3.0,},
  ['Smother']={[0]=6.0,},
  ['Snap Kick']={[0]=2.0,},
  ['Sol L']={[0]=5.0,},
  ['Sonic Burst']={[0]=10.0,},
  ['Soot Covering']={[0]=10.0,},
  ['Soothe Animal']={[0]=15.0,},
  ['Soul Breaker']={[0]=30.0,},
  ['Soul Burn']={[0]=16.0,},
  ['Soul Corruption']={[0]=15.0,},
  ['Soul Drain']={[0]=10.0,},
  ['Soul Revival']={[0]=1800.0,},
  ['Soul Siphon']={[0]=10.0,},
  ['Soul Tap']={[0]=12.0,},
  ['Spell Lock']={[1]=6.0,[2]=8.0,[0]=8.0,},
  ['Spell Vulnerability']={[0]=5.0,},
  ['Spider Poison']={[0]=30.0,},
  ['Spider Web']={[0]=1.0,},
  ['Spider\'s Kiss']={[0]=10.0,},
  ['Spirit Decay']={[0]=1200.0,},
  ['Spirit of Zandalar']={[0]=7200.0,},
  ['Spitelash']={[0]=20.0,},
  ['Starfire Stun']={[0]=3.0,},
  ['Starshards']={[0]=6.0,},
  ['Stasis Effect']={[0]=15.0,},
  ['Stasis Test']={[0]=5.0,},
  ['Static Conduit']={[0]=15.0,},
  ['Steam Jet']={[0]=10.0,},
  ['Sticky Tar']={[0]=4.0,},
  ['Stink Trap']={[0]=120.0,},
  ['Stomp']={[0]=10.0,},
  ['Storm Bolt']={[0]=8.0,},
  ['Stormbolt']={[0]=5.0,},
  ['Stormstrike']={[0]=12.0,},
  ['Strong Cleave']={[0]=10.0,},
  ['Stun']={[0]=2.0,},
  ['Stunning Blast']={[1]=3.0,[2]=5.0,[0]=5.0,},
  ['Stunning Blow']={[1]=5.0,[2]=8.0,[0]=8.0,},
  ['Stunning Strike']={[0]=4.0,},
  ['Stygian Grasp']={[0]=5.0,},
  ['Submersion']={[0]=60.0,},
  ['Sul\'thraze']={[0]=15.0,},
  ['Summon Darrowshire Spirit']={[0]=60.0,},
  ['Summon Finkle Generator']={[0]=5.0,},
  ['Summon Freed Soul']={[0]=60.0,},
  ['Summon Healed Celebrian Vine']={[0]=604800.0,},
  ['Summon Isalien DND']={[0]=600.0,},
  ['Summon Jarien and Sothos DND']={[0]=600.0,},
  ['Summon Knight']={[0]=180.0,},
  ['Summon Kormok DND']={[0]=600.0,},
  ['Summon Lord Valthalak DND']={[0]=600.0,},
  ['Summon Marduk the Black']={[0]=300.0,},
  ['Summon Mor Grayhoof DND']={[0]=600.0,},
  ['Summon Mounted Knight']={[0]=120.0,},
  ['Summon Screecher Spirit']={[0]=120.0,},
  ['Summon Thelrin DND']={[0]=600.0,},
  ['Summon Trainee']={[0]=230.0,},
  ['Summon Various DND']={[0]=600.0,},
  ['Sunder Armor']={[0]=30.0,},
  ['Sundering Cleave']={[0]=30.0,},
  ['Super Shrink Ray']={[0]=20.0,},
  ['Surprise Attack']={[0]=2.5,},
  ['Survival Instinct']={[0]=2.0,},
  ['TWEEP']={[0]=11.0,},
  ['Taelan\'s Suffering Effect']={[0]=2.0,},
  ['Tail Lash']={[0]=2.0,},
  ['Tainted Blood Effect']={[0]=10.0,},
  ['Tainted Mind']={[0]=600.0,},
  ['Tame Adult Plainstrider']={[0]=900.0,},
  ['Tame Armored Scorpid']={[0]=900.0,},
  ['Tame Beast']={[0]=20.0,},
  ['Tame Dire Mottled Boar']={[0]=900.0,},
  ['Tame Ice Claw Bear']={[0]=20.0,},
  ['Tame Large Crag Boar']={[0]=20.0,},
  ['Tame Nightsaber Stalker']={[0]=900.0,},
  ['Tame Prairie Stalker']={[0]=900.0,},
  ['Tame Snow Leopard']={[0]=900.0,},
  ['Tame Strigid Screecher']={[0]=900.0,},
  ['Tame Surf Crawler']={[0]=900.0,},
  ['Tame Swoop']={[0]=900.0,},
  ['Tame Webwood Lurker']={[0]=900.0,},
  ['Target Dummy Spawn Effect']={[0]=5.0,},
  ['Taunt']={[0]=3.0,},
  ['Tendon Rip']={[0]=8.0,},
  ['Tendon Slice']={[0]=8.0,},
  ['Tendrils of Air']={[0]=2.0,},
  ['Terrify']={[0]=4.0,},
  ['Terrifying Howl']={[0]=3.0,},
  ['Terrifying Roar']={[0]=5.0,},
  ['Terrifying Screech']={[0]=4.0,},
  ['Test Curse of Agony']={[0]=24.0,},
  ['Test Rip']={[0]=12.0,},
  ['Test Strike W35']={[0]=10.0,},
  ['Test Strike W50']={[0]=10.0,},
  ['Tetanus']={[0]=1200.0,},
  ['Thorn Volley']={[0]=2.0,},
  ['Thorns']={[0]=600.0,},
  ['Threatening Gaze']={[0]=6.0,},
  ['Threatening Growl']={[0]=30.0,},
  ['Throw Axe']={[0]=2.0,},
  ['Thunder Clap']={[1]=10.0,[2]=14.0,[3]=18.0,[4]=22.0,[5]=26.0,[6]=30.0,[0]=30.0,},
  ['Thunderclap']={[0]=10.0,},
  ['Thundercrack']={[0]=2.5,},
  ['Thunderfury']={[0]=12.0,},
  ['Thundershock']={[0]=5.0,},
  ['Tidal Charm']={[0]=3.0,},
  ['Tight Pinch']={[0]=5.0,},
  ['Time Lapse']={[0]=8.0,},
  ['Torch Burst']={[0]=30.0,},
  ['Torch Toss']={[0]=30.0,},
  ['Tornado']={[0]=4.0,},
  ['Touch of Ravenclaw']={[0]=5.0,},
  ['Touch of Weakness']={[0]=120.0,},
  ['Toxic Contagion']={[0]=60.0,},
  ['Toxic Saliva']={[0]=120.0,},
  ['Toxic Volley']={[0]=15.0,},
  ['Tranquilizing Poison']={[0]=8.0,},
  ['Trap']={[0]=10.0,},
  ['Trelane\'s Freezing Touch']={[0]=12.0,},
  ['Trip']={[0]=3.0,},
  ['True Fulfillment']={[0]=20.0,},
  ['Tunneler Acid']={[0]=30.0,},
  ['Turn Undead']={[1]=10.0,[2]=15.0,[3]=20.0,[0]=20.0,},
  ['Twisted Reflection']={[0]=45.0,},
  ['Unbalancing Strike']={[0]=6.0,},
  ['Unholy Curse']={[0]=12.0,},
  ['Vampiric Embrace']={[0]=60.0,},
  ['Vanish']={[0]=20.0,},
  ['Veil of Darkness']={[0]=7.0,},
  ['Veil of Shadow']={[0]=15.0,},
  ['Vekniss Catalyst']={[0]=30.0,},
  ['Vengeance']={[0]=600.0,},
  ['Venom Spit']={[0]=10.0,},
  ['Venom Sting']={[0]=45.0,},
  ['Venomhide Poison']={[0]=30.0,},
  ['Vicious Rend']={[0]=15.0,},
  ['Vindication']={[0]=10.0,},
  ['Viper Sting']={[0]=8.0,},
  ['Virulent Poison']={[0]=30.0,},
  ['Vital Wound']={[0]=8.0,},
  ['Void Bolt']={[0]=10.0,},
  ['Volatile Infection']={[0]=120.0,},
  ['Voodoo Hex']={[0]=120.0,},
  ['Vulnerable']={[0]=3.0,},
  ['Wail of Nightlash']={[0]=15.0,},
  ['Wail of the Banshee']={[0]=12.0,},
  ['Wailing Dead']={[0]=6.0,},
  ['Wandering Plague']={[0]=300.0,},
  ['War Stomp']={[0]=2.0,},
  ['Warchief\'s Blessing']={[0]=3600.0,},
  ['Ward of Laze effect']={[0]=3.0,},
  ['Warlock Terror']={[0]=2.0,},
  ['Water Breathing']={[0]=600.0,},
  ['Wavering Will']={[0]=60.0,},
  ['Weak Poison']={[0]=12.0,},
  ['Weakening Disease']={[0]=30.0,},
  ['Web']={[0]=8.0,},
  ['Web Explosion']={[0]=10.0,},
  ['Web II']={[0]=10.0,},
  ['Web III']={[0]=12.0,},
  ['Web Spin']={[0]=7.0,},
  ['Web Spray']={[0]=10.0,},
  ['Whipweed Entangle']={[0]=18.0,},
  ['Whipweed Roots']={[0]=15.0,},
  ['Whirling Trip']={[0]=2.0,},
  ['Whirlwind']={[0]=2.0,},
  ['Whisperings of C\'Thun']={[0]=20.0,},
  ['Widow Bite']={[0]=4.0,},
  ['Widow\'s Embrace']={[0]=30.0,},
  ['Wild Magic']={[0]=30.0,},
  ['Wild Polymorph']={[0]=20.0,},
  ['Will of Hakkar']={[0]=20.0,},
  ['Windreaper']={[0]=20.0,},
  ['Windsor Dismisses Horse DND']={[0]=55.0,},
  ['Wing Clip']={[0]=10.0,},
  ['Wings of Despair']={[0]=6.0,},
  ['Winter\'s Chill']={[0]=15.0,},
  ['Wither']={[0]=21.0,},
  ['Wither Strike']={[0]=8.0,},
  ['Wither Touch']={[0]=120.0,},
  ['Withered Touch']={[0]=180.0,},
  ['Withering Heat']={[0]=900.0,},
  ['Withering Poison']={[0]=180.0,},
  ['Wound']={[0]=25.0,},
  ['Wound Poison']={[0]=15.0,},
  ['Wracking Pains']={[0]=180.0,},
  ['Wrath of the Plaguebringer']={[0]=10.0,},
  ['Wyvern Sting']={[0]=12.0,},
  ['[PH] Crystal Corpse Timer']={[0]=7200.0,},
  ['scaler test']={[0]=10.0,},
}

-- custom entries not detected by DBC extractor
pfUI_locale["enUS"]["debuffs"]['Cone of Cold']={[0]=8.0,} -- Cone of Cold

pfUI_locale["enUS"]["totems"] = {
  ["Disease Cleansing Totem"] = "spell_nature_diseasecleansingtotem",
  ["Earth Elemental Totem"] = "spell_nature_earthelemental_totem",
  ["Earthbind Totem"] = "spell_nature_strengthofearthtotem02",
  ["Fire Elemental Totem"] = "spell_fire_elemental_totem",
  ["Fire Nova Totem"] = "spell_fire_sealoffire",
  ["Fire Resistance Totem"] = "spell_fireresistancetotem_01",
  ["Flametongue Totem"] = "spell_nature_guardianward",
  ["Frost Resistance Totem"] = "spell_frostresistancetotem_01",
  ["Grace of Air Totem"] = "spell_nature_invisibilitytotem",
  ["Grounding Totem"] = "spell_nature_groundingtotem",
  ["Healing Stream Totem"] = "Inv_spear_04",
  ["Magma Totem"] = "spell_fire_selfdestruct",
  ["Mana Spring Totem"] = "spell_nature_manaregentotem",
  ["Mana Tide Totem"] = "spell_frost_summonwaterelemental",
  ["Nature Resistance Totem"] = "spell_nature_natureresistancetotem",
  ["Poison Cleansing Totem"] = "spell_nature_poisoncleansingtotem",
  ["Searing Totem"] = "spell_fire_searingtotem",
  ["Sentry Totem"] = "spell_nature_removecurse",
  ["Stoneclaw Totem"] = "spell_nature_stoneclawtotem",
  ["Stoneskin Totem"] = "spell_nature_stoneskintotem",
  ["Strength of Earth Totem"] = "spell_nature_earthbindtotem",
  ["Totem of Wrath"] = "spell_fire_totemofwrath",
  ["Tremor Totem"] = "spell_nature_tremortotem",
  ["Windfury Totem"] = "spell_nature_windfury",
  ["Windwall Totem"] = "spell_nature_earthbind",
  ["Wrath of Air Totem"] = "spell_nature_slowingtotem",
}

pfUI_locale["enUS"]["icons"] = {
  ["Abolish Disease"] = "Spell_Nature_NullifyDisease",
  ["Abolish Poison"] = "Spell_Nature_NullifyPoison_02",
  ["Abolish Poison Effect"] = "Spell_Nature_NullifyPoison_02",
  ["Activate MG Turret"] = "INV_Weapon_Rifle_10",
  ["Adrenaline Rush"] = "Spell_Shadow_ShadowWordDominate",
  ["Aftermath"] = "Spell_Fire_Fire",
  ["Aggression"] = "Ability_Racial_Avatar",
  ["Agonizing Flames"] = "Spell_Fire_BlueImmolation",
  ["Aimed Shot"] = "INV_Spear_07",
  ["Alchemy"] = "Trade_Alchemy",
  ["Ambush"] = "Ability_Rogue_Ambush",
  ["Amplify Curse"] = "Spell_Shadow_Contagion",
  ["Amplify Magic"] = "Spell_Holy_FlashHeal",
  ["Ancestral Fortitude"] = "Spell_Nature_UndyingStrength",
  ["Ancestral Healing"] = "Spell_Nature_UndyingStrength",
  ["Ancestral Knowledge"] = "Spell_Shadow_GrimWard",
  ["Ancestral Spirit"] = "Spell_Nature_Regenerate",
  ["Ancient Hysteria"] = "Spell_Shadow_UnholyFrenzy",
  ["Anesthetic Poison"] = "Spell_Nature_SlowPoison",
  ["Anger Management"] = "Spell_Holy_BlessingOfStamina",
  ["Anguish"] = "Spell_Shadow_GatherShadows",
  ["Anticipation"] = "Spell_Nature_MirrorImage",
  ["Aquatic Form"] = "Ability_Druid_AquaticForm",
  ["Arcane Blast"] = "Spell_Arcane_Blast",
  ["Arcane Brilliance"] = "Spell_Holy_ArcaneIntellect",
  ["Arcane Concentration"] = "Spell_Shadow_ManaBurn",
  ["Arcane Energy"] = "Spell_Holy_MindVision",
  ["Arcane Explosion"] = "Spell_Nature_WispSplode",
  ["Arcane Focus"] = "Spell_Holy_Devotion",
  ["Arcane Fortitude"] = "Spell_Arcane_ArcaneResilience",
  ["Arcane Impact"] = "Spell_Nature_WispSplode",
  ["Arcane Instability"] = "Spell_Shadow_Teleport",
  ["Arcane Intellect"] = "Spell_Holy_MagicalSentry",
  ["Arcane Meditation"] = "Spell_Shadow_SiphonMana",
  ["Arcane Mind"] = "Spell_Shadow_Charm",
  ["Arcane Missiles"] = "Spell_Nature_StarFall",
  ["Arcane Potency"] = "Spell_Arcane_ArcanePotency",
  ["Arcane Power"] = "Spell_Nature_Lightning",
  ["Arcane Resistance"] = "Spell_Nature_StarFall",
  ["Arcane Shot"] = "Ability_ImpalingBolt",
  ["Arcane Shroud"] = "Spell_Magic_LesserInvisibilty",
  ["Arcane Subtlety"] = "Spell_Holy_DispelMagic",
  ["Arcane Torrent"] = "Spell_Shadow_Teleport",
  ["Arcane Weakness"] = "Spell_Shadow_SoulLeech_2",
  ["Arctic Reach"] = "Spell_Shadow_DarkRitual",
  ["Arctic Winds"] = "Spell_Frost_ArcticWinds",
  ["Armor of Faith"] = "Spell_Holy_BlessingOfProtection",
  ["Armorsmith"] = "Trade_BlackSmithing",
  ["Aspect of the Beast"] = "Ability_Mount_PinkTiger",
  ["Aspect of the Cheetah"] = "Ability_Mount_JungleTiger",
  ["Aspect of the Hawk"] = "Spell_Nature_RavenForm",
  ["Aspect of the Monkey"] = "Ability_Hunter_AspectOfTheMonkey",
  ["Aspect of the Pack"] = "Ability_Mount_WhiteTiger",
  ["Aspect of the Viper"] = "Ability_Hunter_AspectoftheViper",
  ["Aspect of the Wild"] = "Spell_Nature_ProtectionformNature",
  ["Astral Recall"] = "Spell_Nature_AstralRecal",
  ["Attack"] = "Temp",
  ["Attacking"] = "Temp",
  ["Auto Shot"] = "Ability_Whirlwind",
  ["Avenger's Shield"] = "Spell_Holy_AvengersShield",
  ["Avenging Wrath"] = "Spell_Holy_AvengineWrath",
  ["Avoidance"] = "Spell_Magic_LesserInvisibilty",
  ["Axe Specialization"] = "INV_Axe_06",
  ["Backlash"] = "Spell_Fire_PlayingWithFire",
  ["Backstab"] = "Ability_BackStab",
  ["Badge of the Swarmguard"] = "INV_Misc_AhnQirajTrinket_04",
  ["Balance of Power"] = "Ability_Druid_BalanceofPower",
  ["Bane"] = "Spell_Shadow_DeathPact",
  ["Banish"] = "Spell_Shadow_Cripple",
  ["Banshee Curse"] = "Spell_Nature_Drowsy",
  ["Barkskin"] = "Spell_Nature_StoneClawTotem",
  ["Barrage"] = "Ability_UpgradeMoonGlaive",
  ["Bash"] = "Ability_Druid_Bash",
  ["Basic Campfire"] = "Spell_Fire_Fire",
  ["Battle Rush"] = "Ability_Whirlwind",
  ["Battle Shout"] = "Ability_Warrior_BattleShout",
  ["Battle Stance"] = "Ability_Warrior_OffensiveStance",
  ["Battle Stance Passive"] = "Ability_Warrior_OffensiveStance",
  ["Bear Form"] = "Ability_Racial_BearForm",
  ["Beast Lore"] = "Ability_Physical_Taunt",
  ["Beast Slaying"] = "INV_Misc_Pelt_Bear_Ruin_02",
  ["Beast Training"] = "Ability_Hunter_BeastCall02",
  ["Benediction"] = "Spell_Frost_WindWalkOn",
  ["Berserker Rage"] = "Spell_Nature_AncestralGuardian",
  ["Berserker Stance"] = "Ability_Racial_Avatar",
  ["Berserker Stance Passive"] = "Ability_Racial_Avatar",
  ["Berserking"] = "Racial_Troll_Berserk",
  ["Bestial Discipline"] = "Spell_Nature_AbolishMagic",
  ["Bestial Swiftness"] = "Ability_Druid_Dash",
  ["Bestial Wrath"] = "Ability_Druid_FerociousBite",
  ["Binding Heal"] = "Spell_Holy_BlindingHeal",
  ["Bite"] = "Ability_Racial_Cannibalize",
  ["Black Arrow"] = "Ability_TheBlackArrow",
  ["Blackout"] = "Spell_Shadow_GatherShadows",
  ["Blacksmithing"] = "Trade_BlackSmithing",
  ["Blade Flurry"] = "Ability_Warrior_PunishingBlow",
  ["Blade Turning"] = "Ability_Warrior_Challange",
  ["Blade Twisting"] = "Ability_Rogue_BladeTwisting",
  ["Blast Wave"] = "Spell_Holy_Excorcism_02",
  ["Blazing Speed"] = "Spell_Fire_BurningSpeed",
  ["Blessed Recovery"] = "Spell_Holy_BlessedRecovery",
  ["Blessed Resilience"] = "Spell_Holy_BlessedResillience",
  ["Blessing of Auchindoun"] = "INV_Battery_02",
  ["Blessing of Freedom"] = "Spell_Holy_SealOfValor",
  ["Blessing of Kings"] = "Spell_Magic_MageArmor",
  ["Blessing of Light"] = "Spell_Holy_PrayerOfHealing02",
  ["Blessing of Might"] = "Spell_Holy_FistOfJustice",
  ["Blessing of Protection"] = "Spell_Holy_SealOfProtection",
  ["Blessing of Sacrifice"] = "Spell_Holy_SealOfSacrifice",
  ["Blessing of Salvation"] = "Spell_Holy_SealOfSalvation",
  ["Blessing of Sanctuary"] = "Spell_Nature_LightningShield",
  ["Blessing of Wisdom"] = "Spell_Holy_SealOfWisdom",
  ["Blind"] = "Spell_Shadow_MindSteal",
  ["Blinding Powder"] = "INV_Misc_Ammo_Gunpowder_02",
  ["Blink"] = "Spell_Arcane_Blink",
  ["Blizzard"] = "Spell_Frost_IceStorm",
  ["Block"] = "Ability_Defend",
  ["Blood Craze"] = "Spell_Shadow_SummonImp",
  ["Blood Frenzy"] = "Ability_Warrior_BloodFrenzy",
  ["Blood Fury"] = "Racial_Orc_BerserkerStrength",
  ["Blood Pact"] = "Spell_Shadow_BloodBoil",
  ["Bloodlust"] = "Spell_Nature_BloodLust",
  ["Bloodrage"] = "Ability_Racial_BloodRage",
  ["Bloodthirst"] = "Spell_Nature_BloodLust",
  ["Boar Charge"] = "Spell_Shadow_VampiricAura",
  ["Booming Voice"] = "Spell_Nature_Purge",
  ["Bow Specialization"] = "INV_Weapon_Bow_12",
  ["Bows"] = "INV_Weapon_Bow_05",
  ["Bright Campfire"] = "Spell_Fire_Fire",
  ["Brittle Armor"] = "Spell_Shadow_GrimWard",
  ["Brutal Impact"] = "Ability_Druid_Bash",
  ["Burning Adrenaline"] = "INV_Gauntlets_03",
  ["Burning Soul"] = "Spell_Fire_Fire",
  ["Burning Wish"] = "Spell_Shadow_PsychicScream",
  ["Call Pet"] = "Ability_Hunter_BeastCall",
  ["Call of Flame"] = "Spell_Fire_Immolation",
  ["Call of Thunder"] = "Spell_Nature_CallStorm",
  ["Call of the Nexus"] = "Spell_Holy_MindVision",
  ["Camouflage"] = "Ability_Stealth",
  ["Cannibalize"] = "Ability_Racial_Cannibalize",
  ["Cat Form"] = "Ability_Druid_CatForm",
  ["Cataclysm"] = "Spell_Fire_WindsofWoe",
  ["Celestial Focus"] = "Spell_Arcane_StarFire",
  ["Chain Heal"] = "Spell_Nature_HealingWaveGreater",
  ["Chain Lightning"] = "Spell_Nature_ChainLightning",
  ["Challenging Roar"] = "Ability_Druid_ChallangingRoar",
  ["Challenging Shout"] = "Ability_BullRush",
  ["Charge"] = "Ability_Warrior_Charge",
  ["Charge Rage Bonus Effect"] = "Ability_Warrior_Charge",
  ["Charge Stun"] = "Spell_Frost_Stun",
  ["Chastise"] = "Spell_Holy_Chastise",
  ["Cheap Shot"] = "Ability_CheapShot",
  ["Chilled"] = "Spell_Frost_IceStorm",
  ["Circle of Healing"] = "Spell_Holy_CircleOfRenewal",
  ["Claw"] = "Ability_Druid_Rake",
  ["Cleanse"] = "Spell_Holy_Renew",
  ["Clearcasting"] = "Spell_Shadow_ManaBurn",
  ["Cleave"] = "Ability_Warrior_Cleave",
  ["Clever Traps"] = "Spell_Nature_TimeStop",
  ["Cloak of Shadows"] = "Spell_Shadow_NetherCloak",
  ["Closing"] = "Temp",
  ["Cloth"] = "INV_Chest_Cloth_21",
  ["Coarse Sharpening Stone"] = "INV_Stone_SharpeningStone_02",
  ["Cobra Reflexes"] = "Spell_Nature_GuardianWard",
  ["Cold Blood"] = "Spell_Ice_Lament",
  ["Cold Snap"] = "Spell_Frost_WizardMark",
  ["Combat Endurance"] = "Spell_Nature_AncestralGuardian",
  ["Combustion"] = "Spell_Fire_SealOfFire",
  ["Command"] = "Ability_Warrior_WarCry",
  ["Commanding Presence"] = "Ability_Warrior_BattleShout",
  ["Commanding Shout"] = "Ability_Warrior_RallyingCry",
  ["Concentration Aura"] = "Spell_Holy_MindSooth",
  ["Concussion"] = "Spell_Fire_Fireball",
  ["Concussion Blow"] = "Ability_ThunderBolt",
  ["Concussive Barrage"] = "Spell_Arcane_StarFire",
  ["Concussive Shot"] = "Spell_Frost_Stun",
  ["Cone of Cold"] = "Spell_Frost_Glacier",
  ["Conflagrate"] = "Spell_Fire_Fireball",
  ["Conflagration"] = "Spell_Fire_Incinerate",
  ["Conjure Food"] = "INV_Misc_Food_10",
  ["Conjure Mana Agate"] = "INV_Misc_Gem_Emerald_01",
  ["Conjure Mana Citrine"] = "INV_Misc_Gem_Opal_01",
  ["Conjure Mana Emerald"] = "INV_Misc_Gem_Stone_01",
  ["Conjure Mana Jade"] = "INV_Misc_Gem_Emerald_02",
  ["Conjure Mana Ruby"] = "INV_Misc_Gem_Ruby_01",
  ["Conjure Water"] = "INV_Drink_06",
  ["Consecration"] = "Spell_Holy_InnerFire",
  ["Consume Magic"] = "Spell_Arcane_StudentOfMagic",
  ["Consume Shadows"] = "Spell_Shadow_AntiShadow",
  ["Contagion"] = "Spell_Shadow_PainfulAfflictions",
  ["Convection"] = "Spell_Nature_WispSplode",
  ["Conviction"] = "Spell_Holy_RetributionAura",
  ["Cooking"] = "INV_Misc_Food_15",
  ["Corruption"] = "Spell_Shadow_AbominationExplosion",
  ["Counterattack"] = "Ability_Warrior_Challange",
  ["Counterspell"] = "Spell_Frost_IceShock",
  ["Counterspell - Silenced"] = "Spell_Frost_IceShock",
  ["Cower"] = "Ability_Druid_Cower",
  ["Create Firestone"] = "INV_Ammo_FireTar",
  ["Create Firestone (Greater)"] = "INV_Ammo_FireTar",
  ["Create Firestone (Lesser)"] = "INV_Ammo_FireTar",
  ["Create Firestone (Major)"] = "INV_Ammo_FireTar",
  ["Create Healthstone"] = "INV_Stone_04",
  ["Create Healthstone (Greater)"] = "INV_Stone_04",
  ["Create Healthstone (Lesser)"] = "INV_Stone_04",
  ["Create Healthstone (Major)"] = "INV_Stone_04",
  ["Create Healthstone (Minor)"] = "INV_Stone_04",
  ["Create Soulstone"] = "Spell_Shadow_SoulGem",
  ["Create Soulstone (Greater)"] = "Spell_Shadow_SoulGem",
  ["Create Soulstone (Lesser)"] = "Spell_Shadow_SoulGem",
  ["Create Soulstone (Major)"] = "Spell_Shadow_SoulGem",
  ["Create Soulstone (Minor)"] = "Spell_Shadow_SoulGem",
  ["Create Spellstone"] = "INV_Misc_Gem_Sapphire_01",
  ["Create Spellstone (Greater)"] = "INV_Misc_Gem_Sapphire_01",
  ["Create Spellstone (Major)"] = "INV_Misc_Gem_Sapphire_01",
  ["Create Spellstone (Master)"] = "INV_Misc_Gem_Sapphire_01",
  ["Creeping Paralysis"] = "Spell_Nature_TimeStop",
  ["Cripple"] = "Spell_Shadow_Cripple",
  ["Crippling Poison"] = "Ability_PoisonSting",
  ["Crippling Poison II"] = "Ability_PoisonSting",
  ["Critical Mass"] = "Spell_Nature_WispHeal",
  ["Crossbows"] = "INV_Weapon_Crossbow_01",
  ["Cruelty"] = "Ability_Rogue_Eviscerate",
  ["Crusade"] = "Spell_Holy_Crusade",
  ["Crusader Aura"] = "Spell_Holy_CrusaderAura",
  ["Crusader Strike"] = "Spell_Holy_CrusaderStrike",
  ["Cultivation"] = "INV_Misc_Flower_01",
  ["Cure Disease"] = "Spell_Holy_NullifyDisease",
  ["Cure Poison"] = "Spell_Nature_NullifyPoison",
  ["Curse of Agony"] = "Spell_Shadow_CurseOfSargeras",
  ["Curse of Doom"] = "Spell_Shadow_AuraOfDarkness",
  ["Curse of Doom Effect"] = "Spell_Shadow_AuraOfDarkness",
  ["Curse of Exhaustion"] = "Spell_Shadow_GrimWard",
  ["Curse of Idiocy"] = "Spell_Shadow_MindRot",
  ["Curse of Recklessness"] = "Spell_Shadow_UnholyStrength",
  ["Curse of Shadow"] = "Spell_Shadow_CurseOfAchimonde",
  ["Curse of Tongues"] = "Spell_Shadow_CurseOfTounges",
  ["Curse of Weakness"] = "Spell_Shadow_CurseOfMannoroth",
  ["Curse of the Elements"] = "Spell_Shadow_ChillTouch",
  ["Cyclone"] = "Spell_Nature_EarthBind",
  ["Dagger Specialization"] = "INV_Weapon_ShortBlade_05",
  ["Daggers"] = "Ability_SteelMelee",
  ["Dampen Magic"] = "Spell_Nature_AbolishMagic",
  ["Dark Barrage"] = "Spell_Shadow_PainSpike",
  ["Dark Pact"] = "Spell_Shadow_DarkRitual",
  ["Darkness"] = "Spell_Shadow_Twilight",
  ["Dash"] = "Ability_Druid_Dash",
  ["Dawnstone Crab"] = "Ability_Hunter_Pet_Crab",
  ["Dazed"] = "Spell_Frost_Stun",
  ["Deadly Interrupt Effect"] = "INV_ThrowingKnife_06",
  ["Deadly Poison"] = "Ability_Rogue_DualWeild",
  ["Deadly Poison II"] = "Ability_Rogue_DualWeild",
  ["Deadly Poison III"] = "Ability_Rogue_DualWeild",
  ["Deadly Poison IV"] = "Ability_Rogue_DualWeild",
  ["Deadly Poison V"] = "Ability_Rogue_DualWeild",
  ["Deadly Poison VI"] = "Ability_Rogue_DualWeild",
  ["Deadly Poison VII"] = "Ability_Rogue_DualWeild",
  ["Deadly Throw"] = "INV_ThrowingKnife_06",
  ["Death Coil"] = "Spell_Shadow_DeathCoil",
  ["Death Wish"] = "Spell_Shadow_DeathPact",
  ["Deep Wound"] = "Ability_BackStab",
  ["Deep Wounds"] = "Ability_BackStab",
  ["Defense"] = "Ability_Racial_ShadowMeld",
  ["Defensive Stance"] = "Ability_Warrior_DefensiveStance",
  ["Defensive Stance Passive"] = "Ability_Warrior_DefensiveStance",
  ["Defiance"] = "Ability_Warrior_InnerRage",
  ["Deflection"] = "Ability_Parry",
  ["Delusions of Jin'do"] = "Spell_Shadow_UnholyFrenzy",
  ["Demon Armor"] = "Spell_Shadow_RagingScream",
  ["Demon Skin"] = "Spell_Shadow_RagingScream",
  ["Demonic Aegis"] = "Spell_Shadow_RagingScream",
  ["Demonic Embrace"] = "Spell_Shadow_Metamorphosis",
  ["Demonic Frenzy"] = "Spell_Shadow_DeathPact",
  ["Demonic Knowledge"] = "Spell_Shadow_ImprovedVampiricEmbrace",
  ["Demonic Resilience"] = "Spell_Shadow_DemonicFortitude",
  ["Demonic Sacrifice"] = "Spell_Shadow_PsychicScream",
  ["Demonic Tactics"] = "Spell_Shadow_DemonicTactics",
  ["Demoralizing Roar"] = "Ability_Druid_DemoralizingRoar",
  ["Demoralizing Shout"] = "Ability_Warrior_WarCry",
  ["Dense Sharpening Stone"] = "INV_Stone_SharpeningStone_05",
  ["Desperate Prayer"] = "Spell_Holy_Restoration",
  ["Destructive Reach"] = "Spell_Shadow_CorpseExplode",
  ["Detect"] = "Ability_Hibernation",
  ["Detect Greater Invisibility"] = "Spell_Shadow_DetectInvisibility",
  ["Detect Invisibility"] = "Spell_Shadow_DetectInvisibility",
  ["Detect Lesser Invisibility"] = "Spell_Shadow_DetectLesserInvisibility",
  ["Detect Magic"] = "Spell_Holy_Dizzy",
  ["Detect Traps"] = "Ability_Spy",
  ["Deterrence"] = "Ability_Whirlwind",
  ["Devastate"] = "INV_Sword_11",
  ["Devastation"] = "Spell_Fire_FlameShock",
  ["Devotion Aura"] = "Spell_Holy_DevotionAura",
  ["Devour Magic"] = "Spell_Nature_Purge",
  ["Devour Magic Effect"] = "Spell_Nature_Purge",
  ["Devouring Plague"] = "Spell_Shadow_BlackPlague",
  ["Diplomacy"] = "INV_Misc_Note_02",
  ["Dire Bear Form"] = "Ability_Racial_BearForm",
  ["Dirty Deeds"] = "Spell_Shadow_SummonSuccubus",
  ["Dirty Tricks"] = "Ability_Sap",
  ["Disarm"] = "Ability_Warrior_Disarm",
  ["Disarm Trap"] = "Spell_Shadow_GrimWard",
  ["Disease Cleansing Totem"] = "Spell_Nature_DiseaseCleansingTotem",
  ["Disenchant"] = "Spell_Holy_RemoveCurse",
  ["Disengage"] = "Ability_Rogue_Feint",
  ["Dismiss Pet"] = "Spell_Nature_SpiritWolf",
  ["Dispel Magic"] = "Spell_Holy_DispelMagic",
  ["Distract"] = "Ability_Rogue_Distract",
  ["Distracting Shot"] = "Spell_Arcane_Blink",
  ["Dive"] = "Spell_Shadow_BurningSpirit",
  ["Divine Favor"] = "Spell_Holy_Heal",
  ["Divine Fury"] = "Spell_Holy_SealOfWrath",
  ["Divine Illumination"] = "Spell_Holy_DivineIllumination",
  ["Divine Intellect"] = "Spell_Nature_Sleep",
  ["Divine Intervention"] = "Spell_Nature_TimeStop",
  ["Divine Protection"] = "Spell_Holy_Restoration",
  ["Divine Shield"] = "Spell_Holy_DivineIntervention",
  ["Divine Spirit"] = "Spell_Holy_DivineSpirit",
  ["Divine Strength"] = "Ability_GolemThunderClap",
  ["Divine Wrath"] = "Spell_Holy_SearingLight",
  ["Dodge"] = "Spell_Nature_Invisibilty",
  ["Doomfire"] = "Spell_Fire_Incinerate",
  ["Dragon's Breath"] = "INV_Misc_Head_Dragon_01",
  ["Dragonscale Leatherworking"] = "INV_Misc_MonsterScales_03",
  ["Drain Life"] = "Spell_Shadow_LifeDrain02",
  ["Drain Mana"] = "Spell_Shadow_SiphonMana",
  ["Drain Soul"] = "Spell_Shadow_Haunting",
  ["Dreamless Sleep"] = "Spell_Nature_Sleep",
  ["Drink"] = "INV_Drink_07",
  ["Dual Wield"] = "Ability_DualWield",
  ["Dual Wield Specialization"] = "Ability_DualWield",
  ["Duel"] = "Temp",
  ["Dust Cloud"] = "Ability_Hibernation",
  ["Eagle Eye"] = "Ability_Hunter_EagleEye",
  ["Earth Elemental Totem"] = "Spell_Nature_EarthElemental_Totem",
  ["Earth Shield"] = "Spell_Nature_SkinofEarth",
  ["Earth Shock"] = "Spell_Nature_EarthShock",
  ["Earthbind"] = "Spell_Nature_StrengthOfEarthTotem02",
  ["Earthbind Totem"] = "Spell_Nature_StrengthOfEarthTotem02",
  ["Earthstrike"] = "Spell_Nature_AbolishMagic",
  ["Efficiency"] = "Spell_Frost_WizardMark",
  ["Elemental Devastation"] = "Spell_Fire_ElementalDevastation",
  ["Elemental Focus"] = "Spell_Shadow_ManaBurn",
  ["Elemental Fury"] = "Spell_Fire_Volcano",
  ["Elemental Leatherworking"] = "Trade_LeatherWorking",
  ["Elemental Mastery"] = "Spell_Nature_WispHeal",
  ["Elemental Precision"] = "Spell_Nature_ElementalPrecision_1",
  ["Elemental Sharpening Stone"] = "INV_Stone_02",
  ["Elemental Weapons"] = "Spell_Fire_FlameTounge",
  ["Elune's Grace"] = "Spell_Holy_ElunesGrace",
  ["Elusiveness"] = "Spell_Magic_LesserInvisibilty",
  ["Emberstorm"] = "Spell_Fire_SelfDestruct",
  ["Empowered Arcane Missiles"] = "Spell_Nature_StarFall",
  ["Empowered Corruption"] = "Spell_Shadow_AbominationExplosion",
  ["Empowered Fireball"] = "Spell_Fire_FlameBolt",
  ["Empowered Frostbolt"] = "Spell_Frost_FrostBolt02",
  ["Empowered Healing"] = "Spell_Holy_GreaterHeal",
  ["Empowered Rejuvenation"] = "Ability_Druid_EmpoweredRejuvination",
  ["Empowered Touch"] = "Ability_Druid_EmpoweredTouch",
  ["Enamored Water Spirit"] = "INV_Wand_01",
  ["Enchanting"] = "Trade_Engraving",
  ["Endurance"] = "Spell_Nature_UnyeildingStamina",
  ["Endurance Training"] = "Spell_Nature_Reincarnation",
  ["Engineering"] = "Trade_Engineering",
  ["Engineering Specialization"] = "INV_Misc_Gear_01",
  ["Enrage"] = "Ability_Druid_Enrage",
  ["Enriched Manna Biscuit"] = "INV_Misc_Fork&Knife",
  ["Enslave Demon"] = "Spell_Shadow_EnslaveDemon",
  ["Entangling Roots"] = "Spell_Nature_StrangleVines",
  ["Entrapment"] = "Spell_Nature_StrangleVines",
  ["Envenom"] = "Ability_Rogue_Disembowel",
  ["Escape Artist"] = "Ability_Rogue_Trip",
  ["Evasion"] = "Spell_Shadow_ShadowWard",
  ["Eviscerate"] = "Ability_Rogue_Eviscerate",
  ["Evocation"] = "Spell_Nature_Purge",
  ["Execute"] = "INV_Sword_48",
  ["Exorcism"] = "Spell_Holy_Excorcism_02",
  ["Expansive Mind"] = "INV_Enchant_EssenceEternalLarge",
  ["Explosive Trap"] = "Spell_Fire_SelfDestruct",
  ["Explosive Trap Effect"] = "Spell_Fire_SelfDestruct",
  ["Expose Armor"] = "Ability_Warrior_Riposte",
  ["Expose Weakness"] = "Ability_Hunter_SniperShot",
  ["Extract Gas"] = "Spell_Nature_AbolishMagic",
  ["Eye for an Eye"] = "Spell_Holy_EyeforanEye",
  ["Eye of Kilrogg"] = "Spell_Shadow_EvilEye",
  ["Eye of the Storm"] = "Spell_Shadow_SoulLeech_2",
  ["Eyes of the Beast"] = "Ability_EyeOfTheOwl",
  ["Fade"] = "Spell_Magic_LesserInvisibilty",
  ["Faerie Fire"] = "Spell_Nature_FaerieFire",
  ["Faerie Fire (Feral)"] = "Spell_Nature_FaerieFire",
  ["Fanaticism"] = "Spell_Holy_Fanaticism",
  ["Far Sight"] = "Spell_Nature_FarSight",
  ["Fatal Attraction"] = "Spell_Shadow_Shadowfury",
  ["Fear"] = "Spell_Shadow_Possession",
  ["Fear Ward"] = "Spell_Holy_Excorcism",
  ["Feed Pet"] = "Ability_Hunter_BeastTraining",
  ["Feed Pet Effect"] = "Ability_Hunter_BeastTraining",
  ["Feedback"] = "Spell_Shadow_RitualOfSacrifice",
  ["Feign Death"] = "Ability_Rogue_FeignDeath",
  ["Feint"] = "Ability_Rogue_Feint",
  ["Fel Armor"] = "Spell_Shadow_FelArmour",
  ["Fel Concentration"] = "Spell_Shadow_FingerOfDeath",
  ["Fel Domination"] = "Spell_Nature_RemoveCurse",
  ["Fel Energy"] = "Spell_Shadow_PsychicScream",
  ["Fel Intellect"] = "Spell_Holy_MagicalSentry",
  ["Fel Rage"] = "Spell_Fire_ElementalDevastation",
  ["Fel Stamina"] = "Spell_Shadow_AntiShadow",
  ["Felfire"] = "Spell_Fire_Fireball",
  ["Feline Grace"] = "INV_Feather_01",
  ["Feral Aggression"] = "Ability_Druid_DemoralizingRoar",
  ["Feral Charge"] = "Ability_Hunter_Pet_Bear",
  ["Feral Instinct"] = "Ability_Ambush",
  ["Feral Swiftness"] = "Spell_Nature_SpiritWolf",
  ["Ferocious Bite"] = "Ability_Druid_FerociousBite",
  ["Ferocious Inspiration"] = "Ability_Hunter_FerociousInspiration",
  ["Ferocity"] = "INV_Misc_MonsterClaw_04",
  ["Fetish"] = "INV_Misc_Horn_01",
  ["Find Fish"] = "INV_Misc_Fish_02",
  ["Find Herbs"] = "INV_Misc_Flower_02",
  ["Find Minerals"] = "Spell_Nature_Earthquake",
  ["Find Treasure"] = "Racial_Dwarf_FindTreasure",
  ["Find Weakness"] = "Ability_Rogue_FindWeakness",
  ["Fire Blast"] = "Spell_Fire_Fireball",
  ["Fire Breath"] = "Spell_Fire_Burnout",
  ["Fire Elemental Totem"] = "Spell_Fire_Elemental_Totem",
  ["Fire Nova Totem"] = "Spell_Fire_SealOfFire",
  ["Fire Power"] = "Spell_Fire_Immolation",
  ["Fire Resistance"] = "Spell_Fire_FireArmor",
  ["Fire Resistance Aura"] = "Spell_Fire_SealOfFire",
  ["Fire Resistance Totem"] = "Spell_FireResistanceTotem_01",
  ["Fire Shield"] = "Spell_Fire_FireArmor",
  ["Fire Vulnerability"] = "Spell_Fire_SoulBurn",
  ["Fire Ward"] = "Spell_Fire_FireArmor",
  ["Fire Weakness"] = "INV_Misc_QirajiCrystal_02",
  ["Fireball"] = "Spell_Fire_FlameBolt",
  ["Firebolt"] = "Spell_Fire_FireBolt",
  ["First Aid"] = "Spell_Holy_SealOfSacrifice",
  ["Fishing"] = "Trade_Fishing",
  ["Fishing Poles"] = "Trade_Fishing",
  ["Fist Weapon Specialization"] = "INV_Gauntlets_04",
  ["Fist Weapons"] = "INV_Gauntlets_04",
  ["Flame Shock"] = "Spell_Fire_FlameShock",
  ["Flame Throwing"] = "Spell_Fire_Flare",
  ["Flamestrike"] = "Spell_Fire_SelfDestruct",
  ["Flamethrower"] = "Spell_Fire_Incinerate",
  ["Flametongue Attack"] = "Spell_Fire_FlameTounge",
  ["Flametongue Totem"] = "Spell_Nature_GuardianWard",
  ["Flametongue Weapon"] = "Spell_Fire_FlameTounge",
  ["Flare"] = "Spell_Fire_Flare",
  ["Flash Heal"] = "Spell_Holy_FlashHeal",
  ["Flash of Light"] = "Spell_Holy_FlashHeal",
  ["Flight Form"] = "Ability_Druid_FlightForm",
  ["Flurry"] = "Ability_GhoulFrenzy",
  ["Focused Casting"] = "Spell_Arcane_Blink",
  ["Focused Mind"] = "Spell_Nature_FocusedMind",
  ["Focused Power"] = "Spell_Shadow_FocusedPower",
  ["Focused Rage"] = "Ability_Warrior_FocusedRage",
  ["Focused Starlight"] = "INV_Staff_01",
  ["Food"] = "INV_Misc_Fork&Knife",
  ["Forbearance"] = "Spell_Holy_RemoveCurse",
  ["Force of Nature"] = "Ability_Druid_ForceofNature",
  ["Force of Will"] = "Spell_Nature_SlowingTotem",
  ["Freezing Trap"] = "Spell_Frost_ChainsOfIce",
  ["Freezing Trap Effect"] = "Spell_Frost_ChainsOfIce",
  ["Frenzied Regeneration"] = "Ability_BullRush",
  ["Frenzy"] = "INV_Misc_MonsterClaw_03",
  ["Frenzy Effect"] = "INV_Misc_MonsterClaw_03",
  ["Frost Armor"] = "Spell_Frost_FrostArmor02",
  ["Frost Blast"] = "Spell_Frost_FrostBolt02",
  ["Frost Channeling"] = "Spell_Frost_Stun",
  ["Frost Nova"] = "Spell_Frost_FrostNova",
  ["Frost Resistance"] = "Spell_Frost_FrostWard",
  ["Frost Resistance Aura"] = "Spell_Frost_WizardMark",
  ["Frost Resistance Totem"] = "Spell_FrostResistanceTotem_01",
  ["Frost Shock"] = "Spell_Frost_FrostShock",
  ["Frost Trap"] = "Spell_Frost_FreezingBreath",
  ["Frost Trap Aura"] = "Spell_Frost_FrostNova",
  ["Frost Ward"] = "Spell_Frost_FrostWard",
  ["Frost Warding"] = "Spell_Frost_FrostWard",
  ["Frost Weakness"] = "INV_Misc_QirajiCrystal_04",
  ["Frostbite"] = "Spell_Frost_FrostArmor",
  ["Frostbolt"] = "Spell_Frost_FrostBolt02",
  ["Frostbrand Attack"] = "Spell_Frost_FrostBrand",
  ["Frostbrand Weapon"] = "Spell_Frost_FrostBrand",
  ["Frozen Core"] = "Spell_Frost_FrozenCore",
  ["Furious Howl"] = "Ability_Hunter_Pet_Wolf",
  ["Furor"] = "Spell_Holy_BlessingOfStamina",
  ["Fury of the Crashing Waves"] = "Spell_Nature_UnrelentingStorm",
  ["Garrote"] = "Ability_Rogue_Garrote",
  ["Garrote - Silence"] = "Ability_Rogue_Garrote",
  ["Ghost Wolf"] = "Spell_Nature_SpiritWolf",
  ["Ghostly Strike"] = "Spell_Shadow_Curse",
  ["Gift of Life"] = "INV_Misc_Gem_Pearl_05",
  ["Gift of Nature"] = "Spell_Nature_ProtectionformNature",
  ["Gift of the Naaru"] = "Spell_Holy_HolyProtection",
  ["Gift of the Wild"] = "Spell_Nature_Regeneration",
  ["Gouge"] = "Ability_Gouge",
  ["Grace of Air"] = "Spell_Nature_InvisibilityTotem",
  ["Grace of Air Totem"] = "Spell_Nature_InvisibilityTotem",
  ["Great Stamina"] = "Spell_Nature_UnyeildingStamina",
  ["Greater Blessing of Kings"] = "Spell_Magic_GreaterBlessingofKings",
  ["Greater Blessing of Light"] = "Spell_Holy_GreaterBlessingofLight",
  ["Greater Blessing of Might"] = "Spell_Holy_GreaterBlessingofKings",
  ["Greater Blessing of Salvation"] = "Spell_Holy_GreaterBlessingofSalvation",
  ["Greater Blessing of Sanctuary"] = "Spell_Holy_GreaterBlessingofSanctuary",
  ["Greater Blessing of Wisdom"] = "Spell_Holy_GreaterBlessingofWisdom",
  ["Greater Dreamless Sleep"] = "Spell_Nature_Sleep",
  ["Greater Heal"] = "Spell_Holy_GreaterHeal",
  ["Grievous Throw"] = "Ability_BackStab",
  ["Grim Reach"] = "Spell_Shadow_CallofBone",
  ["Grounding Totem"] = "Spell_Nature_GroundingTotem",
  ["Grounding Totem Effect"] = "Spell_Nature_GroundingTotem",
  ["Grovel"] = "Temp",
  ["Growl"] = "Ability_Physical_Taunt",
  ["Guardian's Favor"] = "Spell_Holy_SealOfProtection",
  ["Gun Specialization"] = "INV_Musket_03",
  ["Guns"] = "INV_Weapon_Rifle_01",
  ["Hammer of Justice"] = "Spell_Holy_SealOfMight",
  ["Hammer of Wrath"] = "Ability_ThunderClap",
  ["Hamstring"] = "Ability_ShockWave",
  ["Harass"] = "Ability_Hunter_Harass",
  ["Hardiness"] = "INV_Helmet_23",
  ["Haste"] = "INV_Potion_108",
  ["Hawk Eye"] = "Ability_TownWatch",
  ["Heal"] = "Spell_Holy_Heal",
  ["Healing Focus"] = "Spell_Holy_HealingFocus",
  ["Healing Light"] = "Spell_Holy_HolyBolt",
  ["Healing Stream"] = "INV_Spear_04",
  ["Healing Stream Totem"] = "INV_Spear_04",
  ["Healing Touch"] = "Spell_Nature_HealingTouch",
  ["Healing Wave"] = "Spell_Nature_MagicImmunity",
  ["Healing Way"] = "Spell_Nature_HealingWay",
  ["Health Funnel"] = "Spell_Shadow_LifeDrain",
  ["Heart of the Wild"] = "Spell_Holy_BlessingOfAgility",
  ["Heavy Sharpening Stone"] = "INV_Stone_SharpeningStone_03",
  ["Hellfire"] = "Spell_Fire_Incinerate",
  ["Hellfire Effect"] = "Spell_Fire_Incinerate",
  ["Hellfire Superiority"] = "INV_BannerPVP_02",
  ["Hemorrhage"] = "Spell_Shadow_LifeDrain",
  ["Herb Gathering"] = "Spell_Nature_NatureTouchGrow",
  ["Herbalism"] = "Spell_Nature_NatureTouchGrow",
  ["Heroic Strike"] = "Ability_Rogue_Ambush",
  ["Heroism"] = "Ability_Shaman_Heroism",
  ["Hex of Weakness"] = "Spell_Shadow_FingerOfDeath",
  ["Hibernate"] = "Spell_Nature_Sleep",
  ["Holy Fire"] = "Spell_Holy_SearingLight",
  ["Holy Light"] = "Spell_Holy_HolyBolt",
  ["Holy Nova"] = "Spell_Holy_HolyNova",
  ["Holy Power"] = "Spell_Holy_Power",
  ["Holy Reach"] = "Spell_Holy_Purify",
  ["Holy Shield"] = "Spell_Holy_BlessingOfProtection",
  ["Holy Shock"] = "Spell_Holy_SearingLight",
  ["Holy Specialization"] = "Spell_Holy_SealOfSalvation",
  ["Holy Wrath"] = "Spell_Holy_Excorcism",
  ["Honorless Target"] = "Spell_Magic_LesserInvisibilty",
  ["Horse Riding"] = "Spell_Nature_Swiftness",
  ["Howl of Terror"] = "Spell_Shadow_DeathScream",
  ["Humanoid Slaying"] = "Spell_Holy_PrayerOfHealing",
  ["Hunter's Mark"] = "Ability_Hunter_SniperShot",
  ["Hurricane"] = "Spell_Nature_Cyclone",
  ["Hypothermia"] = "Spell_Fire_BlueImmolation",
  ["Ice Armor"] = "Spell_Frost_FrostArmor02",
  ["Ice Barrier"] = "Spell_Ice_Lament",
  ["Ice Block"] = "Spell_Frost_Frost",
  ["Ice Floes"] = "Spell_Frost_IceFloes",
  ["Ice Lance"] = "Spell_Frost_FrostBlast",
  ["Ice Shards"] = "Spell_Frost_IceShard",
  ["Icy Veins"] = "Spell_Frost_ColdHearted",
  ["Ignite"] = "Spell_Fire_Incinerate",
  ["Ignite Mana"] = "Spell_Fire_Incinerate",
  ["Illumination"] = "Spell_Holy_GreaterHeal",
  ["Immolate"] = "Spell_Fire_Immolation",
  ["Immolation Trap"] = "Spell_Fire_FlameShock",
  ["Immolation Trap Effect"] = "Spell_Fire_FlameShock",
  ["Impact"] = "Spell_Fire_MeteorStorm",
  ["Impale"] = "Ability_SearingArrow",
  ["Impaling Spine"] = "Spell_Frost_IceShard",
  ["Improved Ambush"] = "Ability_Rogue_Ambush",
  ["Improved Arcane Missiles"] = "Spell_Nature_StarFall",
  ["Improved Arcane Shot"] = "Ability_ImpalingBolt",
  ["Improved Aspect of the Hawk"] = "Spell_Nature_RavenForm",
  ["Improved Aspect of the Monkey"] = "Ability_Hunter_AspectOfTheMonkey",
  ["Improved Backstab"] = "Ability_BackStab",
  ["Improved Barrage"] = "Ability_UpgradeMoonGlaive",
  ["Improved Berserker Rage"] = "Spell_Nature_AncestralGuardian",
  ["Improved Blessing of Might"] = "Spell_Holy_FistOfJustice",
  ["Improved Blessing of Wisdom"] = "Spell_Holy_SealOfWisdom",
  ["Improved Blink"] = "Spell_Arcane_Blink",
  ["Improved Blizzard"] = "Spell_Frost_IceStorm",
  ["Improved Bloodrage"] = "Ability_Racial_BloodRage",
  ["Improved Chain Heal"] = "Spell_Nature_HealingWaveGreater",
  ["Improved Chain Lightning"] = "Spell_Nature_ChainLightning",
  ["Improved Challenging Shout"] = "Ability_Warrior_Challange",
  ["Improved Charge"] = "Ability_Warrior_Charge",
  ["Improved Cleave"] = "Ability_Warrior_Cleave",
  ["Improved Concentration Aura"] = "Spell_Holy_MindSooth",
  ["Improved Concussive Shot"] = "Spell_Frost_Stun",
  ["Improved Cone of Cold"] = "Spell_Frost_Glacier",
  ["Improved Corruption"] = "Spell_Shadow_AbominationExplosion",
  ["Improved Counterspell"] = "Spell_Frost_IceShock",
  ["Improved Curse of Agony"] = "Spell_Shadow_CurseOfSargeras",
  ["Improved Curse of Weakness"] = "Spell_Shadow_CurseOfMannoroth",
  ["Improved Demoralizing Shout"] = "Ability_Warrior_WarCry",
  ["Improved Devotion Aura"] = "Spell_Holy_DevotionAura",
  ["Improved Disarm"] = "Ability_Warrior_Disarm",
  ["Improved Drain Life"] = "Spell_Shadow_LifeDrain02",
  ["Improved Drain Soul"] = "Spell_Shadow_Haunting",
  ["Improved Enrage"] = "Ability_Druid_Enrage",
  ["Improved Enslave Demon"] = "Spell_Shadow_EnslaveDemon",
  ["Improved Eviscerate"] = "Ability_Rogue_Eviscerate",
  ["Improved Execute"] = "INV_Sword_48",
  ["Improved Expose Armor"] = "Ability_Warrior_Riposte",
  ["Improved Eyes of the Beast"] = "Ability_EyeOfTheOwl",
  ["Improved Fade"] = "Spell_Magic_LesserInvisibilty",
  ["Improved Feign Death"] = "Ability_Rogue_FeignDeath",
  ["Improved Fire Blast"] = "Spell_Fire_Fireball",
  ["Improved Fire Totems"] = "Spell_Fire_SealOfFire",
  ["Improved Fire Ward"] = "Spell_Fire_FireArmor",
  ["Improved Fireball"] = "Spell_Fire_FlameBolt",
  ["Improved Firebolt"] = "Spell_Fire_FireBolt",
  ["Improved Flamestrike"] = "Spell_Fire_SelfDestruct",
  ["Improved Flash of Light"] = "Spell_Holy_FlashHeal",
  ["Improved Frost Nova"] = "Spell_Frost_FreezingBreath",
  ["Improved Frostbolt"] = "Spell_Frost_FrostBolt02",
  ["Improved Ghost Wolf"] = "Spell_Nature_SpiritWolf",
  ["Improved Gouge"] = "Ability_Gouge",
  ["Improved Hammer of Justice"] = "Spell_Holy_SealOfMight",
  ["Improved Hamstring"] = "Ability_ShockWave",
  ["Improved Healing"] = "Spell_Holy_Heal02",
  ["Improved Healing Touch"] = "Spell_Nature_HealingTouch",
  ["Improved Healing Wave"] = "Spell_Nature_MagicImmunity",
  ["Improved Health Funnel"] = "Spell_Shadow_LifeDrain",
  ["Improved Healthstone"] = "INV_Stone_04",
  ["Improved Heroic Strike"] = "Ability_Rogue_Ambush",
  ["Improved Holy Shield"] = "Spell_Holy_BlessingOfProtection",
  ["Improved Howl of Terror"] = "Spell_Shadow_DeathScream",
  ["Improved Hunter's Mark"] = "Ability_Hunter_SniperShot",
  ["Improved Immolate"] = "Spell_Fire_Immolation",
  ["Improved Imp"] = "Spell_Shadow_SummonImp",
  ["Improved Inner Fire"] = "Spell_Holy_InnerFire",
  ["Improved Intercept"] = "Ability_Rogue_Sprint",
  ["Improved Intimidating Shout"] = "Ability_GolemThunderClap",
  ["Improved Judgement"] = "Spell_Holy_RighteousFury",
  ["Improved Kick"] = "Ability_Kick",
  ["Improved Kidney Shot"] = "Ability_Rogue_KidneyShot",
  ["Improved Lash of Pain"] = "Spell_Shadow_Curse",
  ["Improved Lay on Hands"] = "Spell_Holy_LayOnHands",
  ["Improved Leader of the Pack"] = "Spell_Nature_UnyeildingStamina",
  ["Improved Life Tap"] = "Spell_Shadow_BurningSpirit",
  ["Improved Lightning Bolt"] = "Spell_Nature_Lightning",
  ["Improved Lightning Shield"] = "Spell_Nature_LightningShield",
  ["Improved Magma Totem"] = "Spell_Fire_SelfDestruct",
  ["Improved Mana Burn"] = "Spell_Shadow_ManaBurn",
  ["Improved Mana Shield"] = "Spell_Shadow_DetectLesserInvisibility",
  ["Improved Mana Spring Totem"] = "Spell_Nature_ManaRegenTotem",
  ["Improved Mark of the Wild"] = "Spell_Nature_Regeneration",
  ["Improved Mend Pet"] = "Ability_Hunter_MendPet",
  ["Improved Mind Blast"] = "Spell_Shadow_UnholyFrenzy",
  ["Improved Moonfire"] = "Spell_Nature_StarFall",
  ["Improved Mortal Strike"] = "Ability_Warrior_SavageBlow",
  ["Improved Nature's Grasp"] = "Spell_Nature_NaturesWrath",
  ["Improved Overpower"] = "INV_Sword_05",
  ["Improved Power Word: Fortitude"] = "Spell_Holy_WordFortitude",
  ["Improved Power Word: Shield"] = "Spell_Holy_PowerWordShield",
  ["Improved Prayer of Healing"] = "Spell_Holy_PrayerOfHealing02",
  ["Improved Psychic Scream"] = "Spell_Shadow_PsychicScream",
  ["Improved Regrowth"] = "Spell_Nature_ResistNature",
  ["Improved Reincarnation"] = "Spell_Nature_Reincarnation",
  ["Improved Rejuvenation"] = "Spell_Nature_Rejuvenation",
  ["Improved Rend"] = "Ability_Gouge",
  ["Improved Renew"] = "Spell_Holy_Renew",
  ["Improved Retribution Aura"] = "Spell_Holy_AuraOfLight",
  ["Improved Revenge"] = "Ability_Warrior_Revenge",
  ["Improved Revive Pet"] = "Ability_Hunter_BeastSoothe",
  ["Improved Righteous Fury"] = "Spell_Holy_SealOfFury",
  ["Improved Rupture"] = "Ability_Rogue_Rupture",
  ["Improved Scorch"] = "Spell_Fire_SoulBurn",
  ["Improved Scorpid Sting"] = "Ability_Hunter_CriticalShot",
  ["Improved Seal of Righteousness"] = "Ability_ThunderBolt",
  ["Improved Seal of the Crusader"] = "Spell_Holy_HolySmite",
  ["Improved Searing Pain"] = "Spell_Fire_SoulBurn",
  ["Improved Serpent Sting"] = "Ability_Hunter_Quickshot",
  ["Improved Shadow Bolt"] = "Spell_Shadow_ShadowBolt",
  ["Improved Shadow Word: Pain"] = "Spell_Shadow_ShadowWordPain",
  ["Improved Shield Bash"] = "Ability_Warrior_ShieldBash",
  ["Improved Shield Block"] = "Ability_Defend",
  ["Improved Shield Wall"] = "Ability_Warrior_ShieldWall",
  ["Improved Sinister Strike"] = "Spell_Shadow_RitualOfSacrifice",
  ["Improved Slam"] = "Ability_Warrior_DecisiveStrike",
  ["Improved Slice and Dice"] = "Ability_Rogue_SliceDice",
  ["Improved Sprint"] = "Ability_Rogue_Sprint",
  ["Improved Stings"] = "Ability_Hunter_Quickshot",
  ["Improved Succubus"] = "Spell_Shadow_SummonSuccubus",
  ["Improved Sunder Armor"] = "Ability_Warrior_Sunder",
  ["Improved Taunt"] = "Spell_Nature_Reincarnation",
  ["Improved Thunder Clap"] = "Ability_ThunderClap",
  ["Improved Tranquility"] = "Spell_Nature_Tranquility",
  ["Improved Vampiric Embrace"] = "Spell_Shadow_ImprovedVampiricEmbrace",
  ["Improved Vanish"] = "Ability_Vanish",
  ["Improved Voidwalker"] = "Spell_Shadow_SummonVoidWalker",
  ["Improved Weapon Totems"] = "Spell_Fire_EnchantWeapon",
  ["Improved Whirlwind"] = "Ability_Whirlwind",
  ["Improved Wing Clip"] = "Ability_Rogue_Trip",
  ["Incinerate"] = "Spell_Fire_Burnout",
  ["Incineration"] = "Spell_Fire_FlameShock",
  ["Inferno"] = "Spell_Shadow_SummonInfernal",
  ["Initiative"] = "Spell_Shadow_Fumble",
  ["Inner Fire"] = "Spell_Holy_InnerFire",
  ["Inner Focus"] = "Spell_Frost_WindWalkOn",
  ["Innervate"] = "Spell_Nature_Lightning",
  ["Insect Swarm"] = "Spell_Nature_InsectSwarm",
  ["Insidious Whisper"] = "Spell_Shadow_ManaFeed",
  ["Insignifigance"] = "Ability_Hibernation",
  ["Inspiration"] = "Spell_Holy_LayOnHands",
  ["Instant Poison"] = "Ability_Poisons",
  ["Instant Poison II"] = "Ability_Poisons",
  ["Instant Poison III"] = "Ability_Poisons",
  ["Instant Poison IV"] = "Ability_Poisons",
  ["Instant Poison V"] = "Ability_Poisons",
  ["Instant Poison VI"] = "Ability_Poisons",
  ["Instant Poison VII"] = "Ability_Poisons",
  ["Intensity"] = "Spell_Fire_LavaSpawn",
  ["Intercept"] = "Ability_Rogue_Sprint",
  ["Intercept Stun"] = "Spell_Frost_Stun",
  ["Intervene"] = "Ability_Warrior_VictoryRush",
  ["Intimidating Shout"] = "Ability_GolemThunderClap",
  ["Intimidation"] = "Ability_Devour",
  ["Invisibility"] = "Ability_Mage_Invisibility",
  ["Iron Will"] = "Spell_Magic_MageArmor",
  ["Jewelcrafting"] = "INV_Misc_Gem_02",
  ["Jom Gabbar"] = "INV_Misc_EngGizmos_19",
  ["Judgement"] = "Spell_Holy_RighteousFury",
  ["Judgement of Blood"] = "Spell_Shadow_LifeDrain",
  ["Judgement of Command"] = "Ability_Warrior_InnerRage",
  ["Judgement of Justice"] = "Spell_Holy_SealOfWrath",
  ["Judgement of Light"] = "Spell_Holy_HealingAura",
  ["Judgement of Reckoning"] = "Ability_Warrior_InnerRage",
  ["Judgement of Righteousness"] = "Ability_ThunderBolt",
  ["Judgement of Wisdom"] = "Spell_Holy_RighteousnessAura",
  ["Judgement of the Crusader"] = "Spell_Holy_HolySmite",
  ["Kick"] = "Ability_Kick",
  ["Kick - Silenced"] = "Ability_Kick",
  ["Kidney Shot"] = "Ability_Rogue_KidneyShot",
  ["Kill Command"] = "Ability_Hunter_KillCommand",
  ["Killer Instinct"] = "Spell_Holy_BlessingOfStamina",
  ["Kiss of the Spider"] = "INV_Trinket_Naxxramas04",
  ["Kodo Riding"] = "Spell_Nature_Swiftness",
  ["Lacerate"] = "Ability_Druid_Lacerate",
  ["Lash of Pain"] = "Spell_Shadow_Curse",
  ["Last Stand"] = "Spell_Holy_AshesToAshes",
  ["Lay on Hands"] = "Spell_Holy_LayOnHands",
  ["Leader of the Pack"] = "Spell_Nature_UnyeildingStamina",
  ["Leather"] = "INV_Chest_Leather_09",
  ["Leatherworking"] = "INV_Misc_ArmorKit_17",
  ["Lesser Heal"] = "Spell_Holy_LesserHeal",
  ["Lesser Healing Wave"] = "Spell_Nature_HealingWaveLesser",
  ["Lesser Invisibility"] = "Spell_Magic_LesserInvisibilty",
  ["Lethal Shots"] = "Ability_SearingArrow",
  ["Lethality"] = "Ability_CriticalStrike",
  ["Levitate"] = "Spell_Holy_LayOnHands",
  ["Libram"] = "INV_Misc_Book_11",
  ["Life Tap"] = "Spell_Shadow_BurningSpirit",
  ["Lifebloom"] = "INV_Misc_Herb_Felblossom",
  ["Light's Grace"] = "Spell_Holy_LightsGrace",
  ["Lightning Bolt"] = "Spell_Nature_Lightning",
  ["Lightning Breath"] = "Spell_Nature_Lightning",
  ["Lightning Mastery"] = "Spell_Lightning_LightningBolt01",
  ["Lightning Reflexes"] = "Spell_Nature_Invisibilty",
  ["Lightning Shield"] = "Spell_Nature_LightningShield",
  ["Lightning Speed"] = "Spell_Nature_UnrelentingStorm",
  ["Lightwell"] = "Spell_Holy_SummonLightwell",
  ["Lightwell Renew"] = "Spell_Holy_SummonLightwell",
  ["Lockpicking"] = "Spell_Nature_MoonKey",
  ["Long Daze"] = "Spell_Frost_Stun",
  ["Lust for Battle"] = "Spell_Shadow_DeathPact",
  ["Mace Specialization"] = "INV_Mace_01",
  ["Mace Stun Effect"] = "Spell_Frost_Stun",
  ["Mage Armor"] = "Spell_MageArmor",
  ["Magic Absorption"] = "Spell_Nature_AstralRecalGroup",
  ["Magic Attunement"] = "Spell_Nature_AbolishMagic",
  ["Magma Totem"] = "Spell_Fire_SelfDestruct",
  ["Mail"] = "INV_Chest_Chain_05",
  ["Maim"] = "Ability_Druid_Mangle",
  ["Major Dreamless Sleep"] = "Spell_Nature_Sleep",
  ["Malediction"] = "Spell_Shadow_CurseOfAchimonde",
  ["Malice"] = "Ability_Racial_BloodRage",
  ["Mana Burn"] = "Spell_Shadow_ManaBurn",
  ["Mana Feed"] = "Spell_Shadow_ManaFeed",
  ["Mana Shield"] = "Spell_Shadow_DetectLesserInvisibility",
  ["Mana Spring"] = "Spell_Nature_ManaRegenTotem",
  ["Mana Spring Totem"] = "Spell_Nature_ManaRegenTotem",
  ["Mana Tap"] = "Spell_Arcane_ManaTap",
  ["Mana Tide"] = "Spell_Frost_SummonWaterElemental",
  ["Mana Tide Totem"] = "Spell_Frost_SummonWaterElemental",
  ["Mangle"] = "Ability_Druid_Mangle",
  ["Mangle (Bear)"] = "Ability_Druid_Mangle2",
  ["Mangle (Cat)"] = "Ability_Druid_Mangle2",
  ["Mark of the Wild"] = "Spell_Nature_Regeneration",
  ["Martyrdom"] = "Spell_Nature_Tranquility",
  ["Mass Dispel"] = "Spell_Arcane_MassDispel",
  ["Master Conjuror"] = "INV_Ammo_FireTar",
  ["Master Demonologist"] = "Spell_Shadow_ShadowPact",
  ["Master Summoner"] = "Spell_Shadow_ImpPhaseShift",
  ["Master Tactician"] = "Ability_Hunter_MasterTactitian",
  ["Master of Deception"] = "Spell_Shadow_Charm",
  ["Master of Elements"] = "Spell_Fire_MasterOfElements",
  ["Master of Subtlety"] = "Ability_Rogue_MasterOfSubtlety",
  ["Maul"] = "Ability_Druid_Maul",
  ["Mechanostrider Piloting"] = "Spell_Nature_Swiftness",
  ["Meditation"] = "Spell_Nature_Sleep",
  ["Melee Specialization"] = "INV_Axe_02",
  ["Melt Armor"] = "Spell_Fire_Immolation",
  ["Mend Pet"] = "Ability_Hunter_MendPet",
  ["Mental Agility"] = "Ability_Hibernation",
  ["Mental Strength"] = "Spell_Nature_EnchantArmor",
  ["Mind Blast"] = "Spell_Shadow_UnholyFrenzy",
  ["Mind Control"] = "Spell_Shadow_ShadowWordDominate",
  ["Mind Flay"] = "Spell_Shadow_SiphonMana",
  ["Mind Mastery"] = "Spell_Arcane_MindMastery",
  ["Mind Soothe"] = "Spell_Holy_MindSooth",
  ["Mind Vision"] = "Spell_Holy_MindVision",
  ["Mind-numbing Poison"] = "Spell_Nature_NullifyDisease",
  ["Mind-numbing Poison II"] = "Spell_Nature_NullifyDisease",
  ["Mind-numbing Poison III"] = "Spell_Nature_NullifyDisease",
  ["Mining"] = "Spell_Fire_FlameBlades",
  ["Misdirection"] = "Ability_Hunter_Misdirection",
  ["Misery"] = "Spell_Shadow_Misery",
  ["Mocking Blow"] = "Ability_Warrior_PunishingBlow",
  ["Molten Armor"] = "Ability_Mage_MoltenArmor",
  ["Molten Fury"] = "Spell_Fire_MoltenBlood",
  ["Mongoose Bite"] = "Ability_Hunter_SwiftStrike",
  ["Monster Slaying"] = "INV_Misc_Head_Dragon_Black",
  ["Moonfire"] = "Spell_Nature_StarFall",
  ["Moonfury"] = "Spell_Nature_MoonGlow",
  ["Moonglow"] = "Spell_Nature_Sentinal",
  ["Moonkin Aura"] = "Spell_Nature_MoonGlow",
  ["Moonkin Form"] = "Spell_Nature_ForceOfNature",
  ["Mortal Shots"] = "Ability_PierceDamage",
  ["Mortal Strike"] = "Ability_Warrior_SavageBlow",
  ["Multi-Shot"] = "Ability_UpgradeMoonGlaive",
  ["Murder"] = "Spell_Shadow_DeathScream",
  ["Mutilate"] = "Ability_Rogue_ShadowStrikes",
  ["Natural Armor"] = "Spell_Nature_SpiritArmor",
  ["Natural Perfection"] = "Ability_Druid_NaturalPerfection",
  ["Natural Shapeshifter"] = "Spell_Nature_WispSplode",
  ["Naturalist"] = "Spell_Nature_HealingTouch",
  ["Nature Resistance"] = "Spell_Nature_ResistNature",
  ["Nature Resistance Totem"] = "Spell_Nature_NatureResistanceTotem",
  ["Nature Weakness"] = "INV_Misc_QirajiCrystal_03",
  ["Nature's Focus"] = "Spell_Nature_HealingWaveGreater",
  ["Nature's Grace"] = "Spell_Nature_NaturesBlessing",
  ["Nature's Grasp"] = "Spell_Nature_NaturesWrath",
  ["Nature's Guidance"] = "Spell_Frost_Stun",
  ["Nature's Reach"] = "Spell_Nature_NatureTouchGrow",
  ["Nature's Swiftness"] = "Spell_Nature_RavenForm",
  ["Negative Charge"] = "Spell_ChargeNegative",
  ["Nether Protection"] = "Spell_Shadow_NetherProtection",
  ["Netherstorm Flag"] = "INV_Misc_Summerfest_BrazierGreen",
  ["Nightfall"] = "Spell_Shadow_Twilight",
  ["Nurturing Instinct"] = "Ability_Druid_HealingInstincts",
  ["Omen of Clarity"] = "Spell_Nature_CrystalBall",
  ["One-Handed Axes"] = "INV_Axe_01",
  ["One-Handed Maces"] = "INV_Mace_01",
  ["One-Handed Swords"] = "Ability_MeleeDamage",
  ["One-Handed Weapon Specialization"] = "INV_Sword_20",
  ["Opening"] = "Temp",
  ["Opening - No Text"] = "Temp",
  ["Opportunity"] = "Ability_Warrior_WarCry",
  ["Overpower"] = "Ability_MeleeDamage",
  ["Pain Suppression"] = "Spell_Holy_PainSupression",
  ["Panic"] = "INV_Misc_Drum_06",
  ["Paranoia"] = "Spell_Shadow_AuraOfDarkness",
  ["Parasitic Shadowfiend"] = "Spell_Shadow_SoulLeech_3",
  ["Parry"] = "Ability_Parry",
  ["Pathfinding"] = "Ability_Mount_JungleTiger",
  ["Perceived Weakness"] = "Spell_Holy_ArcaneIntellect",
  ["Perception"] = "Spell_Nature_Sleep",
  ["Permafrost"] = "Spell_Frost_Wisp",
  ["Pet Aggression"] = "Ability_Druid_Maul",
  ["Pet Hardiness"] = "Ability_BullRush",
  ["Pet Recovery"] = "Ability_Hibernation",
  ["Pet Resistance"] = "Spell_Holy_BlessingOfAgility",
  ["Phase Shift"] = "Spell_Shadow_ImpPhaseShift",
  ["Pick Lock"] = "Spell_Nature_MoonKey",
  ["Pick Pocket"] = "INV_Misc_Bag_11",
  ["Piercing Howl"] = "Spell_Shadow_DeathScream",
  ["Piercing Ice"] = "Spell_Frost_Frostbolt",
  ["Plate Mail"] = "INV_Chest_Plate01",
  ["Playing with Fire"] = "Spell_Fire_PlayingWithFire",
  ["Poison Cleansing Totem"] = "Spell_Nature_PoisonCleansingTotem",
  ["Poison Spit"] = "Spell_Nature_CorrosiveBreath",
  ["Poisons"] = "Trade_BrewPoison",
  ["Polearms"] = "INV_Spear_06",
  ["Poleaxe Specialization"] = "INV_Weapon_Halbard_01",
  ["Polymorph"] = "Spell_Nature_Polymorph",
  ["Polymorph: Pig"] = "Spell_Magic_PolymorphPig",
  ["Polymorph: Turtle"] = "Ability_Hunter_Pet_Turtle",
  ["Portal: Darnassus"] = "Spell_Arcane_PortalDarnassus",
  ["Portal: Exodar"] = "Spell_Arcane_PortalExodar",
  ["Portal: Ironforge"] = "Spell_Arcane_PortalIronForge",
  ["Portal: Orgrimmar"] = "Spell_Arcane_PortalOrgrimmar",
  ["Portal: Shattrath"] = "Spell_Arcane_PortalShattrath",
  ["Portal: Silvermoon"] = "Spell_Arcane_PortalSilvermoon",
  ["Portal: Stormwind"] = "Spell_Arcane_PortalStormWind",
  ["Portal: Thunder Bluff"] = "Spell_Arcane_PortalThunderBluff",
  ["Portal: Undercity"] = "Spell_Arcane_PortalUnderCity",
  ["Positive Charge"] = "Spell_ChargePositive",
  ["Pounce"] = "Ability_Druid_SupriseAttack",
  ["Pounce Bleed"] = "Ability_Druid_SupriseAttack",
  ["Power Infusion"] = "Spell_Holy_PowerInfusion",
  ["Power Word: Fortitude"] = "Spell_Holy_WordFortitude",
  ["Power Word: Shield"] = "Spell_Holy_PowerWordShield",
  ["Prayer of Fortitude"] = "Spell_Holy_PrayerOfFortitude",
  ["Prayer of Healing"] = "Spell_Holy_PrayerOfHealing02",
  ["Prayer of Mending"] = "Spell_Holy_PrayerOfMendingtga",
  ["Prayer of Shadow Protection"] = "Spell_Holy_PrayerofShadowProtection",
  ["Prayer of Spirit"] = "Spell_Holy_PrayerofSpirit",
  ["Precision"] = "Ability_Marksmanship",
  ["Predatory Instincts"] = "Ability_Druid_PredatoryInstincts",
  ["Predatory Strikes"] = "Ability_Hunter_Pet_Cat",
  ["Premeditation"] = "Spell_Shadow_Possession",
  ["Preparation"] = "Spell_Shadow_AntiShadow",
  ["Presence of Mind"] = "Spell_Nature_EnchantArmor",
  ["Primal Fury"] = "Ability_Racial_Cannibalize",
  ["Prismatic Cloak"] = "Spell_Arcane_PrismaticCloak",
  ["Prospecting"] = "INV_Misc_Gem_Bloodstone_01",
  ["Prowl"] = "Ability_Druid_SupriseAttack",
  ["Psychic Scream"] = "Spell_Shadow_PsychicScream",
  ["Pummel"] = "INV_Gauntlets_04",
  ["Purge"] = "Spell_Nature_Purge",
  ["Purification"] = "Spell_Frost_WizardMark",
  ["Purify"] = "Spell_Holy_Purify",
  ["Purifying Power"] = "Spell_Holy_PurifyingPower",
  ["Pursuit of Justice"] = "Spell_Holy_PersuitofJustice",
  ["Pyroblast"] = "Spell_Fire_Fireball02",
  ["Pyroclasm"] = "Spell_Fire_Volcano",
  ["Pyromaniac"] = "Spell_Fire_Burnout",
  ["Quick Recovery"] = "Ability_Rogue_QuickRecovery",
  ["Quick Shots"] = "Ability_Warrior_InnerRage",
  ["Quickness"] = "Ability_Racial_ShadowMeld",
  ["Rage of the Unraveller"] = "Racial_Orc_BerserkerStrength",
  ["Rain of Fire"] = "Spell_Shadow_RainOfFire",
  ["Rake"] = "Ability_Druid_Disembowel",
  ["Ram Riding"] = "Spell_Nature_Swiftness",
  ["Rampage"] = "Ability_Warrior_Rampage",
  ["Ranged Weapon Specialization"] = "INV_Weapon_Rifle_06",
  ["Rapid Fire"] = "Ability_Hunter_RunningShot",
  ["Rapid Killing"] = "Ability_Hunter_RapidKilling",
  ["Raptor Riding"] = "Spell_Nature_Swiftness",
  ["Raptor Strike"] = "Ability_MeleeDamage",
  ["Ravage"] = "Ability_Druid_Ravage",
  ["Readiness"] = "Ability_Hunter_Readiness",
  ["Rebirth"] = "Spell_Nature_Reincarnation",
  ["Reckless Charge"] = "Spell_Nature_AstralRecal",
  ["Recklessness"] = "Ability_CriticalStrike",
  ["Reckoning"] = "Spell_Holy_BlessingOfStrength",
  ["Redemption"] = "Spell_Holy_Resurrection",
  ["Redoubt"] = "Ability_Defend",
  ["Reflection"] = "Spell_Frost_WindWalkOn",
  ["Regeneration"] = "Spell_Nature_Regenerate",
  ["Regrowth"] = "Spell_Nature_ResistNature",
  ["Reincarnation"] = "Spell_Nature_Reincarnation",
  ["Reinforced Shield"] = "INV_Shield_06",
  ["Rejuvenation"] = "Spell_Nature_Rejuvenation",
  ["Relentless Strikes"] = "Ability_Warrior_DecisiveStrike",
  ["Remorseless"] = "Ability_FiegnDead",
  ["Remorseless Attacks"] = "Ability_FiegnDead",
  ["Remote Toy"] = "INV_Misc_Urn_01",
  ["Remove Curse"] = "Spell_Holy_RemoveCurse",
  ["Remove Insignia"] = "Temp",
  ["Remove Lesser Curse"] = "Spell_Nature_RemoveCurse",
  ["Rend"] = "Ability_Gouge",
  ["Renew"] = "Spell_Holy_Renew",
  ["Repentance"] = "Spell_Holy_PrayerOfHealing",
  ["Restless Strength"] = "Ability_CriticalStrike",
  ["Restorative Totems"] = "Spell_Nature_ManaRegenTotem",
  ["Resurrection"] = "Spell_Holy_Resurrection",
  ["Retaliation"] = "Ability_Warrior_Challange",
  ["Retribution Aura"] = "Spell_Holy_AuraOfLight",
  ["Revenge"] = "Ability_Warrior_Revenge",
  ["Revenge Stun"] = "Ability_Warrior_Revenge",
  ["Reverberation"] = "Spell_Frost_FrostWard",
  ["Revive Pet"] = "Ability_Hunter_BeastSoothe",
  ["Righteous Defense"] = "INV_Shoulder_37",
  ["Righteous Fury"] = "Spell_Holy_SealOfFury",
  ["Rip"] = "Ability_GhoulFrenzy",
  ["Riposte"] = "Ability_Warrior_Challange",
  ["Ritual of Doom"] = "Spell_Shadow_AntiMagicShell",
  ["Ritual of Doom Effect"] = "Spell_Arcane_PortalDarnassus",
  ["Ritual of Refreshment"] = "Spell_Arcane_MassDispel",
  ["Ritual of Souls"] = "Spell_Shadow_Shadesofdarkness",
  ["Ritual of Summoning"] = "Spell_Shadow_Twilight",
  ["Rockbiter Weapon"] = "Spell_Nature_RockBiter",
  ["Rough Sharpening Stone"] = "INV_Stone_SharpeningStone_01",
  ["Ruin"] = "Spell_Shadow_ShadowWordPain",
  ["Rupture"] = "Ability_Rogue_Rupture",
  ["Ruthlessness"] = "Ability_Druid_Disembowel",
  ["Sacrifice"] = "Spell_Shadow_SacrificialShield",
  ["Safe Fall"] = "INV_Feather_01",
  ["Sanctified Crusader"] = "Spell_Holy_HolySmite",
  ["Sanctified Judgement"] = "Spell_Holy_RighteousFury",
  ["Sanctified Light"] = "Spell_Holy_HealingAura",
  ["Sanctity Aura"] = "Spell_Holy_MindVision",
  ["Sap"] = "Ability_Sap",
  ["Savage Fury"] = "Ability_Druid_Ravage",
  ["Savage Strikes"] = "Ability_Racial_BloodRage",
  ["Scare Beast"] = "Ability_Druid_Cower",
  ["Scatter Shot"] = "Ability_GolemStormBolt",
  ["Scorch"] = "Spell_Fire_SoulBurn",
  ["Scorpid Poison"] = "Ability_PoisonSting",
  ["Scorpid Sting"] = "Ability_Hunter_CriticalShot",
  ["Screech"] = "Ability_Hunter_Pet_Bat",
  ["Seal Fate"] = "Spell_Shadow_ChillTouch",
  ["Seal of Blood"] = "Spell_Holy_SealOfBlood",
  ["Seal of Command"] = "Ability_Warrior_InnerRage",
  ["Seal of Justice"] = "Spell_Holy_SealOfWrath",
  ["Seal of Light"] = "Spell_Holy_HealingAura",
  ["Seal of Righteousness"] = "Ability_ThunderBolt",
  ["Seal of Vengeance"] = "Spell_Holy_SealOfVengeance",
  ["Seal of Wisdom"] = "Spell_Holy_RighteousnessAura",
  ["Seal of the Crusader"] = "Spell_Holy_HolySmite",
  ["Searing Light"] = "Spell_Holy_SearingLightPriest",
  ["Searing Pain"] = "Spell_Fire_SoulBurn",
  ["Searing Totem"] = "Spell_Fire_SearingTotem",
  ["Second Wind"] = "Ability_Hunter_Harass",
  ["Seduction"] = "Spell_Shadow_MindSteal",
  ["Seed of Corruption"] = "Spell_Shadow_SeedOfDestruction",
  ["Seethe"] = "Ability_Druid_ChallangingRoar",
  ["Sense Demons"] = "Spell_Shadow_Metamorphosis",
  ["Sense Undead"] = "Spell_Holy_SenseUndead",
  ["Sentry Totem"] = "Spell_Nature_RemoveCurse",
  ["Serpent Sting"] = "Ability_Hunter_Quickshot",
  ["Serrated Blades"] = "INV_Sword_17",
  ["Setup"] = "Spell_Nature_MirrorImage",
  ["Shackle Undead"] = "Spell_Nature_Slow",
  ["Shadow Affinity"] = "Spell_Shadow_ShadowWard",
  ["Shadow Bolt"] = "Spell_Shadow_ShadowBolt",
  ["Shadow Embrace"] = "Spell_Shadow_ShadowEmbrace",
  ["Shadow Focus"] = "Spell_Shadow_BurningSpirit",
  ["Shadow Mastery"] = "Spell_Shadow_ShadeTrueSight",
  ["Shadow Power"] = "Spell_Shadow_ShadowPower",
  ["Shadow Protection"] = "Spell_Shadow_AntiShadow",
  ["Shadow Reach"] = "Spell_Shadow_ChillTouch",
  ["Shadow Resistance"] = "Spell_Shadow_AntiShadow",
  ["Shadow Resistance Aura"] = "Spell_Shadow_SealOfKings",
  ["Shadow Trance"] = "Spell_Shadow_Twilight",
  ["Shadow Vulnerability"] = "Spell_Shadow_ShadowBolt",
  ["Shadow Ward"] = "Spell_Shadow_AntiShadow",
  ["Shadow Weakness"] = "INV_Misc_QirajiCrystal_05",
  ["Shadow Weaving"] = "Spell_Shadow_BlackPlague",
  ["Shadow Word: Death"] = "Spell_Shadow_DemonicFortitude",
  ["Shadow Word: Pain"] = "Spell_Shadow_ShadowWordPain",
  ["Shadow and Flame"] = "Spell_Shadow_ShadowandFlame",
  ["Shadow of Death"] = "Spell_Arcane_PrismaticCloak",
  ["Shadowburn"] = "Spell_Shadow_ScourgeBuild",
  ["Shadowfiend"] = "Spell_Shadow_Shadowfiend",
  ["Shadowform"] = "Spell_Shadow_Shadowform",
  ["Shadowfury"] = "Spell_Shadow_Shadowfury",
  ["Shadowguard"] = "Spell_Nature_LightningShield",
  ["Shadowmeld"] = "Ability_Ambush",
  ["Shadowmeld Passive"] = "Ability_Ambush",
  ["Shadowstep"] = "Ability_Rogue_Shadowstep",
  ["Shamanistic Rage"] = "Spell_Nature_ShamanRage",
  ["Sharpened Claws"] = "INV_Misc_MonsterClaw_04",
  ["Shatter"] = "Spell_Frost_FrostShock",
  ["Shear"] = "Spell_Shadow_FocusedPower",
  ["Shell Shield"] = "Ability_Hunter_Pet_Turtle",
  ["Shield"] = "INV_Shield_04",
  ["Shield Bash"] = "Ability_Warrior_ShieldBash",
  ["Shield Bash - Silenced"] = "Ability_Warrior_ShieldBash",
  ["Shield Block"] = "Ability_Defend",
  ["Shield Slam"] = "INV_Shield_05",
  ["Shield Specialization"] = "INV_Shield_06",
  ["Shield Wall"] = "Ability_Warrior_ShieldWall",
  ["Shiv"] = "INV_ThrowingKnife_04",
  ["Shoot"] = "Ability_ShootWand",
  ["Shoot Bow"] = "Ability_Marksmanship",
  ["Shoot Gun"] = "Ability_Marksmanship",
  ["Shred"] = "Spell_Shadow_VampiricAura",
  ["Shredding Attacks"] = "Spell_Shadow_VampiricAura",
  ["Silence"] = "Spell_Shadow_ImpPhaseShift",
  ["Silencing Shot"] = "Ability_TheBlackArrow",
  ["Silent Resolve"] = "Spell_Nature_ManaRegenTotem",
  ["Silverwing Flag"] = "INV_BannerPVP_02",
  ["Sinister Strike"] = "Spell_Shadow_RitualOfSacrifice",
  ["Siphon Life"] = "Spell_Shadow_Requiem",
  ["Skinning"] = "INV_Misc_Pelt_Wolf_01",
  ["Slam"] = "Ability_Warrior_DecisiveStrike",
  ["Slayer's Crest"] = "INV_Trinket_Naxxramas03",
  ["Sleep"] = "Spell_Nature_Sleep",
  ["Slice and Dice"] = "Ability_Rogue_SliceDice",
  ["Slow"] = "Spell_Nature_Slow",
  ["Slow Fall"] = "Spell_Magic_FeatherFall",
  ["Smelting"] = "Spell_Fire_FlameBlades",
  ["Smite"] = "Spell_Holy_HolySmite",
  ["Snake Trap"] = "Ability_Hunter_SnakeTrap",
  ["Solid Sharpening Stone"] = "INV_Stone_SharpeningStone_04",
  ["Sonic Burst"] = "Spell_Shadow_Teleport",
  ["Soothe Animal"] = "Ability_Hunter_BeastSoothe",
  ["Soothing Kiss"] = "Spell_Shadow_SoothingKiss",
  ["Soul Fire"] = "Spell_Fire_Fireball02",
  ["Soul Leech"] = "Spell_Shadow_SoulLeech_3",
  ["Soul Link"] = "Spell_Shadow_GatherShadows",
  ["Soul Siphon"] = "Spell_Shadow_LifeDrain02",
  ["Soulshatter"] = "Spell_Arcane_Arcane01",
  ["Soulstone Resurrection"] = "INV_Misc_Orb_04",
  ["Spell Haste"] = "Spell_Holy_SearingLight",
  ["Spell Lock"] = "Spell_Shadow_MindRot",
  ["Spell Power"] = "Spell_Arcane_ArcaneTorrent",
  ["Spell Reflection"] = "Ability_Warrior_ShieldReflection",
  ["Spell Vulnerability"] = "Spell_Holy_ElunesGrace",
  ["Spell Warding"] = "Spell_Holy_SpellWarding",
  ["Spellsteal"] = "Spell_Arcane_Arcane02",
  ["Spirit Bond"] = "Ability_Druid_DemoralizingRoar",
  ["Spirit Tap"] = "Spell_Shadow_Requiem",
  ["Spirit of Redemption"] = "INV_Enchant_EssenceEternalLarge",
  ["Spiritual Attunement"] = "Spell_Holy_ReviveChampion",
  ["Spiritual Focus"] = "Spell_Arcane_Blink",
  ["Spiritual Guidance"] = "Spell_Holy_SpiritualGuidence",
  ["Spiritual Healing"] = "Spell_Nature_MoonGlow",
  ["Spiteful Fury"] = "Ability_Warrior_Rampage",
  ["Sprint"] = "Ability_Rogue_Sprint",
  ["Stance Mastery"] = "Spell_Nature_EnchantArmor",
  ["Starfire"] = "Spell_Arcane_StarFire",
  ["Starfire Stun"] = "Spell_Arcane_StarFire",
  ["Starlight Wrath"] = "Spell_Nature_AbolishMagic",
  ["Starshards"] = "Spell_Arcane_StarFire",
  ["Static Charge"] = "Spell_Nature_WispSplode",
  ["Staves"] = "INV_Staff_08",
  ["Steady Shot"] = "Ability_Hunter_SteadyShot",
  ["Stealth"] = "Ability_Stealth",
  ["Stoneclaw Stun"] = "Spell_Nature_StoneClawTotem",
  ["Stoneclaw Totem"] = "Spell_Nature_StoneClawTotem",
  ["Stoneform"] = "Spell_Shadow_UnholyStrength",
  ["Stoneskin"] = "Spell_Nature_StoneSkinTotem",
  ["Stoneskin Totem"] = "Spell_Nature_StoneSkinTotem",
  ["Stormstrike"] = "Spell_Holy_SealOfMight",
  ["Strength of Earth"] = "Spell_Nature_EarthBindTotem",
  ["Strength of Earth Totem"] = "Spell_Nature_EarthBindTotem",
  ["Strength of the Halaani"] = "INV_Trinket_Naxxramas01",
  ["Stuck"] = "Spell_Shadow_Teleport",
  ["Stun"] = "Spell_Frost_Stun",
  ["Subtlety"] = "Ability_EyeOfTheOwl",
  ["Suffering"] = "Spell_Shadow_BlackPlague",
  ["Summon Charger"] = "Ability_Mount_Charger",
  ["Summon Dreadsteed"] = "Ability_Mount_Dreadsteed",
  ["Summon Felguard"] = "Spell_Shadow_SummonFelGuard",
  ["Summon Felhunter"] = "Spell_Shadow_SummonFelHunter",
  ["Summon Felsteed"] = "Spell_Nature_Swiftness",
  ["Summon Imp"] = "Spell_Shadow_SummonImp",
  ["Summon Succubus"] = "Spell_Shadow_SummonSuccubus",
  ["Summon Voidwalker"] = "Spell_Shadow_SummonVoidWalker",
  ["Summon Warhorse"] = "Spell_Nature_Swiftness",
  ["Summon Water Elemental"] = "Spell_Frost_SummonWaterElemental_2",
  ["Sunder Armor"] = "Ability_Warrior_Sunder",
  ["Suppression"] = "Spell_Shadow_UnsummonBuilding",
  ["Surefooted"] = "Ability_Kick",
  ["Surge of Light"] = "Spell_Holy_SurgeOfLight",
  ["Surprise Attacks"] = "Ability_Rogue_SurpriseAttack",
  ["Survivalist"] = "Spell_Shadow_Twilight",
  ["Sweeping Strikes"] = "Ability_Rogue_SliceDice",
  ["Swift Flight Form"] = "Ability_Druid_FlightForm",
  ["Swiftmend"] = "INV_Relics_IdolofRejuvenation",
  ["Swipe"] = "INV_Misc_MonsterClaw_03",
  ["Sword Specialization"] = "INV_Sword_27",
  ["Symbol of Hope"] = "Spell_Holy_SymbolOfHope",
  ["Tactical Mastery"] = "Spell_Nature_EnchantArmor",
  ["Tailoring"] = "Trade_Tailoring",
  ["Tainted Blood"] = "Spell_Shadow_LifeDrain",
  ["Tainted Mind"] = "Spell_Shadow_ShadowPact",
  ["Tame Beast"] = "Ability_Hunter_BeastTaming",
  ["Taunt"] = "Spell_Nature_Reincarnation",
  ["Teleport: Darnassus"] = "Spell_Arcane_TeleportDarnassus",
  ["Teleport: Exodar"] = "Spell_Arcane_TeleportExodar",
  ["Teleport: Ironforge"] = "Spell_Arcane_TeleportIronForge",
  ["Teleport: Moonglade"] = "Spell_Arcane_TeleportMoonglade",
  ["Teleport: Orgrimmar"] = "Spell_Arcane_TeleportOrgrimmar",
  ["Teleport: Shattrath"] = "Spell_Arcane_TeleportShattrath",
  ["Teleport: Silvermoon"] = "Spell_Arcane_TeleportSilvermoon",
  ["Teleport: Stormwind"] = "Spell_Arcane_TeleportStormWind",
  ["Teleport: Thunder Bluff"] = "Spell_Arcane_TeleportThunderBluff",
  ["Teleport: Undercity"] = "Spell_Arcane_TeleportUnderCity",
  ["Temporal Rift"] = "Spell_Nature_TimeStop",
  ["The Beast Within"] = "Ability_Hunter_BeastWithin",
  ["The Human Spirit"] = "INV_Enchant_ShardBrilliantSmall",
  ["Thick Hide"] = "INV_Misc_Pelt_Bear_03",
  ["Thorns"] = "Spell_Nature_Thorns",
  ["Thrill of the Hunt"] = "Ability_Hunter_ThrilloftheHunt",
  ["Throw"] = "Ability_Throw",
  ["Throwing Specialization"] = "INV_ThrowingAxe_03",
  ["Thrown"] = "INV_ThrowingKnife_02",
  ["Thunder Clap"] = "Spell_Nature_ThunderClap",
  ["Thunderfury"] = "Spell_Nature_Cyclone",
  ["Thundering Strikes"] = "Ability_ThunderBolt",
  ["Thunderstomp"] = "Ability_Hunter_Pet_Gorilla",
  ["Tidal Focus"] = "Spell_Frost_ManaRecharge",
  ["Tidal Mastery"] = "Spell_Nature_Tranquility",
  ["Tiger Riding"] = "Spell_Nature_Swiftness",
  ["Tiger's Fury"] = "Ability_Mount_JungleTiger",
  ["Time's Favor"] = "Ability_Rogue_FleetFooted",
  ["Torment"] = "Spell_Shadow_GatherShadows",
  ["Totem"] = "Spell_Nature_StoneClawTotem",
  ["Totem of Wrath"] = "Spell_Fire_TotemOfWrath",
  ["Totemic Call"] = "Spell_unused",
  ["Totemic Focus"] = "Spell_Nature_MoonGlow",
  ["Touch of Shadow"] = "Spell_Shadow_PsychicScream",
  ["Touch of Weakness"] = "Spell_Shadow_DeadofNight",
  ["Toughness"] = "Spell_Holy_Devotion",
  ["Traces of Silithyst"] = "Spell_Nature_TimeStop",
  ["Track Beasts"] = "Ability_Tracking",
  ["Track Demons"] = "Spell_Shadow_SummonFelHunter",
  ["Track Dragonkin"] = "INV_Misc_Head_Dragon_01",
  ["Track Elementals"] = "Spell_Frost_SummonWaterElemental",
  ["Track Giants"] = "Ability_Racial_Avatar",
  ["Track Hidden"] = "Ability_Stealth",
  ["Track Humanoids"] = "Spell_Holy_PrayerOfHealing",
  ["Track Undead"] = "Spell_Shadow_DarkSummoning",
  ["Tranquil Air"] = "Spell_Nature_Brilliance",
  ["Tranquil Air Totem"] = "Spell_Nature_Brilliance",
  ["Tranquil Spirit"] = "Spell_Holy_ElunesGrace",
  ["Tranquility"] = "Spell_Nature_Tranquility",
  ["Tranquilizing Shot"] = "Spell_Nature_Drowsy",
  ["Trap Mastery"] = "Ability_Ensnare",
  ["Travel Form"] = "Ability_Druid_TravelForm",
  ["Tree of Life"] = "Ability_Druid_TreeofLife",
  ["Tremor Totem"] = "Spell_Nature_TremorTotem",
  ["Tribal Leatherworking"] = "Spell_Nature_NullWard",
  ["Trueshot Aura"] = "Ability_TrueShot",
  ["Turn Undead"] = "Spell_Holy_TurnUndead",
  ["Twin Spire Blessing"] = "Spell_Nature_ElementalPrecision_1",
  ["Two-Handed Axes"] = "INV_Axe_04",
  ["Two-Handed Axes and Maces"] = "INV_Axe_10",
  ["Two-Handed Maces"] = "INV_Mace_04",
  ["Two-Handed Swords"] = "Ability_MeleeDamage",
  ["Two-Handed Weapon Specialization"] = "INV_Axe_09",
  ["Unarmed"] = "Ability_GolemThunderClap",
  ["Unbreakable Will"] = "Spell_Magic_MageArmor",
  ["Unbridled Wrath"] = "Spell_Nature_StoneClawTotem",
  ["Undead Horsemanship"] = "Spell_Nature_Swiftness",
  ["Underwater Breathing"] = "Spell_Shadow_DemonBreath",
  ["Unending Breath"] = "Spell_Shadow_DemonBreath",
  ["Unholy Power"] = "Spell_Shadow_ShadowWordDominate",
  ["Unleashed Fury"] = "Ability_BullRush",
  ["Unleashed Rage"] = "Spell_Nature_UnleashedRage",
  ["Unstable Affliction"] = "Spell_Shadow_UnstableAffliction_3",
  ["Unstable Power"] = "Spell_Lightning_LightningBolt01",
  ["Unyielding Faith"] = "Spell_Holy_UnyieldingFaith",
  ["Upset Tummy"] = "Ability_Hunter_Pet_Boar",
  ["Vampiric Embrace"] = "Spell_Shadow_UnsummonBuilding",
  ["Vampiric Touch"] = "Spell_Holy_Stoicism",
  ["Vanish"] = "Ability_Vanish",
  ["Vanished"] = "Ability_Vanish",
  ["Vengeance"] = "Spell_Nature_Purge",
  ["Venomous Totem"] = "Spell_Totem_WardOfDraining",
  ["Victory Rush"] = "Ability_Warrior_Devastate",
  ["Vigor"] = "Spell_Nature_EarthBindTotem",
  ["Vile Beam"] = "Spell_Shadow_ShadowBolt",
  ["Vile Poisons"] = "Ability_Rogue_FeignDeath",
  ["Vindication"] = "Spell_Holy_Vindication",
  ["Viper Sting"] = "Ability_Hunter_AimedShot",
  ["Volley"] = "Ability_Marksmanship",
  ["Wand Specialization"] = "INV_Wand_01",
  ["Wands"] = "Ability_ShootWand",
  ["War Stomp"] = "Ability_WarStomp",
  ["Warp"] = "Spell_Arcane_Arcane04",
  ["Warsong Flag"] = "INV_BannerPVP_01",
  ["Water Breathing"] = "Spell_Shadow_DemonBreath",
  ["Water Shield"] = "Ability_Shaman_WaterShield",
  ["Water Tomb"] = "Spell_Frost_ManaRecharge",
  ["Water Walking"] = "Spell_Frost_WindWalkOn",
  ["Waterbolt"] = "Spell_Frost_FrostBolt",
  ["Watery Grave"] = "Spell_Shadow_DemonBreath",
  ["Weakened Soul"] = "Spell_Holy_AshesToAshes",
  ["Weapon Mastery"] = "Ability_Warrior_WeaponMastery",
  ["Weaponsmith"] = "INV_Sword_25",
  ["Whirlwind"] = "Ability_Whirlwind",
  ["Will of the Forsaken"] = "Spell_Shadow_RaiseDead",
  ["Windfury"] = "Spell_Nature_Cyclone",
  ["Windfury Attack"] = "Spell_Nature_Cyclone",
  ["Windfury Totem"] = "Spell_Nature_Windfury",
  ["Windfury Weapon"] = "Spell_Nature_Cyclone",
  ["Windwall"] = "Spell_Nature_EarthBind",
  ["Windwall Totem"] = "Spell_Nature_EarthBind",
  ["Wing Clip"] = "Ability_Rogue_Trip",
  ["Winter's Chill"] = "Spell_Frost_ChillingBlast",
  ["Wisp Spirit"] = "Spell_Nature_WispSplode",
  ["Wolf Riding"] = "Spell_Nature_Swiftness",
  ["Wound Poison"] = "INV_Misc_Herb_16",
  ["Wound Poison II"] = "INV_Misc_Herb_16",
  ["Wound Poison III"] = "INV_Misc_Herb_16",
  ["Wound Poison IV"] = "INV_Misc_Herb_16",
  ["Wound Poison V"] = "INV_Misc_Herb_16",
  ["Wrath"] = "Spell_Nature_AbolishMagic",
  ["Wrath of Air Totem"] = "Spell_Nature_SlowingTotem",
  ["Wrath of Cenarius"] = "Ability_Druid_TwilightsWrath",
  ["Wrath of the Astromancer"] = "Spell_Arcane_Arcane02",
  ["Wyvern Sting"] = "INV_Spear_02",
}
