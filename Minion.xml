<Ui xmlns="http://www.blizzard.com/wow/ui/" 
xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" 
xsi:schemaLocation="http://www.blizzard.com/wow/ui/
C:\Projects\WoW\Bin\Interface\FrameXML\UI.xsd">
	
	<Frame name="LazyScriptMinionFrame" hidden="true" frameStrata="MEDIUM" toplevel="true" 
	enableMouse="true" movable="true" parent="UIParent">
		<Scripts>
			<OnLoad>
				lazyScript.metadata:updateRevisionFromKeyword("$Revision: 527 $")
				this:RegisterForDrag("LeftButton")
			</OnLoad>
			<OnUpdate>
				lazyScript.minion.OnUpdate()
			</OnUpdate>
			<OnDragStart>
				if (IsShiftKeyDown()) then
				this:StartMoving()
				end
			</OnDragStart>
			<OnDragStop>
				this:StopMovingOrSizing()
			</OnDragStop>
			<OnMouseUp>
				this:StopMovingOrSizing()
			</OnMouseUp>
			<OnEnter>
				lazyScript.minion.OnEnter(this)
			</OnEnter>
			<OnLeave>
				lazyScript.minion.OnLeave(this)
			</OnLeave>
		</Scripts>
		
		<Size>
			<AbsDimension x="200" y="32"/>
		</Size>
		<Anchors>
			<Anchor point="TOP">
				<Offset>
					<AbsDimension x="0" y="-50"/>
				</Offset>
			</Anchor>
		</Anchors>
		
		<Backdrop name="$parentBackdrop" bgFile="Interface\TutorialFrame\TutorialFrameBackground"
		edgeFile="Interface\DialogFrame\UI-DialogBox-Border" tile="true">
			<EdgeSize>
				<AbsValue val="16"/>
			</EdgeSize>
			<TileSize>
				<AbsValue val="32"/>
			</TileSize>
			<BackgroundInsets>
				<AbsInset left="5" right="5" top="5" bottom="5"/>
			</BackgroundInsets>
		</Backdrop>
		
		<Layers>
			<Layer level="BACKGROUND">
				<FontString name="LazyScriptMinionText" inherits="GameFontNormal">
					<Size>
						<AbsDimension x="190" y="12"/>
					</Size>
					<Anchors>
						<Anchor point="TOP">
							<Offset>
								<AbsDimension x="0" y="-9"/>
							</Offset>
						</Anchor>
					</Anchors>
				</FontString>
			</Layer>
		</Layers>
	</Frame>
	
</Ui>